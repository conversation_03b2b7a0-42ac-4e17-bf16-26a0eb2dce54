# Comprehensive Technical Documentation: Truck System (Unabridged)

This document is a complete and unabridged compilation of the technical analysis performed on the truck system. It is composed of the direct outputs from four separate analysis tasks.

---

# Part 1: Truck Pathfinding System

## 1.1. Overview

The Truck Pathfinding System is a core gameplay mechanic responsible for the movement and navigation of truck entities. It is a player-driven system where routes are manually drawn by the user, rather than being determined by an automated pathfinding algorithm like A*. The system's complexity lies in its real-time path validation, waypoint following, and automated sequences for parking and exiting the level.

The primary files governing this system are:
*   [`lib/game/entities/objects/truck.js`](lib/game/entities/objects/truck.js): The main truck entity containing all movement, input, and state logic.
*   [`lib/game/entities/path-head.js`](lib/game/entities/path-head.js): A logical entity used for real-time collision detection of the drawn path.
*   [`lib/game/entities/objects/parking-slot.js`](lib/game/entities/objects/parking-slot.js): Defines the destination points for trucks.
*   [`lib/game/levels/*.js`](lib/game/levels/level1.js): Level files that define the physical environment, including collision boundaries, parking slot locations, and exit zones.

---

## 1.2. Truck Navigation and Route Drawing

Truck navigation is initiated and controlled directly by the player through a click-and-drag interface.

### 1.2.1. Path Creation

1.  **Initiation**: The player clicks on a truck entity. This action is detected in [`truck.js`](lib/game/entities/objects/truck.js) by the `handleInput` function, which calls `onClicked()`.
2.  **Path Head**: A logical `EntityPathHead` is spawned at the cursor's position. This entity has a circular collision shape whose radius (`pathHeadRadius`) is based on the truck's width, effectively representing the truck's physical footprint as the path is drawn.
3.  **Drawing**: As the player drags the cursor, the `clicking()` function continuously updates the `pathHead`'s position and adds coordinates to the truck's `trajectory` array.
    *   Points are added only if the distance between them exceeds a minimum threshold, preventing an excessive number of waypoints.
    *   To ensure path smoothness, the system interpolates and adds extra points if the cursor moves a large distance between frames.
4.  **Path Validation**: The system performs two crucial validation checks in real-time:
    *   **Collision Detection**: The `checkPathHeadCollisions()` function uses the Separating Axis Theorem (SAT) to check if the `pathHead`'s shape intersects with any static obstacles (like buildings defined in the level file) or other trucks. If a collision is detected, the path is marked as invalid (`isCurrentPathInvalid = true`), and the user cannot continue drawing that route.
    *   **Minimum Distance**: The initial segment of the path must exceed a minimum length (`minTrajectoryDistance`), preventing accidental, short paths.

### 1.2.2. Route Following

Once the player releases the mouse button, the drawn `trajectory` array becomes the truck's route.

1.  **State Transition**: The truck enters a movement state where `isFollowingTrajectory()` returns `true`.
2.  **Waypoint Processing**: The `updateTrajectoryMovement()` function is the core of the navigation loop.
    *   It retrieves the next waypoint from the front of the `trajectory` array using `getCurrentWaypoint()`.
    *   It calculates the required angle to the waypoint and smoothly rotates the truck towards it using `updateAngleSimple()`.
    *   The truck's velocity is applied in its forward direction. The speed is dynamically adjusted based on the sharpness of the turn; sharper turns result in lower speeds to simulate more realistic movement.
3.  **Waypoint Completion**: The truck is considered to have reached a waypoint when its pivot point is within a small tolerance radius (`checkWaypointReached()`). Upon reaching a waypoint, it is removed from the `trajectory` array via `advanceWaypoint()`, and the truck proceeds to the next point.
4.  **Route Completion**: When the `trajectory` array is empty, the `finalizeTrajectoryMovement()` function is called, and the truck stops.

---

## 1.3. Pathfinding Algorithm and AI Logic

The system does not use a traditional, automated pathfinding algorithm. The "intelligence" is focused on assisting and validating the player's manually drawn path.

### 1.3.1. Snap-to-Park (Parking Assistance)

This is the system's primary "AI" decision-making feature. It simplifies the process of parking a truck.

1.  **Detection**: While the player is drawing a path, the `attemptSnapToParkingSlot()` function checks if the path's endpoint is within the `optimizationRadius` of a parking slot's designated `frontPoint`.
2.  **Validation**: A slot is a valid target only if:
    *   It is not currently occupied (`isCurrentlyOccupied()`).
    *   Its `color` property matches the truck's `color`.
3.  **Execution**: If a valid target is found, the path is automatically extended to the slot's `frontPoint`, and the trajectory is finalized. This triggers the automated parking sequence. The truck will then navigate to the slot and park itself without further user input.

```javascript
// Snippet from truck.js showing the snap-to-park check
attemptSnapToParkingSlot: function (trajectoryEndpoint) {
    // ...
    var snapRadius = this.optimizationRadius || 150;

    for (var i = 0; i < this.parkingSlots.length; i++) {
        var slot = this.parkingSlots[i];
        // ... validation checks for color and occupancy ...
        var frontPoint = slotEntity.getFrontPoint ? slotEntity.getFrontPoint() : null;
        // ...
        var distToFrontPoint = ig.utils.distanceBetweenPoints(trajectoryEndpoint, frontPoint);

        if (distToFrontPoint < snapRadius) {
            // We have a snap!
            this.trajectory.push(frontPoint);
            // ...
            return true; // Snap occurred
        }
    }
    return false; // No snap
},
```

### 1.3.2. Path Smoothing (Chaikin's Algorithm)

The file [`lib/plugins/path-smoother.js`](lib/plugins/path-smoother.js) contains an implementation of Chaikin's algorithm, which can be used to smooth the sharp corners of a path by iteratively cutting them. However, analysis of [`truck.js`](lib/game/entities/objects/truck.js) shows that the calls to this smoothing function are currently commented out.

---

## 1.4. Waypoints, Routes, and Special Zones

### 1.4.1. Waypoints and Routes

*   **Waypoints**: The individual `{x, y}` coordinate objects stored in the `trajectory` array.
*   **Route**: The complete, ordered sequence of waypoints in the `trajectory` array. The route is calculated and stored entirely during the player's drag action.

### 1.4.2. Parking Slots

Defined in level files and instantiated as `EntityParkingSlot`. Each slot has key properties derived from its vertices in the level editor:
*   `color`: The required color of the truck.
*   `frontPoint` / `backPoint`: These two points define the slot's orientation and are crucial for the snap-to-park feature and the final parking angle of the truck.
*   `isOccupied`: A boolean flag to prevent multiple trucks from parking in the same spot.

### 1.4.3. Exit Zones

Defined in the `exitZones` array in the level data. These are polygonal or rectangular areas. After a truck has successfully parked and waited, it enters an `exit-ready` state. If the player then draws a path from the truck that terminates inside an exit zone, the truck will follow the path and then continue moving in its final direction until it is off-screen, at which point it is removed from the game and scores a point.

```javascript
// Example of exit zone and parking slot definitions in level1.js
Level1MapData = {
    // ...
    exitZones: [
        {
            name: 'bottom-exit',
            vertices: [{ x: 980.75, y: 1004 }, { x: 1080.00, y: 1004 }, { x: 1080.00, y: 1080.00 }, { x: 981.94, y: 1080.00 }],
            label: 'BOTTOM EXIT',
            showBorder: false
        }
    ],
    buildings: [
        // ... obstacle definitions ...
        {
            name: 'parking-slot-1-building1',
            type: 3, // PARKING_SLOT
            vertices: [{ x: 814.73, y: 251.65 }, { x: 915.77, y: 252.11 }, { x: 915.53, y: 484.79 }, { x: 815.56, y: 486.21 }],
            color: GameColors.RED,
            swapPoints: true
        },
        // ... more parking slots ...
    ]
};
```

---
---

# Part 2: Truck Parking & Unloading Mechanics

This document details the technical implementation of the parking and unloading systems for truck entities in the game. It covers how trucks identify and move into parking spots, the unloading process, and the state management involved.

## 2.1. Parking System Implementation

The parking system is initiated when a player draws a path that ends in proximity to a valid parking spot. The core of this system is a "Snap-to-Park" feature that automates the final approach and alignment of the truck.

### 2.1.1. Identifying and Snapping to a Parking Slot

- **Snap Detection:** When the player is drawing a path (`isClicked` is true), the truck continuously checks if the end of the trajectory is within a specific radius of a parking slot's designated entry point.
    - The truck entity uses its `optimizationRadius` property to define this snap-detection zone around the entry point of each parking slot.
    - The parking slot entity (`EntityParkingSlot`) provides this entry point via the `getFrontPoint()` method. This point is pre-calculated based on the slot's geometry in the level.

- **Path Finalization:**
    - The `attemptSnapToParkingSlot` function in `truck.js` iterates through all available `parkingSlots`.
    - If the final point of the user-drawn trajectory falls within the `optimizationRadius` of a valid slot's `frontPoint`, the system considers it a successful "snap."
    - Upon snapping, the truck's trajectory is automatically extended with the `frontPoint` of the slot, and the path is finalized. This action triggers the automated parking sequence.

### 2.1.2. Automated Parking Maneuver

- **Initiation:** Once a path is snapped, the truck enters the parking state by setting `isParking = true` and `truckState = 'parking'`. The player loses control of the truck at this point.
- **Movement & Alignment:** The `updateParking` and `handleParking` functions manage the maneuver:
    1.  **Target Acquisition:** The truck gets the target parking slot's center coordinates and final angle from `parkingTargetSlotInfo`. The final angle is calculated to be 180 degrees opposite the slot's orientation (`orientationAngle`), ensuring the truck reverses into the spot.
    2.  **Rotation:** The truck rotates towards the `finalParkingAngle` at a speed controlled by `parkingRotationSpeed`.
    3.  **Translation:** Simultaneously, the truck moves towards the center of the parking slot at a speed defined by `parkingSpeed`. This speed is reduced if the truck is not properly aligned with the target angle to ensure a smooth entry.
- **Completion:** The maneuver is considered complete when the truck is within a small `parkingThreshold` distance of the slot's center and has aligned with the final parking angle.

## 2.2. Unloading Process

The "unloading" process is a timed event that occurs immediately after a truck has successfully parked.

- **State Trigger:** Upon successful completion of the parking maneuver, the `finishParking` function is called. It sets `isParked = true` and transitions `truckState` to `'waiting'`.
- **Unloading Timer:**
    - A timer, `parkingWaitTimer`, is initiated and counts up to the value specified in `parkingWaitTime`. This duration can vary per truck type.
    - While in the `'waiting'` state, a visual timer is rendered on the screen over the parking spot. The `drawParkingTimer` function draws a circular progress indicator that fills up as the `parkingWaitTimer` progresses.
- **Completion and State Transition:**
    - Once `parkingWaitTimer` reaches `parkingWaitTime`, the unloading is complete.
    - The truck's `truckState` transitions to `'exit-ready'`.
    - Critically, the truck's angle is immediately flipped by 180 degrees, so it now faces out of the parking spot, ready for the player to draw an exit path.

## 2.3. State Transitions

The truck's behavior during parking and unloading is governed by a set of boolean flags and a state machine property.

### 2.3.1. Key Properties:

-   `isParking` (boolean): `true` only during the automated maneuver into the parking slot. Player input is disabled.
-   `isParked` (boolean): `true` from the moment the parking maneuver is complete until the truck begins to exit the slot. Player input is disabled until the state becomes `'exit-ready'`.
-   `truckState` (string): A more granular state machine that manages the entire lifecycle.

### 2.3.2. State Flow Diagram:

```mermaid
graph TD
    A[Idle / Following Path<br>(truckState: 'none')] --> B{Path Snaps?};
    B -->|Yes| C[Parking Maneuver<br>(isParking: true, truckState: 'parking')];
    C --> D[Parked & Unloading<br>(isParked: true, truckState: 'waiting')];
    D -->|Timer Complete| E[Ready for Exit<br>(isParked: true, truckState: 'exit-ready')];
    E -->|Player Draws Exit Path| F[Exiting<br>(isParked: false, truckState: 'exiting')];
    F --> G[Off-screen & Removed];
```

## 2.4. Parking Validation Logic

To prevent errors and invalid game states, several checks are performed before a truck can snap to or park in a slot. This logic resides primarily in the `_validateSlot` function within `truck.js`.

- **Color Matching:** The truck's `color` property must match the parking slot's `color` property. A truck cannot park in a mismatched slot.
- **Occupancy Status:** The system checks if the target `EntityParkingSlot` is already occupied.
    - The `isCurrentlyOccupied()` method on the slot entity returns its status.
    - A slot is marked as occupied via `setOccupied(true)` when a truck successfully parks in it and is vacated with `setOccupied(false)` when the truck leaves the slot's collision area.
- **Entity Validity:** The system ensures that the parking slot data corresponds to a valid, existing entity in the game world.

---
---

# Part 3: Truck Collision Detection and Response System

## 3.1. Overview

The `EntityTruck` in the ImpactJS game features a sophisticated collision system to handle interactions with the game world, including static obstacles like buildings, screen boundaries, and special entities like parking slots. The system is designed to provide clear feedback to the player upon collision and prevent trucks from getting stuck. It relies on custom polygonal shapes, the Separating Axis Theorem (SAT) for accurate detection, and a multi-phase response mechanism.

## 3.2. Collision Boundaries and Shapes

Instead of simple rectangular bounding boxes, trucks use precise, custom-defined polygons for collision detection. This allows the collision shape to more accurately match the truck's visual sprite.

### 3.2.1. Main Body Shape

*   **Custom Vertices:** The primary collision shape for each truck variant is defined in [`customVertices`](lib/game/entities/objects/truck.js:115). These vertices are defined in local space relative to the entity's center.
    ```javascript
    customVertices: {
        "truck1n": [{ x: -33.14, y: -94.56 }, { x: 30.02, y: -94.56 }, { x: 31.42, y: 98.77 }, { x: -31.74, y: 97.37 }],
        // ... other truck types
    },
    ```
*   **Rotation and World Space:** The [`updateRotatedVertices()`](lib/game/entities/objects/truck.js:1640) function is called whenever the truck moves or rotates. It takes the base `customVertices`, rotates them according to the truck's current `angle`, and translates them to world space coordinates.
*   **SAT Shape:** The resulting world-space vertices are used to create a collision shape object via the `ig.SAT.Shape` constructor, which is stored in the `this.shape` property. This shape is then used by the SAT-based collision detection logic.
    ```javascript
    // in updateRotatedVertices()
    this.updateSATShape();
    
    // in updateSATShape()
    this.shape = new ig.SAT.Shape(this.vertices);
    ```

### 3.2.2. Attack Box

For certain checks, a smaller, forward-facing "attack box" is used. This is primarily for detecting collisions with the front of the truck, for example, when checking for off-screen boundaries.

*   **Definition:** The attack box is a rectangle covering a percentage of the front of the truck, defined by [`attackBoxPercentage`](lib/game/entities/objects/truck.js:49).
*   **Creation:** The vertices for this box are generated by [`createAttackVertices()`](lib/game/entities/objects/truck.js:1605) and, like the main shape, are rotated and stored in a separate SAT shape in the `this.attackShape` property.

## 3.3. Collision Detection

Collision detection is handled through a combination of standard ImpactJS methods and custom logic for specific scenarios.

### 3.3.1. Static Obstacles (Buildings & Boundaries)

*   **`onCollision`:** The primary entry point for collision is the [`onCollision()`](lib/game/entities/objects/truck.js:2514) method. It checks if the truck has collided with an entity of type `BUILDING` or `BOUNDARY`.
*   **Screen Boundaries:** There are no physical "boundary" entities. Instead, the [`checkOffScreen()`](lib/game/entities/objects/truck.js:2670) method continuously checks if the truck's `attackVertices` have moved outside the screen dimensions. If they have, it manually triggers the collision response by calling `onCollision({ type: LevelData.COLLISION_TYPES.BOUNDARY })`.

### 3.3.2. Path-Drawing Collision

To prevent the player from drawing a path that goes through an obstacle, a separate collision check is performed in real-time as the user drags their finger/mouse.

*   **`checkPathHeadCollisions()`:** This function, called during the `clicking()` update, checks the `pathHead` entity (a small circle at the tip of the drawn path) against all buildings and other trucks.
*   **Invalidation:** If the `pathHead` collides with anything, the `isCurrentPathInvalid` flag is set to `true`, the path is visually rendered as invalid (red), and the trajectory is discarded upon release.

## 3.4. Three-Phase Collision Response System

When a truck collides with a `BUILDING` or a `BOUNDARY`, it initiates a three-phase response to stop the truck, provide visual feedback, and reset its state.

### 3.4.1. Phase 1: Immediate Stop & Bounce Initiation

This phase occurs instantly upon collision detection in [`onCollisionInternal()`](lib/game/entities/objects/truck.js:2518).

1.  **Stop Movement:** The truck's current trajectory is immediately cleared (`this.trajectory = []`), and any user input is cancelled.
2.  **Initiate Response:** The [`startBuildingCollisionResponse()`](lib/game/entities/objects/truck.js:2563) function is called, which plays a "crash" sound.
3.  **Start Bounce Back:** The core of the response is handled by [`startBounceBack()`](lib/game/entities/objects/truck.js:2586).
    *   The `isBouncing` flag is set to `true`.
    *   The bounce direction is calculated as the inverse of the truck's velocity just before impact.
    *   The truck's collision mask is temporarily modified via [`setCollisionMaskForResponse()`](lib/game/entities/objects/truck.js:2655) to disable collisions with buildings and boundaries. This is crucial to allow the truck to move away from the obstacle without getting stuck in a collision loop.

### 3.4.2. Phase 2: Bounce Back Animation

This phase handles the physical movement of the truck away from the obstacle.

*   **`updateBounceBack()`:** While `isBouncing` is true, this function is called on every frame.
*   It moves the truck in the calculated `bounceBackDirection` at a speed defined by [`bounceBackSpeed`](lib/game/entities/objects/truck.js:55).
*   The movement speed decreases as the `bounceBackProgress` increases, creating an easing-out effect.

### 3.4.3. Phase 3: Resume Normal State

Once the bounce-back animation is complete, the truck is reset.

*   **`finishBounceBack()`:** This function is called when `bounceBackProgress` reaches its maximum.
*   It stops all movement by setting `vel.x` and `vel.y` to `0`.
*   The `isBouncing` flag is set to `false`.
*   After a short delay, [`setCollisionMaskForResponse(false)`](lib/game/entities/objects/truck.js:2655) is called, which sets `needsCollisionRestore` to true. This flags the system to restore the original collision mask on the next update tick, re-enabling collisions with all obstacles.

## 3.5. Special Collision Handling

### 3.5.1. Parking Slots

Collisions with `PARKING_SLOT` entities do not trigger the bounce-back response. Instead, they initiate the parking sequence.

*   **Entering a Slot:** When the truck's `attackShape` collides with a valid (matching color, unoccupied) parking slot, [`beginParking()`](lib/game/entities/objects/truck.js:2252) is called, and the truck transitions to the `'parking'` state.
*   **Exiting a Slot:** The [`onExitCollisionInternal()`](lib/game/entities/objects/truck.js:2537) method is triggered when the truck is no longer physically overlapping with its designated parking slot. This calls [`vacateSlot()`](lib/game/entities/objects/truck.js:2427) to free up the slot for other trucks.

### 3.5.2. Inter-Truck Collisions

Contrary to earlier analysis, direct truck-vs-truck collision detection is a critical, game-ending event. The logic is managed centrally in the `GameController` rather than the `EntityTruck` to have a global view of all trucks.

*   **Centralized Collision Loop**: The core logic resides in the `update` loop of `ig.GameController` ([`lib/game/controllers/game-controller.js:392`](lib/game/controllers/game-controller.js:392)). It iterates through all pairs of active `EntityTruck` instances.
*   **Two-Phase Check**: For each pair, it first performs a computationally cheap AABB (Axis-Aligned Bounding Box) overlap check using `checkAABBOverlap()`.
*   **Precise SAT Check**: If the AABBs overlap, it proceeds to a more precise collision check using the Separating Axis Theorem (`this.sat.simpleShapeIntersect`). This check is specialized: it specifically checks if the `attackShape` (the front part) of one truck intersects with the main `shape` of the other. This prevents collisions from triggering when only the sides or rears of trucks touch.
*   **Collision Handling**: If the SAT check passes, the `handleTruckVsTruckCollision()` function ([`lib/game/controllers/game-controller.js:621`](lib/game/controllers/game-controller.js:621)) is called.
*   **Game Over Trigger**: This function immediately triggers the game over sequence by calling `this.onGameOver()`. It stops the movement of both colliding trucks (if they aren't in the middle of a parking maneuver), plays a "crash" sound, and displays the game over screen with the final score.
*   **Proximity System as a Warning**: The `updateProximityDetection()` system in the truck entity now serves as a *warning* mechanism rather than the primary collision prevention system. It honks and turns the truck red to alert the player *before* a game-ending collision occurs.

---
---

# Part 4: Additional Core Truck Functions

This document details the remaining core functionalities of the truck entity in the ImpactJS game, based on the analysis of `lib/game/entities/objects/truck.js`.

---

## 4.1. Truck State Management and Lifecycle

The truck's behavior is governed by a state machine controlled by the `truckState` property. This property dictates the truck's current actions, from parking to exiting the level.

### 4.1.1. States and Lifecycle Flow

The truck progresses through the following states in a mostly linear sequence:

1.  **`'none'`**:
    *   The default initial state for a truck after spawning.
    *   In this state, the truck is idle or following a user-drawn trajectory.
    *   The truck is waiting for a valid path to a parking slot.

2.  **`'parking'`**:
    *   **Transition**: Occurs when a user-drawn path snaps to a valid, unoccupied parking slot of the correct color. The `beginParkingInternal` function ([`truck.js:2265`](lib/game/entities/objects/truck.js:2265)) is called.
    *   **Behavior**: The truck autonomously moves towards the center of the target parking slot, adjusting its angle to align with the slot's orientation. User input is disabled.

3.  **`'waiting'`**:
    *   **Transition**: Triggered by `finishParking` ([`truck.js:2388`](lib/game/entities/objects/truck.js:2388)) once the truck reaches the parking slot's center and is correctly aligned.
    *   **Behavior**: The truck becomes stationary within the slot for a randomized duration defined by `parkingWaitTime`. A visual timer is displayed over the truck.

4.  **`'exit-ready'`**:
    *   **Transition**: Occurs when the `parkingWaitTimer` completes in the `handleParkedState` function ([`truck.js:2356`](lib/game/entities/objects/truck.js:2356)).
    *   **Behavior**: The truck flips its orientation 180 degrees to face out of the parking slot. It is now stationary and waiting for the player to draw a new path to an exit zone.

5.  **`'exiting'`**:
    *   **Transition**: Triggered when the player draws a valid path for an `'exit-ready'` truck. The parking slot is vacated via `vacateSlot` ([`truck.js:2427`](lib/game/entities/objects/truck.js:2427)).
    *   **Behavior**: The truck follows the new user-drawn trajectory. It is now eligible to leave the level via an exit zone.

6.  **`'exited'`**:
    *   **Transition**: This state is set immediately after the slot is vacated (`vacateSlot`). It's a sub-state of the exiting process.
    *   **Behavior**: The truck has left its parking spot and is moving. If its path ends within a designated exit zone, it will continue moving off-screen.

The lifecycle concludes when the truck successfully moves off-screen and is destroyed.

```mermaid
graph TD
    A[Spawning] --> B(none);
    B -- Draw Path to Slot --> C(parking);
    C -- Reaches Slot --> D(waiting);
    D -- Wait Timer Ends --> E(exit-ready);
    E -- Draw Path to Exit --> F(exiting / exited);
    F -- Reaches Exit Zone & Moves Off-screen --> G([Destroyed]);
```

---

## 4.2. Proximity Detection System

To prevent collisions and add realism, trucks have a system to detect and react to each other's presence. This system is managed in the `updateProximityDetection` function ([`truck.js:1164`](lib/game/entities/objects/truck.js:1164)).

### 4.2.1. Key Features:

*   **Detection Radius**: A truck checks for other trucks within a `proximityThreshold` of 210 pixels.
*   **Audible Warning**: When another truck enters this radius, `onTruckEnterProximity` ([`truck.js:1229`](lib/game/entities/objects/truck.js:1229)) is called, which plays a "honk" sound effect. A `proximityHonkCooldown` timer prevents the sound from playing too frequently.
*   **Visual Warning**:
    *   When one or more trucks are nearby, the `enterProximityWarningState` function ([`truck.js:1254`](lib/game/entities/objects/truck.js:1254)) is triggered.
    *   This function swaps the truck's current animation to a "warning" version, which is visually identical but colored red. This is achieved by changing the animation name suffix (e.g., from `truck1n` to `truck1w`).
    *   The truck's original appearance is stored in `originalTruckType` and restored via `exitProximityWarningState` ([`truck.js:1269`](lib/game/entities/objects/truck.js:1269)) once no other trucks are in the proximity radius.

---

## 4.3. Exit Conditions and Screen Boundary Handling

The process for a truck to leave the level is clearly defined, involving exit zones and off-screen checks.

### 4.3.1. Exit Zone Mechanics

*   **Definition**: Exit zones are areas on the map where trucks can despawn. They are defined in the level data and can be rectangular areas or complex polygons.
*   **Eligibility**: A truck must be in the `'exited'` state to be able to use an exit zone.
*   **Detection**: The `isInExitZone` function ([`truck.js:1943`](lib/game/entities/objects/truck.js:1943)) checks if the truck's pivot point is within the boundaries of any defined exit zone.
*   **Visual Cue**: When an `'exit-ready'` truck is selected and the player is drawing a path, the available exit zones are highlighted with a blinking yellow overlay, as seen in `drawExitZones` ([`truck.js:642`](lib/game/entities/objects/truck.js:642)).

### 4.3.2. Exiting Process

1.  When a truck following a trajectory completes its path inside an exit zone, the `enterExitMode` function ([`truck.js:2156`](lib/game/entities/objects/truck.js:2156)) is called.
2.  The truck continues to move in its last known direction.
3.  The `checkOffScreen` function ([`truck.js:2670`](lib/game/entities/objects/truck.js:2670)) continuously monitors the truck's position. For an exiting truck, it waits until **all** of the truck's vertices are outside the screen boundaries.
4.  Once fully off-screen, `onExitScreen` ([`truck.js:2748`](lib/game/entities/objects/truck.js:2748)) is called, which increments the player's score and `kill()`s the entity, removing it from the game.

### 4.3.3. Screen Boundary Handling

For non-exiting trucks, the screen edges act as a solid boundary.
*   The `checkOffScreen` function, when not in exit mode, checks if **any** vertex of the truck's forward-facing "attack box" has gone outside the screen boundaries.
*   If it has, this is treated as a collision with a `BOUNDARY` object, triggering the same bounce-back collision response used for hitting buildings.

---

## 4.4. Performance Optimizations

Several optimizations are in place to ensure smooth gameplay, even with many trucks on screen.

*   **Exit Zone Caching**:
    *   On initialization, the `cacheExitZoneBounds` function ([`truck.js:217`](lib/game/entities/objects/truck.js:217)) pre-calculates and stores the bounding boxes of all exit zones from the level data.
    *   Subsequent checks in functions like `isInExitZone` and `drawExitZones` read from this cache instead of performing expensive calculations every frame.

*   **Proximity Check Throttling**:
    *   The proximity detection logic in `updateProximityDetection` does not run on every frame.
    *   It uses a `proximityCheckCooldown` timer to ensure the check is performed only a few times per second, significantly reducing the computational load from distance checks between all trucks.

*   **Conditional Updates**:
    *   The `updatePositionAndRotation` function ([`truck.js:414`](lib/game/entities/objects/truck.js:414)) contains a crucial check: it only updates the truck's position and recalculates its collision vertices if the truck has velocity (`vel.x` or `vel.y` is not 0) or if its angle has changed.
    *   This prevents redundant, costly calculations for stationary trucks.

*   **Broad-Phase Collision Detection**:
    *   When checking for path collisions in `checkPathHeadCollisions` ([`truck.js:1872`](lib/game/entities/objects/truck.js:1872)), the system first performs a computationally cheap Axis-Aligned Bounding Box (AABB) overlap test.
    *   The more expensive and precise Separating Axis Theorem (SAT) polygon collision check is only performed if the broad-phase AABB test passes. This avoids running complex calculations for objects that are not close to each other.