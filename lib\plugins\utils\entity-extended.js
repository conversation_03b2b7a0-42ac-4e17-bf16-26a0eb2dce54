ig.module('plugins.utils.entity-extended')
.requires(
    'impact.entity'
).defines(function () {

    ig.EntityExtended = ig.Entity.extend({
        children: [],
        init: function (x, y, settings) {
            if (this.idleSheetInfo != null) {
				this.setSpriteSheet('idle');
				this.setSize('idle');
			}
            this.parent(x, y, settings);
        },
        
        setSpriteSheet: function (state) {
			this[state + 'Sheet'] = new ig.AnimationSheet(this[state + 'SheetInfo'].sheetImage.path,
			this[state + 'SheetInfo'].sheetImage.width / this[state + 'SheetInfo'].frameCountX,
			this[state + 'SheetInfo'].sheetImage.height / this[state + 'SheetInfo'].frameCountY);
		},

		setSize: function (state) {
			this.size.x = this[state + 'SheetInfo'].sheetImage.width / this[state + 'SheetInfo'].frameCountX;
			this.size.y = this[state + 'SheetInfo'].sheetImage.height / this[state + 'SheetInfo'].frameCountY;
		},
        /**
         * Hides the entity by disabling it and updating its zIndex.
         */
        hide: function () {
            if (this.hidden) return;
            this.disable();
            this.hideChildren();
            this.hidden = true;
            this.visible = false;
            this._zIndex = this.zIndex;
            this.zIndex = -1;
            if (ig.game.currentWindow.overlay && ig.game.currentWindow.overlay.zIndex > 0) {
                ig.game.currentWindow.overlay.hide();
            }
            ig.game.sortEntitiesDeferred();
        },

        /**
         * Shows the entity by enabling it and restoring its zIndex.
         */
        show: function () {
            if (!this.hidden) return;
            this.enable();
            this.showChildren();
            this.hidden = false;
            this.visible = true;
            this.zIndex = this._zIndex;
            ig.game.sortEntitiesDeferred();
        },

        hideChildren: function () {
            if (this.children.length > 0) {
                for (var i = 0; i < this.children.length; i++) {
                    var child  = this.children[i];
                    if (typeof child.hide === 'function') {
                        child.hide();
                    }
                }
            }
        },

        showChildren: function () {
            if (this.children.length > 0) {
                for (var i = 0; i < this.children.length; i++) {
                    var child  = this.children[i];
                    if (typeof child.show === 'function') {
                        child.show();
                    }
                }
            }
        },

        /**
         * Disables the entity, preventing interactions.
         */
        disable: function () {
            if (this.disabled) return;
            this.disabled = true;
        },

        /**
         * Enables the entity, allowing interactions.
         */
        enable: function () {
            if (!this.disabled) return;
            this.disabled = false;
        },
        
        /**
         * Spawns a new entity and automatically adds parent reference.
         * Keeps track of child entities in the children array.
         * @returns {Object} The created entity
         */
        spawnEntity: function (entityClass, x, y, settings) {
            // Create a new array for arguments
            var args = [entityClass, x, y];
            
            // Create settings object if not provided
            settings = settings || {};
            
            // Add parent reference
            settings._parent = this;

            // Make child zIndex on-top of parent
            if (!settings.zIndex) settings.zIndex = this.zIndex + 1;
            
            // Add the modified settings to arguments
            args.push(settings);
            
            // Call the original spawn method with our modified arguments
            var entity = ig.game.spawnEntity.apply(ig.game, args);
            
            // Add to children array
            this.children.push(entity);
            
            // Return the created entity
            return entity;
        }
    });
});
