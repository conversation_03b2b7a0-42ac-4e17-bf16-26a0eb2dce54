# Comprehensive Technical Documentation: Truck System

This document provides a complete technical overview of the truck entity system in the ImpactJS game, based on a detailed analysis of `lib/game/entities/objects/truck.js` and related game files.

---

## 1. Truck Pathfinding System

### 1.1. Overview

The Truck Pathfinding System is a core gameplay mechanic responsible for the movement and navigation of truck entities. It is a player-driven system where routes are manually drawn by the user, rather than being determined by an automated pathfinding algorithm like A*. The system's complexity lies in its real-time path validation, waypoint following, and automated sequences for parking and exiting the level.

The primary files governing this system are:
*   `lib/game/entities/objects/truck.js`: The main truck entity containing all movement, input, and state logic.
*   `lib/game/entities/path-head.js`: A logical entity used for real-time collision detection of the drawn path.
*   `lib/game/entities/objects/parking-slot.js`: Defines the destination points for trucks.
*   `lib/game/levels/*.js`: Level files that define the physical environment, including collision boundaries, parking slot locations, and exit zones.

### 1.2. Truck Navigation and Route Drawing

Truck navigation is initiated and controlled directly by the player through a click-and-drag interface.

#### 1.2.1. Path Creation

1.  **Initiation**: The player clicks on a truck entity. This action is detected in `truck.js` by the `handleInput` function, which calls `onClicked()`.
2.  **Path Head**: A logical `EntityPathHead` is spawned at the cursor's position. This entity has a circular collision shape whose radius (`pathHeadRadius`) is based on the truck's width, effectively representing the truck's physical footprint as the path is drawn.
3.  **Drawing**: As the player drags the cursor, the `clicking()` function continuously updates the `pathHead`'s position and adds coordinates to the truck's `trajectory` array.
    *   Points are added only if the distance between them exceeds a minimum threshold, preventing an excessive number of waypoints.
    *   To ensure path smoothness, the system interpolates and adds extra points if the cursor moves a large distance between frames.
4.  **Path Validation**: The system performs two crucial validation checks in real-time:
    *   **Collision Detection**: The `checkPathHeadCollisions()` function uses the Separating Axis Theorem (SAT) to check if the `pathHead`'s shape intersects with any static obstacles (like buildings defined in the level file) or other trucks. If a collision is detected, the path is marked as invalid (`isCurrentPathInvalid = true`), and the user cannot continue drawing that route.
    *   **Minimum Distance**: The initial segment of the path must exceed a minimum length (`minTrajectoryDistance`), preventing accidental, short paths.

#### 1.2.2. Route Following

Once the player releases the mouse button, the drawn `trajectory` array becomes the truck's route.

1.  **State Transition**: The truck enters a movement state where `isFollowingTrajectory()` returns `true`.
2.  **Waypoint Processing**: The `updateTrajectoryMovement()` function is the core of the navigation loop.
    *   It retrieves the next waypoint from the front of the `trajectory` array using `getCurrentWaypoint()`.
    *   It calculates the required angle to the waypoint and smoothly rotates the truck towards it using `updateAngleSimple()`.
    *   The truck's velocity is applied in its forward direction. The speed is dynamically adjusted based on the sharpness of the turn; sharper turns result in lower speeds to simulate more realistic movement.
3.  **Waypoint Completion**: The truck is considered to have reached a waypoint when its pivot point is within a small tolerance radius (`checkWaypointReached()`). Upon reaching a waypoint, it is removed from the `trajectory` array via `advanceWaypoint()`, and the truck proceeds to the next point.
4.  **Route Completion**: When the `trajectory` array is empty, the `finalizeTrajectoryMovement()` function is called, and the truck stops.

### 1.3. Pathfinding Algorithm and AI Logic

The system does not use a traditional, automated pathfinding algorithm. The "intelligence" is focused on assisting and validating the player's manually drawn path.

#### 1.3.1. Snap-to-Park (Parking Assistance)

This is the system's primary "AI" decision-making feature. It simplifies the process of parking a truck.

1.  **Detection**: While the player is drawing a path, the `attemptSnapToParkingSlot()` function checks if the path's endpoint is within the `optimizationRadius` of a parking slot's designated `frontPoint`.
2.  **Validation**: A slot is a valid target only if it is not currently occupied (`isCurrentlyOccupied()`) and its `color` property matches the truck's `color`.
3.  **Execution**: If a valid target is found, the path is automatically extended to the slot's `frontPoint`, and the trajectory is finalized. This triggers the automated parking sequence.

```javascript
// Snippet from truck.js showing the snap-to-park check
attemptSnapToParkingSlot: function (trajectoryEndpoint) {
    // ...
    var snapRadius = this.optimizationRadius || 150;

    for (var i = 0; i < this.parkingSlots.length; i++) {
        var slot = this.parkingSlots[i];
        // ... validation checks for color and occupancy ...
        var frontPoint = slotEntity.getFrontPoint ? slotEntity.getFrontPoint() : null;
        // ...
        var distToFrontPoint = ig.utils.distanceBetweenPoints(trajectoryEndpoint, frontPoint);

        if (distToFrontPoint < snapRadius) {
            // We have a snap!
            this.trajectory.push(frontPoint);
            // ...
            return true; // Snap occurred
        }
    }
    return false; // No snap
},
```

#### 1.3.2. Path Smoothing (Chaikin's Algorithm)

The file `lib/plugins/path-smoother.js` contains an implementation of Chaikin's algorithm, which can be used to smooth the sharp corners of a path by iteratively cutting them. However, analysis of `truck.js` shows that the calls to this smoothing function are currently commented out.

### 1.4. Waypoints, Routes, and Special Zones

*   **Waypoints**: The individual `{x, y}` coordinate objects stored in the `trajectory` array.
*   **Route**: The complete, ordered sequence of waypoints in the `trajectory` array.
*   **Parking Slots**: Defined in level files as `EntityParkingSlot`, each with a `color`, `frontPoint`, and `backPoint`.
*   **Exit Zones**: Polygonal areas defined in level data. If a truck in an `exit-ready` state has a path drawn to an exit zone, it will drive off-screen and be removed.

```javascript
// Example of exit zone and parking slot definitions in level1.js
Level1MapData = {
    // ...
    exitZones: [
        {
            name: 'bottom-exit',
            vertices: [{ x: 980.75, y: 1004 }, { x: 1080.00, y: 1004 }, { x: 1080.00, y: 1080.00 }, { x: 981.94, y: 1080.00 }],
            label: 'BOTTOM EXIT',
            showBorder: false
        }
    ],
    buildings: [
        // ... obstacle definitions ...
        {
            name: 'parking-slot-1-building1',
            type: 3, // PARKING_SLOT
            vertices: [{ x: 814.73, y: 251.65 }, { x: 915.77, y: 252.11 }, { x: 915.53, y: 484.79 }, { x: 815.56, y: 486.21 }],
            color: GameColors.RED,
            swapPoints: true
        },
        // ... more parking slots ...
    ]
};
```

---

## 2. Parking and Unloading Mechanics

### 2.1. Parking System Implementation

The parking system is initiated when a player draws a path that ends in proximity to a valid parking spot.

#### 2.1.1. Identifying and Snapping to a Parking Slot

- **Snap Detection:** The `attemptSnapToParkingSlot` function checks if the end of the user-drawn trajectory is within the `optimizationRadius` of a parking slot's `frontPoint`.
- **Path Finalization:** If a valid slot is found, the truck's trajectory is automatically extended with the `frontPoint`, and the path is finalized, triggering the automated parking sequence.

#### 2.1.2. Automated Parking Maneuver

- **Initiation:** The truck enters the parking state by setting `isParking = true` and `truckState = 'parking'`.
- **Movement & Alignment:** The `updateParking` and `handleParking` functions manage the maneuver, moving the truck to the slot's center and rotating it to the final parking angle.
- **Completion:** The maneuver is complete when the truck is within a `parkingThreshold` distance of the slot's center and correctly aligned.

### 2.2. Unloading Process

The "unloading" is a timed event that occurs after successful parking.

- **State Trigger:** `finishParking` is called, setting `isParked = true` and `truckState = 'waiting'`.
- **Unloading Timer:** A `parkingWaitTimer` counts up to `parkingWaitTime`. A circular progress indicator is drawn on-screen via `drawParkingTimer`.
- **Completion:** Once the timer finishes, `truckState` transitions to `'exit-ready'`, and the truck's angle is flipped 180 degrees.

### 2.3. State Transitions

```mermaid
graph TD
    A[Idle / Following Path<br>(truckState: 'none')] --> B{Path Snaps?};
    B -->|Yes| C[Parking Maneuver<br>(isParking: true, truckState: 'parking')];
    C --> D[Parked & Unloading<br>(isParked: true, truckState: 'waiting')];
    D -->|Timer Complete| E[Ready for Exit<br>(isParked: true, truckState: 'exit-ready')];
    E -->|Player Draws Exit Path| F[Exiting<br>(isParked: false, truckState: 'exiting')];
    F --> G[Off-screen & Removed];
```

### 2.4. Parking Validation Logic

The `_validateSlot` function ensures:
- **Color Matching:** The truck's `color` must match the slot's `color`.
- **Occupancy Status:** The slot must not be currently occupied via `isCurrentlyOccupied()`.

---

## 3. Collision Detection and Response System

### 3.1. Overview

The system uses custom polygonal shapes, the Separating Axis Theorem (SAT) for accurate detection, and a multi-phase response mechanism.

### 3.2. Collision Boundaries and Shapes

- **Main Body Shape**: Defined in `customVertices`, these are rotated by `updateRotatedVertices()` and used to create an `ig.SAT.Shape`.
- **Attack Box**: A smaller, forward-facing rectangle defined by `attackBoxPercentage` and created by `createAttackVertices()`, used for specific checks.

### 3.3. Collision Detection

- **Static Obstacles:** The `onCollision()` method handles collisions with `BUILDING` or `BOUNDARY` types.
- **Screen Boundaries:** `checkOffScreen()` checks if the truck is outside screen dimensions and manually triggers the `onCollision` response.
- **Path-Drawing Collision:** `checkPathHeadCollisions()` checks the `pathHead` in real-time to prevent drawing invalid paths.

### 3.4. Three-Phase Collision Response System

Triggered by `onCollisionInternal()`:

1.  **Phase 1: Immediate Stop & Bounce Initiation**
    *   The truck's trajectory is cleared.
    *   `startBuildingCollisionResponse()` plays a "crash" sound.
    *   `startBounceBack()` sets `isBouncing = true` and calculates the bounce direction.
    *   `setCollisionMaskForResponse()` temporarily disables collisions to prevent getting stuck.

2.  **Phase 2: Bounce Back Animation**
    *   `updateBounceBack()` moves the truck in the calculated direction with an easing-out effect.

3.  **Phase 3: Resume Normal State**
    *   `finishBounceBack()` stops all movement.
    *   The original collision mask is restored.

### 3.5. Special Collision Handling

- **Parking Slots**: Collisions with `PARKING_SLOT` entities initiate the parking sequence via `beginParking()`. `onExitCollisionInternal()` frees the slot via `vacateSlot()`.
- **Inter-Truck Collisions**: Direct physics collisions are avoided. The Proximity Detection System provides warnings instead.

---

## 4. Additional Core Truck Functions

### 4.1. Truck State Management and Lifecycle

The `truckState` property governs the truck's lifecycle:

1.  **`'none'`**: Idle or following a user-drawn path.
2.  **`'parking'`**: Triggered by `beginParkingInternal`, the truck moves autonomously into a slot.
3.  **`'waiting'`**: Triggered by `finishParking`, the truck waits for a timed duration.
4.  **`'exit-ready'`**: Triggered by `handleParkedState`, the truck is ready to leave.
5.  **`'exiting'` / `'exited'`**: Triggered when the player draws an exit path. The truck follows the path and can leave the level.

### 4.2. Proximity Detection System

Managed in `updateProximityDetection`:

- **Audible Warning**: `onTruckEnterProximity` plays a "honk" sound.
- **Visual Warning**: `enterProximityWarningState` swaps the truck's animation to a red-colored "warning" version.

### 4.3. Exit Conditions and Screen Boundary Handling

- **Exit Zones**: `isInExitZone` checks if the truck is within a valid exit area. `drawExitZones` highlights them.
- **Exiting Process**: `enterExitMode` is called when a path ends in an exit zone. `onExitScreen` is called when the truck is fully off-screen, which then `kill()`s the entity.

### 4.4. Performance Optimizations

- **Exit Zone Caching**: `cacheExitZoneBounds` pre-calculates exit zone bounding boxes.
- **Proximity Check Throttling**: `updateProximityDetection` uses a cooldown to avoid running every frame.
- **Conditional Updates**: `updatePositionAndRotation` only runs calculations if the truck is moving or rotating.
- **Broad-Phase Collision Detection**: `checkPathHeadCollisions` uses a cheap AABB test before the expensive SAT check.