<!DOCTYPE html>
<html>
<head>
    <title>Path Preservation Test - Truck Master</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        #canvas {
            border: 2px solid #333;
            display: block;
            margin: 0 auto;
            background-color: white;
        }
        .info {
            max-width: 960px;
            margin: 20px auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-scenarios {
            margin-top: 20px;
        }
        .scenario {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f8f8;
            border-left: 4px solid #4CAF50;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 0 5px;
            font-size: 16px;
            cursor: pointer;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            background-color: #e8f4f8;
            border-radius: 4px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>Path Preservation Test</h1>
        <p>This test verifies that clicking on a truck with an existing path preserves the path unless a new valid path is drawn.</p>
        
        <div class="test-scenarios">
            <h2>Test Scenarios:</h2>
            
            <div class="scenario">
                <strong>Scenario 1: Click and Release Without Drawing</strong>
                <ol>
                    <li>Draw a valid path for a truck</li>
                    <li>Click on the truck and immediately release without drawing</li>
                    <li>Expected: The original path should remain visible and the truck continues following it</li>
                </ol>
            </div>
            
            <div class="scenario">
                <strong>Scenario 2: Draw Invalid Path (Too Short)</strong>
                <ol>
                    <li>Draw a valid path for a truck</li>
                    <li>Click on the truck and draw a very short path (less than minimum distance)</li>
                    <li>Expected: The original path should be restored when you release</li>
                </ol>
            </div>
            
            <div class="scenario">
                <strong>Scenario 3: Draw Valid New Path</strong>
                <ol>
                    <li>Draw a valid path for a truck</li>
                    <li>Click on the truck and draw a new valid path</li>
                    <li>Expected: The new path should replace the old one</li>
                </ol>
            </div>
            
            <div class="scenario">
                <strong>Scenario 4: Path Extension</strong>
                <ol>
                    <li>Draw a valid path for a truck</li>
                    <li>Click near the end of the existing path to extend it</li>
                    <li>Expected: The path should extend from the existing endpoint</li>
                </ol>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="location.reload()">Reload Test</button>
            <button onclick="toggleDebugMode()">Toggle Debug Mode</button>
        </div>
        
        <div class="status">
            <p><strong>Instructions:</strong> The game will load with trucks spawning automatically. Test each scenario above to verify path preservation works correctly.</p>
        </div>
    </div>

    <canvas id="canvas"></canvas>

    <script type="text/javascript" src="lib/impact/impact.js"></script>
