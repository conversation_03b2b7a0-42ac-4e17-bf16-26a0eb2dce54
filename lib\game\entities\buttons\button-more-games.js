ig.module('game.entities.buttons.button-more-games')
.requires(
	'plugins.utils.buttons.button-base'
	, 'plugins.clickable-div-layer'
)
.defines(function () {
	EntityButtonMoreGames = EntityButtonBase.extend({
		type: ig.Entity.TYPE.A,
		idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-more-games.png'), frameCountX: 1, frameCountY: 1 },
        buttonTextConfig: {
            fontSize: 60,
            fontFamily: 'bebasneue-bold',
            fontColor: '#fff',
            text: _STRINGS['Game']['Buttons']['MoreGames'],
            align: 'left',
            vAlign: 'bottom'
        },
        hasText: true,
        buttonTextOffset: { x: 0, y: -8 },
		clickableLayer: null,
		link: null,
		newWindow: false,
		div_layer_name: "more-games",
		name: "moregames",
		init: function (x, y, settings){
			this.parent(x, y, settings);

            //ig.soundHandler.unmuteAll(true);
            
			if (ig.global.wm)
			{
				return;
			}
			
			if (settings.div_layer_name)
			{
				//console.log('settings found ... using that div layer name')
				this.div_layer_name = settings.div_layer_name;
			}
			else
			{
				this.div_layer_name = 'more-games';
			}
			
			if (_SETTINGS.MoreGames.Enabled)
			{
				this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
				this.currentAnim = this.idle;
				
				if (_SETTINGS.MoreGames.Link)
				{
					this.link = _SETTINGS.MoreGames.Link;
				}
				if (_SETTINGS.MoreGames.NewWindow)
				{
					this.newWindow = _SETTINGS.MoreGames.NewWindow;
				}
				this.clickableLayer = new ClickableDivLayer(this);

				this.buttonTextOffset.x = this.size.x * 0.25;

				this.textEntity.anchorTo(
					this, /* Target entity: this button */
					{
						targetAnchor: { x: 0, y: 0 }, /* Target anchor: center of the button */
						offset: this.buttonTextOffset /* Pixel offset */
					}
				);
			}
			else
			{
				this.kill();
			}	
		},
        show: function ()
        {
            var elem = ig.domHandler.getElementById("#" + this.div_layer_name);
            if (elem) { ig.domHandler.show(elem); }
        },
        hide: function ()
        {
            var elem = ig.domHandler.getElementById("#" + this.div_layer_name);
            if (elem) { ig.domHandler.hide(elem); }
        },

		disable: function () {
			this.parent();
			this.hide();
		},
		enable: function () {
			this.parent();
			this.show();
		}
	});
});