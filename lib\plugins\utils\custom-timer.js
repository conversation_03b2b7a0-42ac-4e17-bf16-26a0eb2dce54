/*
    v1.0.0
    CustomTimer and TimerManager plugin by <PERSON><PERSON><PERSON>
    ©️ <PERSON> - for the additional utility functions
    CustomTimer:
        Custom timer with callback function upon completion.

        - init(seconds, callback, context): Initializes the timer.
            - @param (required) seconds: The duration for the timer to run in seconds.
            - @param (required) callback: The function that will be called when the timer is up.
            - @param (optional) context: The context in which the callback function will be invoked (For example, 'this').

        Usage Example:
            (somewhere in your code)
            var custCallback = function() {
                console.log('Timer finished!');
            };
            var custTimer = new CustomTimer(5, custCallback, this);

            (in update function)
            custTimer.tick();

    TimerManager:
        This class manages multiple instances of CustomTimer.

        - add(name, seconds, callback, context): Adds a new timer.
            - @param (optional) name: The name of the timer (optional). If not provided, a unique name will be automatically generated.
            - @param (required) seconds: The duration for the timer to run in seconds.
            - @param (required) callback: The function that will be called when the timer is up.
            - @param (optional) context: The context in which the callback function will be invoked (For example, 'this').

        - find(name): Finds a timer by name.
            - @param name: The name of the timer to find.
            - @returns: The timer if found, otherwise null.

        - update(): Updates all timers managed by the TimerManager, and automatically removes any timers whose callbacks have been called.

        Usage Example:
            Initialize the timer manager
            (init function in main.js)
                this.timerManager = new TimerManager();
            
            Update the timer manager
            (update function in main.js)
                this.timerManager.update();
            
            Add a new timer
            (somewhere in your code)
                var custCallback= function() {
                    console.log('Timer finished!');
                };
                ig.game.timerManager.add('myTimer', 5, custCallback, this);  // Add a named timer
                ig.game.timerManager.add(5, custCallback, this);  // Add an unnamed timer

            Find a timer
            (somewhere in your code)
                var myTimer = ig.game.timerManager.find('myTimer');
*/
ig.module(
    'plugins.utils.custom-timer'
)
.requires(
    'impact.impact'
)
.defines(function() {
    // The CustomTimer class, each instance represents an individual timer.
    CustomTimer = ig.Class.extend({
        target: 0,
        base: 0,
        last: 0,
        pausedAt: 0,
        callback: null,
        context: null,

        // Initializes the timer with the provided parameters.
        init: function(seconds, callback, context) {
            this.last = this.base = ig.Timer.time;
            this.target = seconds || 0;
            this.callback = callback || null;
            this.context = context || null;
        },

        // Sets the timer with new parameters.
        set: function(seconds, callback, context) {
            this.target = seconds || 0;
            this.base = ig.Timer.time;
            this.pausedAt = 0;
            this.callback = callback || null;
            this.context = context || null;
        },

        // Sets the timer with new parameters.
        updateTime: function(seconds) {
            this.target = seconds || 0;
        },

        // Resets the timer, effectively starting it anew.
        reset: function() {
            this.base = ig.Timer.time;
            this.pausedAt = 0;
        },

        // Called on each frame to update the timer's state. 
        // If the timer's duration has passed, calls the callback function.
        tick: function() {
            var delta = (this.pausedAt || ig.Timer.time) - this.base;
            if (this.callback && delta >= this.target) {
                this.callback.call(this.context);
                this.callback = null;  // Ensure the callback is called only once
            }
            return delta;
        },

        tickInfinite: function() {
            var delta = (this.pausedAt || ig.Timer.time) - this.base;
            if (this.callback && delta >= this.target) {
                this.callback.call(this.context);
                this.base = ig.Timer.time;
            }
            return delta;
        },

        // Pauses the timer.
        pause: function() {
            if (!this.pausedAt) {
                this.pausedAt = ig.Timer.time;
            }
        },

        // Unpauses the timer.
        unpause: function() {
            if (this.pausedAt) {
                this.base += ig.Timer.time - this.pausedAt;
                this.pausedAt = 0;
            }
        },

        // Returns the time since the timer finished.
        delta: function() {
            var currentTime = this.pausedAt || ig.Timer.time;
            return currentTime - (this.base + this.target);
        },

        // Returns the time since the timer started.
        timePassed: function() {
            var currentTime = this.pausedAt || ig.Timer.time;
            return currentTime - this.base;
        },

        // Adds additional duration to the timer.
        addTime: function(duration) {
            this.target += duration;
            this.pausedAt = 0;
        },

        // Returns the percentage of the timer's duration that has passed.
        getPercentPassed: function() {
            var currentTime = this.pausedAt || ig.Timer.time;
            var passedTime = currentTime - this.base;
            return passedTime / this.target;
        },

        // Returns the percentage of the timer's duration that is remaining.
        getPercentLeft: function() {
            var currentTime = this.pausedAt || ig.Timer.time;
            var leftTime = this.target - (currentTime - this.base);
            return leftTime / this.target;
        },

        // Checks if the timer has finished.
        isFinished: function() {
            return this.delta() >= 0;
        },
    });

    // The TimerManager class, used to manage multiple CustomTimer instances.
    TimerManager = ig.Class.extend({
        timers: {},
        unnamedTimerCount: 0,

        // Adds a new timer to the TimerManager.
        add: function(name, seconds, callback, context) {
            // If only name is not provided, shift the parameters.
            if (typeof name === 'number') {
                context = callback;
                callback = seconds;
                seconds = name;
                name = null;
            }

            // Generate a unique name if one is not provided.
            if (!name) {
                name = 'UnnamedTimer' + this.unnamedTimerCount++;
            }

            // Throw an error if a timer with the same name already exists.
            if (this.timers.hasOwnProperty(name)) {
                throw new Error('Timer with name \'' + name + '\' already exists.');
            }

            // Create a new CustomTimer instance and add it to the timers object.
            var timer = new CustomTimer(seconds, callback, context);
            this.timers[name] = timer;
            return timer;  // In case you need a reference to the timer.
        },

        // Finds and returns a timer by its name. Returns null if the timer is not found.
        find: function(name) {
            if (!this.timers.hasOwnProperty(name)) {
                return null;
            }

            return this.timers[name];
        },

        // Removes a timer by name after stopping it. Does nothing if the timer is not found.
        remove: function(name) {
            if (this.timers.hasOwnProperty(name)) {
                var timer = this.timers[name];
                timer.callback = null;  // Stop the timer by clearing its callback
                delete this.timers[name];
            }
        },

        // Updates all timers managed by the TimerManager, and automatically removes any timers whose callbacks have been called.
        update: function() {
            for (var name in this.timers) {
                var timer = this.timers[name];
                timer.tick();
                if (timer.callback === null) {  // Timer is done when callback has been called.
                    delete this.timers[name];
                }
            }
        }
    });     
});
