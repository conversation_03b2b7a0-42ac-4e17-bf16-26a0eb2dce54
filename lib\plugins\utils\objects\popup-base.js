ig.module('plugins.utils.objects.popup-base')
.requires(
    'plugins.utils.entity-extended',
    'plugins.utils.buttons.button-factory',
    'game.entities.text'
)
.defines(function () {
    EntityPopupBase = ig.EntityExtended.extend({
        name: 'popup-base',
        collides: ig.Entity.COLLIDES.NEVER,
        type: ig.Entity.TYPE.A, // Typically for UI elements that don't interact physically
        gravityFactor: 0,
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/popup.png'), frameCountX: 1, frameCountY: 1 },
        zIndex: 1000,
        popupAlpha: 0,
        isVisible: false,
        displayOverlay: true,
        isTweening: false,
        elements: {}, // To store child elements like overlay, close button, and header text
        tweenPosY: 500, // Vertical distance for tweening animation

        // Default configuration for the header text.
        // This will be passed to the spawned EntityText.
        // Can be overridden by 'headerTextConfig' in settings when spawning EntityPopupBase.
        headerTextConfig: {
            text: 'Popup Header',
            fontSize: 48,
            fontFamily: 'Arial',
            fontColor: '#000000',
            align: 'center',
            vAlign: 'middle',
            // width & height will be calculated based on popup size or can be set in settings
        },

        // Default offset for positioning the header text EntityText relative to the popup's top-left.
        // Can be overridden by 'headerTextOffset' in settings.
        headerTextOffset: { x: 0, y: 0 },

        hasCloseButton: true,

        init: function (x, y, settings) {
            if (settings.idleSheetInfo) this.idleSheetInfo = settings.idleSheetInfo;
            this.parent(x, y, settings); // Call parent's init

            this.zIndex = ig.game.LAYERS.POPUP; // Set zIndex from game layers

            // Initialize animation for the popup background
            this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            this.currentAnim = this.idle;
            this.currentAnim.alpha = this.popupAlpha; // Set initial alpha for the background

            this.repos(); // Initial position calculation

            // Spawn overlay
            this.elements.overlay = ig.game.spawnEntity(EntityPopupOverlay, 0, 0, { 
                zIndex: this.zIndex - 2, 
                displayOverlay: this.displayOverlay,
                opacityCap: this.opacityCap
            });
            
            if (this.hasCloseButton) {
                // Spawn close button - ensure it's on top
                this.elements.buttonClose = ig.game.spawnEntity(EntityButtonClosePopup, 0, 0, { 
                    zIndex: this.zIndex + 2, // Higher zIndex than header text
                    popupEntity: this 
                });
            }

            // --- Initialize Header Text using EntityText ---
            var currentHeaderTextConfig = {};
            // Start with this popup's defaults for its header
            ig.merge(currentHeaderTextConfig, this.headerTextConfig);
            // Override with settings passed to EntityPopupBase for its header
            if (settings && settings.headerTextConfig) {
                ig.merge(currentHeaderTextConfig, settings.headerTextConfig);
            }

            // Set dynamic width/height for the header text box if not specified
            currentHeaderTextConfig.width = currentHeaderTextConfig.width || (this.size.x); // (this.size.x * 0.92)
            currentHeaderTextConfig.height = currentHeaderTextConfig.height || (this.size.y * 0.2);
            
            // Determine the offset for the header text
            var currentHeaderTextOffset = (settings && settings.headerTextOffset) ? settings.headerTextOffset : this.headerTextOffset;

            // Spawn the EntityText for the header
            var headerTextEntitySettings = {
                textConfig: currentHeaderTextConfig,
                alpha: this.popupAlpha, // Start with the popup's current alpha
                zIndex: this.zIndex + 1 // zIndex between popup bg and close button
            };
            
            // Calculate initial position for the header text entity
            // This position is relative to the game world, based on popup's position and offset
            var initialHeaderX = this.pos.x + (currentHeaderTextOffset.x || 0);
            var initialHeaderY = this.pos.y + (currentHeaderTextOffset.y || 0);

            this.elements.headerText = ig.game.spawnEntity(
                EntityText, 
                initialHeaderX, 
                initialHeaderY, 
                headerTextEntitySettings
            );
            // --- End Header Text Initialization ---

            this.tweenEnter(); // Start enter animation
            ig.game.sortEntitiesDeferred(); // Sort entities by zIndex
        },

        enterCb: function () {}, // Callback after enter animation
        exitCb: function () {},  // Callback after exit animation
        onTweenUpdateEnter: function () {}, // Called during tween update
        onTweenUpdateExit: function () {}, // Called during tween update

        tweenEnter: function () {
            this.isVisible = true;
            this.isTweening = true;

            var origPosY = this.pos.y;
            this.pos.y += this.tweenPosY; // Start from below

            var tweenObj = { popupAlpha: this.popupAlpha, posY: this.pos.y };

            // Tween for alpha
            new ig.TweenDef(tweenObj)
            .to({ popupAlpha: 1 }, 300)
            .onUpdate(function () {
                this.popupAlpha = tweenObj.popupAlpha;
                this.updateElementsAlpha(this.popupAlpha);
            }.bind(this))
            .onComplete(function () { // Ensure final alpha is set
                this.popupAlpha = 1;
                this.updateElementsAlpha(this.popupAlpha);
            }.bind(this))
            .start();

            // Tween for position
            new ig.TweenDef(tweenObj)
            .easing(ig.Tween.Easing.Back.EaseOut)
            .to({ posY: origPosY }, 300)
            .onUpdate(function () {
                this.pos.y = tweenObj.posY;
                this.updateElementsPosition();
            }.bind(this))
            .onComplete(function () {
                this.pos.y = origPosY; // Ensure final position
                this.updateElementsPosition();
                this.isTweening = false;
                if (typeof this.enterCb === 'function') {
                    this.enterCb();
                }
            }.bind(this))
            .start();
        },

        tweenExit: function () {
            this.isVisible = false;
            this.isTweening = true;

            var tweenObj = { popupAlpha: this.popupAlpha, posY: this.pos.y };
            var targetPosY = this.pos.y + this.tweenPosY; // Target position when exiting

            // Tween for alpha
            new ig.TweenDef(tweenObj)
            .to({ popupAlpha: 0 }, 200)
            .onUpdate(function () {
                this.popupAlpha = tweenObj.popupAlpha;
                this.updateElementsAlpha(this.popupAlpha);
            }.bind(this))
            .onComplete(function () { // Ensure final alpha is set
                this.popupAlpha = 0;
                this.updateElementsAlpha(this.popupAlpha);
            }.bind(this))
            .start();

            // Tween for position
            new ig.TweenDef(tweenObj)
            .easing(ig.Tween.Easing.Back.EaseIn)
            .to({ posY: targetPosY }, 300)
            .onUpdate(function () {
                this.pos.y = tweenObj.posY;
                this.updateElementsPosition();
            }.bind(this))
            .onComplete(function () {
                this.pos.y = targetPosY; // Ensure final position
                this.updateElementsPosition();
                this.isTweening = false;
                if (typeof this.exitCb === 'function') {
                    this.exitCb();
                }
                this.delayedCall(0.05, this.kill.bind(this)); // Kill after animation
            }.bind(this))
            .start();
        },

        updateElementsAlpha: function (alpha) {
            // Update alpha for the popup background animation
            if (this.currentAnim) {
                this.currentAnim.alpha = alpha;
            }

            // Update alpha for child elements that have an updateAlpha method
            if (this.elements.overlay && typeof this.elements.overlay.updateAlpha === 'function') {
                this.elements.overlay.updateAlpha(alpha);
            }
            if (this.elements.buttonClose && typeof this.elements.buttonClose.updateAlpha === 'function') {
                this.elements.buttonClose.updateAlpha(alpha);
            }
            if (this.elements.headerText && typeof this.elements.headerText.updateAlpha === 'function') {
                this.elements.headerText.updateAlpha(alpha);
            }
        },

        updateElementsPosition: function () {
            // Update position for the close button
            if (this.elements.buttonClose) {
                this.elements.buttonClose.pos.x = this.pos.x + this.size.x - this.elements.buttonClose.size.x - 10;
                this.elements.buttonClose.pos.y = this.pos.y + 10;
            }

            // Update position for the header text entity
            if (this.elements.headerText) {
                // Get the effective offset (could have been overridden by settings)
                var currentHeaderTextOffset = (this.settings && this.settings.headerTextOffset) ? this.settings.headerTextOffset : this.headerTextOffset;
                this.elements.headerText.pos.x = this.pos.x + (currentHeaderTextOffset.x || 0);
                this.elements.headerText.pos.y = this.pos.y + (currentHeaderTextOffset.y || 0);
            }
        },

        kill: function () {
            // Kill all child elements
            if (this.elements.overlay) this.elements.overlay.kill();
            if (this.elements.buttonClose) this.elements.buttonClose.kill();
            if (this.elements.headerText) this.elements.headerText.kill();
            
            this.elements = {}; // Clear elements object
            this.parent(); // Call parent's kill method
        },

        draw: function () {
            // The popup background is drawn by this.parent() if currentAnim is set
            // Alpha for the background is handled by this.currentAnim.alpha
            
            var ctx = ig.system.context;
            ctx.save();
            // Apply shadow - this shadow will be for the popup background
            // The globalAlpha for the popup background itself is handled by its animation's alpha.
            // If we want the shadow to also fade, this.popupAlpha should influence shadow's alpha.
            ctx.shadowColor = 'rgba(0, 0, 0, ' + (0.5 * this.popupAlpha) + ')'; // Shadow fades with popup
            ctx.shadowBlur = 20;
            ctx.shadowOffsetX = 5;
            ctx.shadowOffsetY = 5;
            
            this.parent(); // Draws the this.currentAnim (popup background)
            
            ctx.restore();

            // Child elements (overlay, button, headerText) are separate entities and will be drawn
            // by the ImpactJS game loop according to their zIndex and own draw methods.
            // No need to draw them explicitly here.
        },
        
        update: function () {
            this.parent();
            // Any specific update logic for the popup itself
        },

        repos: function () {
            // Recalculate position, typically to center on screen
            if (!this.isTweening) { // Only reposition if not currently animating its position
                this.pos.x = ig.system.width * 0.5 - this.size.x * 0.5 + ig.game.screen.x;
                this.pos.y = ig.system.height * 0.5 - this.size.y * 0.5 + ig.game.screen.y;
                this.updateElementsPosition(); // Update children positions when popup repositions
            }
        }
    });

    EntityPopupOverlay = ig.Entity.extend({
        name: 'popup-overlay',
        size: { x: 0, y: 0 },
        zIndex: 999,
        alpha: 0,
        displayOverlay: true,
        opacityCap: 0.6,

        init: function (x, y, settings) {
            this.parent(0, 0, settings); // Position is always 0,0 relative to screen
            if (settings.displayOverlay !== undefined) {
                this.displayOverlay = settings.displayOverlay;
            }
            if (settings.zIndex) {
                this.zIndex = settings.zIndex;
                this._zIndex = settings.zIndex;
            }
            this.size = { x: ig.system.width, y: ig.system.height };
            if (settings.alpha !== undefined) {
                 this.alpha = settings.alpha;
            }
        },

        updateAlpha: function (newAlpha) {
            // Overlay alpha can be capped, e.g., max 0.6 opacity
            this.alpha = Math.min(this.opacityCap, newAlpha); 
        },

        show: function () {
            this.alpha = this.opacityCap;
            this.zIndex = this._zIndex;
            ig.game.sortEntitiesDeferred();
        },

        hide: function () {
            this.alpha = 0;
            this._zIndex = this.zIndex;
            this.zIndex = -1;
            ig.game.sortEntitiesDeferred();
        },

        draw: function () {
            if (!this.displayOverlay || this.alpha <= 0) return;
            
            var ctx = ig.system.context;
            ctx.save();
            ctx.fillStyle = 'rgba(0, 0, 0, ' + this.alpha + ')';
            ctx.fillRect(this.pos.x - ig.game.screen.x, this.pos.y - ig.game.screen.y, this.size.x, this.size.y);
            ctx.restore();
        },

        update: function () {
            // Ensure the overlay always covers the screen and follows the camera
            this.pos.x = ig.game.screen.x;
            this.pos.y = ig.game.screen.y;
            this.size.x = ig.system.width;
            this.size.y = ig.system.height;
            this.parent();
        }
    });
        
    EntityButtonClosePopup = EntityButtonImage.extend({
        name: 'button-close-popup',
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-exit.png'), frameCountX: 1, frameCountY: 1 },
        popupEntity: null,
        singleClickOnly: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings); // Call parent's init (e.g., EntityButtonImage)
            
            if (settings.popupEntity) {
                this.popupEntity = settings.popupEntity;
            }
             if (settings.zIndex) { // zIndex passed by spawner (EntityPopupBase)
                this.zIndex = settings.zIndex;
            }
            if (settings.alpha !== undefined) { // Alpha passed by spawner
                 this.alpha = settings.alpha;
            } else {
                this.alpha = 0; // Start transparent if not specified, will be updated by popup
            }
        },

        // This method would be called by the button's interaction logic (e.g., on click)
        onClickCallback: function () {
            if (this.popupEntity) {
                this.popupEntity.tweenExit(); // Tell the popup to close
            }
        },

        // updateAlpha is inherited or defined in EntityButtonImage/base
        // If not, define it:
        updateAlpha: function (newAlpha) {
            this.alpha = newAlpha;
            if (this.currentAnim) this.currentAnim.alpha = newAlpha;
        }
    });
});