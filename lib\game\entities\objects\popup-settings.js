ig.module('game.entities.objects.popup-settings')
.requires(
    'plugins.utils.objects.popup-base',
    'game.entities.text',
    'game.entities.buttons.button-audio-sfx',
    'game.entities.buttons.button-audio-bgm',
    'game.entities.buttons.button-main-menu',
    'game.entities.buttons.button-replay'
)
.defines(function () {

    EntityPopupSettings = EntityPopupBase.extend({
        name: 'popup-settings',

        // --- Configuration for the Settings Popup ---

        // Override default header text settings from EntityPopupBase
        headerTextConfig: {
            text: _STRINGS['Game']['Settings'],
            fontSize: 120,
            fontFamily: 'bebasneue-bold',
            fontColor: '#FFFFFF',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },
        

        // Offset for the body text relative to the popup's center
        headerTextOffset: { x: 0, y: 60 },

        // --- Popup Properties ---
        displayOverlay: false,
        hasCloseButton: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.elements.buttons = {};
            this.elements.buttons.sound = this.spawnEntity(
                EntityButtonAudioSFX,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.bgm = this.spawnEntity(
                EntityButtonAudioBGM,
                this.pos.x,
                this.pos.y
            );

            ig.game.sortEntitiesDeferred();
        },

        updateElementsAlpha: function (alpha) {
            this.parent(alpha); // Call parent method to update header, close button, overlay

            if (this.elements.buttons) {
                for (var key in this.elements.buttons) {
                    if (this.elements.buttons[key] && typeof this.elements.buttons[key].updateAlpha === 'function') {
                        this.elements.buttons[key].updateAlpha(alpha);
                    }
                }
            }
        },

        updateElementsPosition: function () {
            this.parent(); // Call parent method to update header, close button

            if (this.elements.buttons) {
                var padding = 60;
                this.elements.buttons.sound.pos.x = this.pos.x + (this.size.x * 0.5) - (this.elements.buttons.sound.size.x * 0.5);
                this.elements.buttons.sound.pos.y = this.pos.y + this.size.y * 0.5 - padding * 1.2;

                this.elements.buttons.bgm.pos.x = this.elements.buttons.sound.pos.x;
                this.elements.buttons.bgm.pos.y = this.elements.buttons.sound.pos.y + this.elements.buttons.sound.size.y + padding;

                this.elements.buttons.sound.textEntity._updateAnchorPosition();
                this.elements.buttons.bgm.textEntity._updateAnchorPosition();
            }

            if (this.elements.headerText) this.elements.headerText._updateAnchorPosition();
        },

        exitCb: function () {
            ig.currentCtrl.tweenShow();
        },

        kill: function () {
            if (this.elements.buttons) {
                this.elements.buttons.sound.kill();
                this.elements.buttons.bgm.kill();                
            }
            // Kill other custom buttons if added
            this.parent();
        },

        update: function () {
            this.parent();
        }
    });

    EntityPopupSettingsGame = EntityPopupBase.extend({
        name: 'popup-settings-game',

        // --- Configuration for the Settings Game Popup ---

        // Header text configuration, same as popup-settings
        headerTextConfig: {
            text: _STRINGS['Game']['Paused'],
            fontSize: 120,
            fontFamily: 'bebasneue-bold',
            fontColor: '#FFFFFF',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },
        
        // Offset for the header text relative to the popup's top
        headerTextOffset: { x: 0, y: 60 },

        // --- Popup Properties ---
        displayOverlay: true,
        opacityCap: 0.4,
        hasCloseButton: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.elements.buttons = {};

            this.elements.buttons.sound = this.spawnEntity(
                EntityButtonAudioSFX,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.bgm = this.spawnEntity(
                EntityButtonAudioBGM,
                this.pos.x,
                this.pos.y
            );

            this.elements.buttons.menu = this.spawnEntity(
                EntityButtonMainMenu,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.replay = this.spawnEntity(
                EntityButtonReplay,
                this.pos.x,
                this.pos.y
            );

            ig.game.sortEntitiesDeferred();
        },

        updateElementsAlpha: function (alpha) {
            this.parent(alpha); // Update base elements (header, close button, overlay)

            // Update all buttons
            if (this.elements.buttons) {
                for (var key in this.elements.buttons) {
                    if (this.elements.buttons[key] && typeof this.elements.buttons[key].updateAlpha === 'function') {
                        this.elements.buttons[key].updateAlpha(alpha);
                    }
                }
            }
        },

        updateElementsPosition: function () {
            this.parent(); // Update base elements (header, close button)

            if (this.elements.buttons) {
                // Position the audio buttons
                var audioPadding = 60;
                this.elements.buttons.sound.pos.x = this.pos.x + (this.size.x * 0.5) - (this.elements.buttons.sound.size.x * 0.5);
                this.elements.buttons.sound.pos.y = this.pos.y + this.size.y * 0.5 - audioPadding * 1.8;

                this.elements.buttons.bgm.pos.x = this.elements.buttons.sound.pos.x;
                this.elements.buttons.bgm.pos.y = this.elements.buttons.sound.pos.y + this.elements.buttons.sound.size.y + audioPadding;

                // Position the navigation buttons
                var navPadding = 20;
                this.elements.buttons.menu.pos.x = this.pos.x + (this.size.x * 0.5) - (this.elements.buttons.menu.size.x) - navPadding;
                this.elements.buttons.menu.pos.y = this.pos.y + this.size.y * 0.8;

                this.elements.buttons.replay.pos.x = this.pos.x + (this.size.x * 0.5) + navPadding;
                this.elements.buttons.replay.pos.y = this.elements.buttons.menu.pos.y;
                
                // Update anchor positions for all button text entities
                this.elements.buttons.sound.textEntity._updateAnchorPosition();
                this.elements.buttons.bgm.textEntity._updateAnchorPosition();
                this.elements.buttons.menu.textEntity._updateAnchorPosition();
                this.elements.buttons.replay.textEntity._updateAnchorPosition();
            }

            if (this.elements.headerText) {
                this.elements.headerText._updateAnchorPosition();
            }
        },

        exitCb: function () {
            ig.currentCtrl.resumeGame();
        },

        kill: function () {
            // Kill all buttons
            if (this.elements.buttons) {
                this.elements.buttons.sound.kill();
                this.elements.buttons.bgm.kill();
                this.elements.buttons.menu.kill();
                this.elements.buttons.replay.kill();                
            }
            this.parent(); // Kill base elements
        },

        update: function () {
            this.parent();
        }
    });
});
