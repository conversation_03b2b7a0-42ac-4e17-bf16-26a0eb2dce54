ig.module('game.data.level-data')
.requires(
    'impact.impact',
    'game.levels.level1',
    'game.levels.level2',
    'game.levels.level3'
)
.defines(function (){
    LevelData = ig.Class.extend({
        // Static-like property for collision types
        statics: {
            COLLISION_TYPES: {
                NONE: 0,         // Default or unclassified
                BUILDING: 1,     // Impassable structures like buildings
                BOUNDARY: 2,     // Defines the edges of the playable level area
                PARKING_SLOT: 3, // Designated areas where trucks aim to park
                TRUCK: 4         // Represents the truck entities themselves
            }
        },

        levels: {}, // Store level data objects { 'level1': Level1MapData, ... }

        mapImageList: {
            "map-1": new ig.Image('media/graphics/sprites/maps/map-1.png'),
            "map-2": new ig.Image('media/graphics/sprites/maps/map-2.png'),
            "map-3": new ig.Image('media/graphics/sprites/maps/map-3.png')
        },
        init: function () {
            // Populate the levels map with data from required level files.
            this.levels['level1'] = Level1MapData;
            this.levels['level2'] = Level2MapData;
            this.levels['level3'] = Level3MapData;
            // To add more levels:
            // 1. Require the level's data file (e.g., 'game.levels.level2').
            // 2. Add it to this.levels map (e.g., this.levels['level2'] = Level2MapData;).

            // Abstract collision types to LevelData
            LevelData.COLLISION_TYPES = this.statics.COLLISION_TYPES;

            console.log("LevelData manager initialized with level IDs:", Object.keys(this.levels));
        },

        /**
         * Retrieves the raw data object for a specific level.
         * @param {string} levelId - The identifier for the level (e.g., "level1").
         * @returns {Object|null} The level data object, or null if not found.
         */
        getLevelData: function (levelId) {
            if (!this.levels[levelId]) {
                console.error("LevelData Error: No data found for levelId:", levelId);
                return null;
            }
            return this.levels[levelId];
        },

        /**
         * Retrieves the preloaded image object for a given level's map.
         * The image key (filename) is specified in the level's data file (e.g., data.image).
         * This image must be preloaded and available in `ig.game.mapImageList` at runtime.
         * @param {string} levelId - The identifier for the level.
         * @returns {Image|null} The Image object for the level's map, or null if not found or not preloaded.
         */
        getMapImage: function (levelId) {
            var data = this.getLevelData(levelId);
            // Ensure ig.game and its mapImageList are available, as this might be called before game fully initialized.
            if (data && data.image && ig.game && this.mapImageList && this.mapImageList[data.image]) {
                return this.mapImageList[data.image];
            } else {
                console.warn("LevelData Warning: Could not retrieve map image for levelId:", levelId,
                             ". Image key:", data ? data.image : 'N/A',
                             ". Check if image is preloaded in this.mapImageList.");
                return null;
            }
        }
    });
});
