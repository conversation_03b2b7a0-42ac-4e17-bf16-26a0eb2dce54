ig.module(
    'game.entities.text'
)
.requires(
    'plugins.utils.entity-extended',
    'plugins.utils.text-to-canvas'
)
.defines(function () {

    EntityText = ig.EntityExtended.extend({
        size: { x: 1, y: 1 }, 
        alpha: 1, 
        visualScale: 1, 
        gravityFactor: 0,
        collides: ig.Entity.COLLIDES.NEVER,

        useOffscreenCanvas: true, 
        offCanvas: null,
        offCanvasCTX: null,
        imageBitmap: null,
        needsRedraw: true,      

        _anchorTargetEntity: null,
        _targetAnchorFactor: { x: 0, y: 0 }, 
        _selfAnchorFactor: { x: 0, y: 0 },   
        _anchorPixelOffset: { x: 0, y: 0 },  
        _isAnchored: false,

        entityTextConfig: {
            text: 'Text Entity',
            fontSize: 16, 
            fontFamily: 'Arial',
            fontColor: '#FFFFFF', 
            width: 100,          
            height: 30,          
            align: 'left',       
            vAlign: 'top',       
            strokeColor: '#000000',
            strokeWidth: 0,
            justify: false,
            inferWhitespace: true, 
            overflow: true,      // Default to true to allow text to use allowance
            debug: false,
            // --- Shadow Properties ---
            shadowEnabled: false,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
            shadowBlur: 3,
            shadowColor: 'rgba(0,0,0,0.5)',
            // --- Overflow Allowance ---
            overflowAllowance: 5 // Pixels of padding on each side within the off-screen canvas
        },

        entityTextOffset: { 
            x: 0,
            y: 0
        },

        isInvisible: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            if (!ig.textToCanvas || !ig.TextToCanvasMixin) {
                console.error('TextToCanvas plugin and mixin are required for EntityText');
                return;
            }
            
            if (settings && typeof settings.useOffscreenCanvas !== 'undefined') {
                this.useOffscreenCanvas = settings.useOffscreenCanvas;
            }

            // Initialize textConfig by merging defaults. The mixin handles the priority.
            this.initText(settings ? settings.textConfig : null); 
            
            // Ensure overflowAllowance is a number
            this.textConfig.overflowAllowance = Number(this.textConfig.overflowAllowance) || 0;

            if (settings && settings.textOffset) {
                ig.merge(this.textOffset, settings.textOffset);
            }
            
            if (settings && typeof settings.alpha !== 'undefined') {
                this.alpha = settings.alpha;
            }
            if (settings && typeof settings.visualScale !== 'undefined') {
                this.visualScale = settings.visualScale;
            }

            if (this.useOffscreenCanvas) {
                this.initOffscreenCanvas();
            }
        },

        initOffscreenCanvas: function () {
            if (!this.useOffscreenCanvas) return;
            var hasFullSupport = typeof OffscreenCanvas !== 'undefined' && 
                                 typeof createImageBitmap !== 'undefined' &&
                                 typeof ImageBitmap !== 'undefined';
            
            var allowance = this.textConfig.overflowAllowance * 2; // Total extra width/height

            var canvasWidth = this.textConfig.width + allowance;
            var canvasHeight = this.textConfig.height + allowance;

            if (!hasFullSupport && typeof document !== 'undefined') { 
                this.offCanvas = document.createElement("canvas");
            } else if (hasFullSupport) {
                this.offCanvas = new OffscreenCanvas(canvasWidth, canvasHeight);
            } else {
                this.useOffscreenCanvas = false; return;
            }
            if (!this.offCanvas) { this.useOffscreenCanvas = false; return; }

            this.offCanvas.width = canvasWidth;
            this.offCanvas.height = canvasHeight;
            this.offCanvasCTX = this.offCanvas.getContext("2d");
            if (this.offCanvasCTX.textRendering !== undefined) {  
                this.offCanvasCTX.textRendering = "optimizeSpeed";
            }
        },

        redrawOffscreenCanvas: function () {
            if (!this.useOffscreenCanvas || !this.offCanvasCTX) return;

            var allowance = this.textConfig.overflowAllowance * 2;
            var newCanvasWidth = this.textConfig.width + allowance;
            var newCanvasHeight = this.textConfig.height + allowance;

            if (this.offCanvas.width !== newCanvasWidth || this.offCanvas.height !== newCanvasHeight) {
                this.offCanvas.width = newCanvasWidth;
                this.offCanvas.height = newCanvasHeight;
            }
            this.offCanvasCTX.clearRect(0, 0, this.offCanvas.width, this.offCanvas.height);
            this.drawTextToOffscreen(); 
            this.createImageBitmap(); 
            this.needsRedraw = false;
        },
        
        _applyShadowStyles: function (ctx) {
            if (this.textConfig.shadowEnabled) {
                ctx.shadowOffsetX = this.textConfig.shadowOffsetX || 0;
                ctx.shadowOffsetY = this.textConfig.shadowOffsetY || 0;
                ctx.shadowBlur = this.textConfig.shadowBlur || 0;
                ctx.shadowColor = this.textConfig.shadowColor || 'rgba(0,0,0,0)';
            }
        },

        _clearShadowStyles: function (ctx) {
            if (this.textConfig.shadowEnabled) {
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                ctx.shadowBlur = 0;
                ctx.shadowColor = 'rgba(0,0,0,0)';
            }
        },

        drawTextToOffscreen: function () {
             if (!this.textConfig || !this.textConfig.text || !ig.textToCanvas || !this.offCanvasCTX) return;
            
            // renderConfig uses logical width/height for text-to-canvas layout.
            // Text is drawn onto the larger off-screen canvas, offset by overflowAllowance.
            var renderConfig = { 
                x: this.textConfig.overflowAllowance, // Draw text offset by allowance
                y: this.textConfig.overflowAllowance, // Draw text offset by allowance
                width: this.textConfig.width,         // Logical width for layout
                height: this.textConfig.height        // Logical height for layout
            }; 
            // Copy other textConfig properties
            for (var key in this.textConfig) {
                if (this.textConfig.hasOwnProperty(key) && !renderConfig.hasOwnProperty(key)) {
                    renderConfig[key] = this.textConfig[key];
                }
            }
            
            this.offCanvasCTX.save(); 
            this._applyShadowStyles(this.offCanvasCTX);
            
            ig.textToCanvas.drawText(this.offCanvasCTX, this.textConfig.text, renderConfig);
            
            this._clearShadowStyles(this.offCanvasCTX); 
            this.offCanvasCTX.restore(); 
        },

        createImageBitmap: function () { 
            var self = this; 
            if (!self.useOffscreenCanvas || !self.offCanvas) return;
            if (self.imageBitmap && typeof self.imageBitmap.close === 'function') {
                self.imageBitmap.close(); self.imageBitmap = null;
            }
            if (typeof createImageBitmap !== 'undefined') {
                createImageBitmap(self.offCanvas)
                    .then(function (bitmap) { self.imageBitmap = bitmap; })
                    .catch(function (e) { console.error("EntityText: Error creating ImageBitmap.", e); self.imageBitmap = self.offCanvas; });
            } else { self.imageBitmap = self.offCanvas; }
        },

        _parseAnchorSpec: function (spec, defaultFactor) {
            defaultFactor = defaultFactor || { xFactor: 0, yFactor: 0 };
            if (typeof spec === 'string') {
                var s = spec.toLowerCase().replace(/\s+/g, '');
                var factors = { xFactor: 0.5, yFactor: 0.5 }; 
                if (s.indexOf('left') !== -1) factors.xFactor = 0; else if (s.indexOf('right') !== -1) factors.xFactor = 1;
                if (s.indexOf('top') !== -1) factors.yFactor = 0; else if (s.indexOf('bottom') !== -1) factors.yFactor = 1;
                if (s === 'top' || s === 'bottom') factors.xFactor = 0.5; if (s === 'left' || s === 'right') factors.yFactor = 0.5;
                if (s === 'top-left' || s === 'left-top') { factors.xFactor = 0; factors.yFactor = 0; }
                if (s === 'top-center' || s === 'center-top') { factors.xFactor = 0.5; factors.yFactor = 0; }
                if (s === 'top-right' || s === 'right-top') { factors.xFactor = 1; factors.yFactor = 0; }
                if (s === 'left-middle' || s === 'middle-left') { factors.xFactor = 0; factors.yFactor = 0.5; }
                if (s === 'center-middle' || s === 'middle-center' || s === 'center' || s === 'middle') { factors.xFactor = 0.5; factors.yFactor = 0.5; }
                if (s === 'right-middle' || s === 'middle-right') { factors.xFactor = 1; factors.yFactor = 0.5; }
                if (s === 'bottom-left' || s === 'left-bottom') { factors.xFactor = 0; factors.yFactor = 1; }
                if (s === 'bottom-center' || s === 'center-bottom') { factors.xFactor = 0.5; factors.yFactor = 1; }
                if (s === 'bottom-right' || s === 'right-bottom') { factors.xFactor = 1; factors.yFactor = 1; }
                return factors;
            } else if (spec && typeof spec.x === 'number' && typeof spec.y === 'number') {
                return { xFactor: (Math.max(-1, Math.min(1, spec.x)) + 1) / 2, yFactor: (Math.max(-1, Math.min(1, spec.y)) + 1) / 2 };
            }
            return defaultFactor;
        },

        /**
         * Anchors this text entity to another entity.
         * @param {ig.Entity} entity - The target entity to anchor to.
         * @param {Object} targetAnchorSpec - Anchor point on the target entity. E.g., {x: 0, y: 0} for center (-1 to 1 range).
         * @param {String|Object} [selfAlignOrPixelOffset] - If String: self-alignment keyword (e.g., "center-middle").
         * If Object: direct pixel offset {x,y}.
         * Defaults to "top-left" self-alignment if omitted.
         * @param {Object} [pixelOffsetIfSelfAlignString] - Optional pixel offset {x,y} ONLY if selfAlignOrPixelOffset was a string.
         */
        anchorTo: function (targetEntity, options) {
            if (!targetEntity) {
                console.error("EntityText.anchorTo: Target entity is null or undefined.");
                this.unanchor();
                return;
            }

            this._anchorTargetEntity = targetEntity;

            options = options || {};

            // Get targetAnchor from options, default to 'center-middle'
            // _parseAnchorSpec's second arg is the default if the first arg is invalid/missing
            this._targetAnchorFactor = this._parseAnchorSpec(options.targetAnchor, { xFactor: 0.5, yFactor: 0.5 });

            // Get selfAnchor from options, default to 'center-middle'
            this._selfAnchorFactor = this._parseAnchorSpec(options.selfAnchor, { xFactor: 0.5, yFactor: 0.5 });
            
            // Get offset from options, default to {x: 0, y: 0}
            if (options.offset && typeof options.offset.x === 'number' && typeof options.offset.y === 'number') {
                this._anchorPixelOffset = options.offset;
            } else {
                this._anchorPixelOffset = { x: 0, y: 0 };
            }
            
            this._isAnchored = true;
            this._updateAnchorPosition(); 
        },

        unanchor: function () {
            this._isAnchored = false;
            this._anchorTargetEntity = null;
        },

        _updateAnchorPosition: function () {
            if (!this._isAnchored || !this._anchorTargetEntity || this._anchorTargetEntity._killed) {
                if (this._anchorTargetEntity && this._anchorTargetEntity._killed) {
                    this.unanchor(); 
                }
                return;
            }

            var targetPointX = this._anchorTargetEntity.pos.x + (this._anchorTargetEntity.size.x * this._targetAnchorFactor.xFactor);
            var targetPointY = this._anchorTargetEntity.pos.y + (this._anchorTargetEntity.size.y * this._targetAnchorFactor.yFactor);

            var selfOffsetX = this.textConfig.width * this._selfAnchorFactor.xFactor;
            var selfOffsetY = this.textConfig.height * this._selfAnchorFactor.yFactor;

            this.pos.x = targetPointX - selfOffsetX + this._anchorPixelOffset.x;
            this.pos.y = targetPointY - selfOffsetY + this._anchorPixelOffset.y;
        },

        update: function () {
            if (this._isAnchored) {
                this._updateAnchorPosition();
            }
            this.parent();
        },

        draw: function () {
            if (this.alpha <= 0 || !this.textConfig || !this.textConfig.text || this.visualScale === 0) return; 
            
            var ctx = ig.system.context;

            if (this.useOffscreenCanvas) {
                if (this.needsRedraw) { this.redrawOffscreenCanvas(); }
                
                if (!this.imageBitmap && this.offCanvas) { this.imageBitmap = this.offCanvas; } // Fallback
                if (!this.imageBitmap) { this.drawDirectly(ctx); return; } // Further fallback

                ctx.save();
                ctx.globalAlpha = this.alpha;

                // Determine pivotX_in_text_coords and pivotY_in_text_coords
                var pivotX_in_text_coords;
                var pivotY_in_text_coords;
                if (this._isAnchored && this._anchorTargetEntity) {
                    pivotX_in_text_coords = this.textConfig.width * this._selfAnchorFactor.xFactor;
                    pivotY_in_text_coords = this.textConfig.height * this._selfAnchorFactor.yFactor;
                } else {
                    pivotX_in_text_coords = this.textConfig.width / 2;
                    pivotY_in_text_coords = this.textConfig.height / 2;
                }

                // Calculate pivotScreenX and pivotScreenY
                var pivotScreenX = ig.system.getDrawPos(this.pos.x + pivotX_in_text_coords - ig.game.screen.x);
                var pivotScreenY = ig.system.getDrawPos(this.pos.y + pivotY_in_text_coords - ig.game.screen.y);

                // Translate and scale context
                ctx.translate(pivotScreenX, pivotScreenY);
                ctx.scale(this.visualScale, this.visualScale);

                // Draw imageBitmap
                ctx.drawImage(
                    this.imageBitmap,
                    -pivotX_in_text_coords - this.textConfig.overflowAllowance,
                    -pivotY_in_text_coords - this.textConfig.overflowAllowance
                );
                
                ctx.restore();
            } else { 
                this.drawDirectly(ctx); 
            }
        },
        
        drawDirectly: function (context) {
            context.save();
            context.globalAlpha = this.alpha;
            this._applyShadowStyles(context);
            var renderConfig = {};
            for (var key in this.textConfig) {
                if (this.textConfig.hasOwnProperty(key)) { renderConfig[key] = this.textConfig[key]; }
            }
            renderConfig.x = ig.system.getDrawPos(this.pos.x - ig.game.screen.x + this.textOffset.x);
            renderConfig.y = ig.system.getDrawPos(this.pos.y - ig.game.screen.y + this.textOffset.y);
            ig.textToCanvas.drawText(context, this.textConfig.text, renderConfig);
            this._clearShadowStyles(context); 
            context.restore();
        },

        setPosition: function (x, y) {
            this.pos.x = x; this.pos.y = y;
        },

        setTextContent: function (newText) {
            if (typeof newText === 'number') newText = newText.toString();
            if (typeof newText !== 'string') return;
            if (this.textConfig.text === newText) return; 
            this.textConfig.text = newText; 
            this.needsRedraw = true;
        },

        /**
         * Updates text configuration properties.
         * Note: If fontSize is changed, it updates the BASE rendering font size.
         * Visual scaling is handled by setTextScale -> this.visualScale.
         */
        configureText: function (configUpdates) {
            var changed = false; var oldFontSize = this.textConfig.fontSize;
            for (var key in configUpdates) {
                if (configUpdates.hasOwnProperty(key) && this.textConfig[key] !== configUpdates[key]) {
                    changed = true; break;
                }
            }
            if (!changed && configUpdates.width && this.textConfig.width !== configUpdates.width) changed = true;
            if (!changed && configUpdates.height && this.textConfig.height !== configUpdates.height) changed = true;
            if (!changed) return;
            ig.merge(this.textConfig, configUpdates);
            if (configUpdates.fontSize && configUpdates.fontSize !== oldFontSize) {
                this._originalFontSize = this.textConfig.fontSize;
            }
            this.needsRedraw = true;
        },
        
        setTextScale: function (scale) {
            if (this.visualScale === scale) return;
            this.visualScale = scale;
        },

        updateAlpha: function (newAlpha) {
            var clampedAlpha = Math.max(0, Math.min(1, newAlpha));
            if (this.alpha === clampedAlpha) return; 
            this.alpha = clampedAlpha;
        },    
        
        kill: function () {
            if (this.imageBitmap && typeof this.imageBitmap.close === 'function') {
                this.imageBitmap.close(); this.imageBitmap = null;
            }
            this.offCanvas = null; this.offCanvasCTX = null;
            this.unanchor(); 
            this.parent();
        }
    });

    if (ig.TextToCanvasMixin) {
        EntityText.inject(ig.TextToCanvasMixin);
    } else {
        console.error("TextToCanvasMixin not found for EntityText injection.");
    }
});
