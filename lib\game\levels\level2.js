ig.module(
    'game.levels.level2'
)
.defines(function (){

Level2MapData = {
    id: 'level2', // Unique ID for this specific level data object
    title: 'The Port', // Placeholder title
    image: "map-2", // Key to look up the image in mapImageList
    truckSpawnAreas: ['left5', 'bot5', 'right5', 'top6', 'top7', 'top8'],
    exitZones: [
        {
            name: 'top-exit',
            vertices: [{ x: 1485, y: 112 }, { x: 730, y: 108 }, { x: 730, y: 0 }, { x: 1485, y: 0 }] 
        },
        {
            name: 'right-exit',
            vertices: [{ x: 1920, y: 730 }, { x: 1810, y: 730 }, { x: 1810, y: 600 }, { x: 1920, y: 600 }] 
        },
        {
            name: 'bottom-exit',
            vertices: [{ x: 1180, y: 1080 }, { x: 730, y: 1080 }, { x: 730, y: 950 }, { x: 1180, y: 950 }] 
        },
        {
            name: 'left-exit',
            vertices: [{ x: 105, y: 690 }, { x: 0, y: 690 }, { x: 0, y: 540 }, { x: 105, y: 540 }] 
        }
    ],
    buildings: [
        {
            name: 'building1', // Lower Left Building
            type: 1,
            vertices: [{ x: -0.00, y: 942.80 }, { x: 617.86, y: 942.80 }, { x: 617.86, y: 1082.37 }, { x: 0.00, y: 1080.00 }] 
        },
        { 
            name: 'building2', // Middle Building
            type: 1,
            vertices: [{ x: 694.95, y: 473.18 }, { x: 1099.35, y: 296.48 }, { x: 1214.38, y: 554.64 }, { x: 808.80, y: 730.15 }]
        },
        {
            name: 'building-3', // Upper Right Building
            type: 1,
            vertices: [{ x: 1723.53, y: 20.95 }, { x: 1920.00, y: 22.14 }, { x: 1920.40, y: 558.59 }, { x: 1722.74, y: 556.22 }]
        },
        { 
            name: 'side-containers',
            type: 1,
            vertices: [{ x: 26.09, y: 405.58 }, { x: 249.04, y: 394.91 }, { x: 247.86, y: 498.90 }, { x: 27.28, y: 507.20 }]
        },
        { 
            name: 'middle-container',
            type: 1,
            vertices: [{ x: 113.85, y: 412.70 }, { x: 154.17, y: 413.89 }, { x: 154.17, y: 516.69 }, { x: 113.85, y: 516.69 }]
        },
        {
            name: 'small-box-building1',
            type: 1,
            vertices: [{ x: 539.59, y: 923.83 }, { x: 594.14, y: 923.83 }, { x: 595.33, y: 964.97 }, { x: 540.78, y: 966.15 }]
        },
        {
            name: 'stacked-box-building1',
            type: 1,
            vertices: [{ x: 626.16, y: 984.31 }, { x: 702.06, y: 984.31 }, { x: 704.43, y: 1062.21 }, { x: 628.54, y: 1062.21 }]
        },
        {
            name: 'forklift-building-2',
            type: 1,
            vertices: [{ x: 626.16, y: 543.15 }, { x: 689.02, y: 514.69 }, { x: 763.73, y: 688.65 }, { x: 700.88, y: 720.67 }]
        },
        {
            name: 'containers-building-2',
            type: 1,
            vertices: [{ x: 1127.81, y: 324.94 }, { x: 1168.13, y: 309.52 }, { x: 1253.51, y: 503.64 }, { x: 1212.01, y: 517.88 }]
        },
        {
            name: 'boxes-building-3',
            type: 1,
            vertices: [{ x: 1563.04, y: 3.56 }, { x: 1759.90, y: 3.56 }, { x: 1758.72, y: 99.25 }, { x: 1565.41, y: 102.80 }]
        },
        {
            name: 'crane-top',
            type: 1,
            vertices: [{ x: 304.78, y: 310.71 }, { x: 383.05, y: 263.27 }, { x: 436.42, y: 344.73 }, { x: 358.15, y: 392.17 }]
        },
        {
            name: 'water-top-1',
            type: 1,
            vertices: [{ x: 0.00, y: 0.00 }, { x: 645.14, y: -1.19 }, { x: 394.32, y: 253.60 }, { x: 243.11, y: 354.22 }, { x: 0.00, y: 362.52 }]
        },
        // {
        //     name: 'water-top-2',
        //     type: 1,
        //     vertices: [{ x: 573.98, y: 28.46 }, { x: 775.59, y: -42.69 }, { x: 802.87, y: -0.37 }, { x: 595.33, y: 82.64 }]
        // },
        // {
        //     name: 'water-top-3',
        //     type: 1,
        //     vertices: [{ x: 786.26, y: -33.21 }, { x: 947.55, y: -32.02 }, { x: 946.36, y: 0.82 }, { x: 786.26, y: 2.00 }]
        // },
        {
            name: 'water-bot-1',
            type: 1,
            vertices: [{ x: 1244.50, y: 972.45 }, { x: 1320.99, y: 910.07 }, { x: 1416.40, y: 884.34 }, { x: 1594.08, y: 870.05 }, { x: 1795.94, y: 831.24 }, { x: 1921.19, y: 768.47 }, { x: 1920.00, y: 1080.00 }, { x: 1223.87, y: 1081.19 }]
        },
        // --- Parking Slots ---
        {
            name: 'parking-slot-1-building1',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 125.71, y: 707.52 }, { x: 226.27, y: 707.76 }, { x: 224.61, y: 940.30 }, { x: 125.94, y: 940.06 }],
            color: GameColors.RED,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            }
            // Swap the frontPoint and backPoint if the automatic calculation is incorrect.
            // swapPoints: true
            // To manually setup the points, uncomment the following lines and adjust the coordinates as needed.
            // frontPoint: { x: 1585.89, y: 741.57 },
            // backPoint: { x: 1586.68, y: 972.52 }
        },
        {
            name: 'parking-slot-2-building1',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 337.27, y: 707.99 }, { x: 437.37, y: 708.23 }, { x: 437.13, y: 942.20 }, { x: 337.04, y: 941.48 }],
            color: GameColors.BLUE,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            }
        },
        {
            name: 'parking-slot-1-building2',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 748.79, y: 173.74 }, { x: 840.58, y: 134.84 }, { x: 932.25, y: 349.24 }, { x: 842.24, y: 388.25 }],
            color: GameColors.GREEN,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            }
        },
        {
            name: 'parking-slot-2-building2',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 961.66, y: 662.33 }, { x: 1053.45, y: 621.66 }, { x: 1140.38, y: 823.01 }, { x: 1047.40, y: 863.81 }],
            color: GameColors.YELLOW,
            colorIndicator: {
                anchor: { x: 0.5, y: 0 },
                anchorOffset: { x: 0, y: 0 }
            }
        },
        {
            name: 'parking-slot-1-building3',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 1489.39, y: 235.60 }, { x: 1489.87, y: 134.64 }, { x: 1723.85, y: 134.98 }, { x: 1722.78, y: 234.09 }],
            color: GameColors.PURPLE,
            colorIndicator: {
                anchor: { x: 0, y: 0.5 },
                anchorOffset: { x: 0, y: 0 }
            }
        },
        {
            name: 'parking-slot-2-building3',
            type: 3,
            collidesWith: [4],
            vertices: [{ x: 1489.79, y: 446.30 }, { x: 1489.47, y: 346.13 }, { x: 1722.66, y: 346.08 }, { x: 1723.18, y: 445.97 }],
            color: GameColors.ORANGE,
            colorIndicator: {
                anchor: { x: 0, y: 0.5 },
                anchorOffset: { x: 0, y: 0 }
            }
        }
        // Add more collision objects here as needed
    ]
};

});
