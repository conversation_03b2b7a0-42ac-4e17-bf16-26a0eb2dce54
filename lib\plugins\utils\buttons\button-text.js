ig.module('plugins.utils.buttons.button-text')
.requires(
	'plugins.utils.buttons.button-base'
)
.defines(function () {
	EntityButtonText = EntityButtonBase.extend({
        /**
         * Configuration options for text rendering
         * @property {object} textConfigs
         * @property {number} textConfigs.width - Width of the text box
         * @property {number} textConfigs.height - Height of the text box
         * @property {number} [textConfigs.x=0] - X position of the text box
         * @property {number} [textConfigs.y=0] - Y position of the text box
         * @property {string} [textConfigs.align='center'] - Text align (left, center, right)
         * @property {string} [textConfigs.vAlign='middle'] - Text vertical align (top, middle, bottom)
         * @property {string} [textConfigs.fontFamily='Arial'] - Base font family of the text
         * @property {number} [textConfigs.fontSize=14] - Base font size of the text in px
         * @property {string} [textConfigs.fontStyle=''] - Base font style, same as css font-style
         * @property {string} [textConfigs.fontVariant=''] - Base font variant, same as css font-variant
         * @property {string} [textConfigs.fontWeight='400'] - Base font weight, same as css font-weight
         * @property {string} [textConfigs.fontColor='black'] - Base font color, same as css color
         * @property {string} [textConfigs.strokeColor='black'] - Base stroke color, same as css color
         * @property {number} [textConfigs.strokeWidth=0] - Base stroke width. Positive number; <=0 means none
         * @property {boolean} [textConfigs.justify=false] - Justify text if true
         * @property {boolean} [textConfigs.inferWhitespace=true] - If whitespace in the text should be inferred
         * @property {boolean} [textConfigs.overflow=true] - Allows text to overflow out of the box
         * @property {boolean} [textConfigs.debug=false] - Draws border and alignment lines for debugging
         * 
         * Note: To force a new line, use '\n' in the text string
         */
		textConfigs: {
            fontSize: 24,             // REQUIRED
            fontFace: 'Arial',        // REQUIRED
            fontColor: '#000',        // REQUIRED
            text: 'ButtonBase\nText', // REQUIRED
            width: 0,                 // REQUIRED
            height: 0,                // REQUIRED
            align: 'center',
            vAlign: 'middle',
            strokeColor: '#000',
            strokeWidth: 0,
            justify: false,
            inferWhitespace: true,
            overflow: true,
            debug: false
        },
		// Text position offset
		// Note: This moves the whole text box, not individual lines
        textPosOffset: {
            x: 0,
            y: 0
        },

		init: function (x, y, settings) {
			if (settings.idleSheetInfo) this.idleSheetInfo = settings.idleSheetInfo;
			this.parent(x, y, settings);
		},
		onClickCallback: function () {}

		/** Available methods (check parent class for more button-base):
		*	// Updates the text displayed on the button.
		*	// @param {string} text - The new text to display.
		*	updateText: function (text) {
		*		if (text) {
		*			this.textConfigs.text = text;
		*		}
		*	},
		*
		*	// Updates the text configuration and regenerates the text canvas.
		*	// @param {object} config - The new text configuration.
		*	updateTextConfig: function (config) {
		*		ig.merge(this.textConfigs, config);
		*		// Update stored font size
		*		this.textConfigs._fontSize = this.textConfigs.fontSize;
		*	},
		**/
	});
});
