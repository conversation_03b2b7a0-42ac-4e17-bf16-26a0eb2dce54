ig.module('game.entities.buttons.button-audio-sfx')
.requires(
    'plugins.utils.buttons.button-base'
)
.defines(function () {
    "use strict";

    ig.EntityButtonAudioSFX = ig.global.EntityButtonAudioSFX = EntityButtonBase.extend({
        name: 'button-audio-sfx',
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-sfx.png'), frameCountX: 1, frameCountY: 2 },
        buttonTextConfig: {
            fontSize: 72,
            fontFamily: 'bebasneue-bold',
            fontColor: '#2d2d2d',
            text: _STRINGS['Game']['Buttons']['On'],
            align: 'center',
            vAlign: 'bottom'
        },
        hasText: true,
        buttonTextOffset: { x: 0, y: 0 },
        mutedFlag: false,
        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.mutedFlag = ig.game.load('sound') === 0;
            this.on = new ig.Animation(this.idleSheet, 1, [0], true);
            this.off = new ig.Animation(this.idleSheet, 1, [1], true);
            this.currentAnim = this.on;

            this.handleOnLoadAudio();

            ig.game.sortEntitiesDeferred();
        },

        handleAudio: function () {
            if (this.mutedFlag) {
                this.currentAnim = this.on;
				/** volume */
				ig.soundHandler.sfxPlayer.volume(0.5);
				/** save session data */
				ig.game.save('sound', 0.5);
                this.updateButtonLabelText(_STRINGS.Game.Buttons.On);
                this.mutedFlag = false;
            }
            else {
                this.currentAnim = this.off;
				/** volume */
				ig.soundHandler.sfxPlayer.volume(0);
				/** save session data */
				ig.game.save('sound', 0);
                this.updateButtonLabelText(_STRINGS.Game.Buttons.Off);
                this.mutedFlag = true;
            }
            this.needsRedraw = true;
        },

        handleOnLoadAudio: function () {
            if (!this.mutedFlag) {
                this.currentAnim = this.on;
				/** volume */
                this.updateButtonLabelText(_STRINGS.Game.Buttons.On);
				ig.soundHandler.sfxPlayer.volume(0.5);
            }
            else {
                this.currentAnim = this.off;
				/** volume */
                this.updateButtonLabelText(_STRINGS.Game.Buttons.Off);
				ig.soundHandler.sfxPlayer.volume(0);
            }
            this.needsRedraw = true;
        },

        onClickCallback: function () {
            this.handleAudio();
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();
        }
    });
});
