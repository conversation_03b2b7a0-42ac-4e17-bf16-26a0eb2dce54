body {
	background-color: #000;
	color: #fff;
	font-family: sans-serif;
	font-size: 10pt;
	margin: 0px;
	overflow: hidden;
	text-shadow: 0px 1px 1px rgba(0,0,0,0.5);
	-webkit-font-smoothing: antialiased;
	-webkit-user-select: none;
}

h2 {
	margin: 0 0 4px 0;
	padding: 4px 0 4px 6px;
	background-color: #000;
	font-size: 100%;
	color: #555;
	xtext-transform: uppercase;
	xborder-bottom: 1px solid #555;
}

h3 {
	margin: 0;
	font-size: 100%;
	display: block;
}

dt {
	margin: 0;
	padding: 4px 0 0 6px;
	display:inline;
	float:left;
	margin-right:5px;
}

dd {
	margin: 0;
	padding: 2px 0 8px 6px;
}

dl {
	margin:0;
}

div.clear {
	clear: both;
}

label {
	cursor: pointer;
}

/* --- Input ------------------------------------------------------------------ */

input {
	background-color: rgba(0,0,0,0.5);
	border: 1px solid rgb(50,50,50);
	color: #fff;
	margin: 0;
	font-family: sans-serif;
	-webkit-font-smoothing: antialiased;
	font-size: 10pt;
	outline: none;
	text-shadow: 0px 1px 1px rgba(0,0,0,0.5);
	
}

input:focus{
	border: 1px solid rgb(200,200,200);
}

input.text {
	padding: 1px;
	margin: 0;
}

input.number {
	width: 30px;
	text-align: right;
}

input.button {
	font-size: 90%;
	padding-left: 13px;
	padding-right: 13px;
	padding-top: 5px;
	padding-bottom: 5px;
	color: #fff;
	font-weight: bold;
	background-color: rgba(255,255,255,0.1);
	border:none;
	border-top: 1px solid rgba(255,255,255,0.1);
	border-bottom: 1px solid rgba(0,0,0,0.1);
	cursor: pointer;
	-webkit-transition: 0.1s linear;
}

input.button:hover {
	background-color: rgba(255,255,255,0.2);
}

input.button:active {
	background-color: rgba(255,255,255,0.3);
}

input.text#layerName {
	width:140px;
}

input.text#layerTileset {
	width:138px;
}

input#levelSaveAs { margin-right: 10px; }
input#levelLoad { margin-right: 10px; }
input#reloadImages {margin-right: 10px;}

input:disabled {
	background-color: #555;
	color: #888;
}

/* --- Layout ------------------------------------------------------------------ */

#editor {
	margin: 0px;
	position: relative;
}

#canvas {
	image-rendering: optimizeSpeed;
	-webkit-interpolation-mode: nearest-neighbor;
}

#menu {
	width: 200px;
	float: right;
	position:absolute;
	top:0px;
	right:0px;
}

/* --- Layers ------------------------------------------------------------------ */

#layerContainer {
	background-color: rgba(0,0,0,0.95);
	padding-right:2px;
}

#layers {
	max-height: 200px;
	overflow: auto;
}

#layerButtons div.button#buttonAddLayer {
	position:absolute;
	right: 0px;
	top:-5px;
	cursor: pointer;
	padding: 10px;
	color:rgba(255,255,255,0.5);
	font-weight: bold;
	font-size:110%;
	margin-left:1px;
	-webkit-transition: 0.1s linear;
	-webkit-font-smoothing: none;
	z-index:10;
}
#layerButtons div.button#buttonAddLayer:hover { color:rgba(255,255,255,1);}
#layerButtons div.button#buttonAddLayer:active { color:rgba(255,255,255,1); text-shadow:none;}

.layer {
	padding: 6px 4px;
	cursor: pointer;
	-webkit-transition: background-color 0.1s linear;
	border-top: 1px solid rgba(255,255,255,0);
	border-bottom: 1px solid rgba(255,255,255,0);
}

.layer:hover {
	background-color: rgba(255,255,255,0.1);
	border-top: 1px solid rgba(255,255,255,0.1);
	border-bottom: 1px solid rgba(255,255,255,0.1);
}

.layer:active {
	background-color: rgba(255,255,255,0.2);
	border-top: 1px solid rgba(255,255,255,0.2);
	border-bottom: 1px solid rgba(255,255,255,0.2);
}

.layerActive { 
	background-color: rgba(255,255,255,0.1); 
	border-top: 1px solid rgba(255,255,255,0.2);
	border-bottom: 1px solid rgba(255,255,255,0.2) !important;
}


#layerEntities { border-bottom: 1px solid #000; }

.layer .visible {
	background-color: rgba(255,255,255,0.2); 
	text-indent: -99999px; 
	display: inline-block;
	width: 10px; 
	height: 10px; 
	margin-right: 7px; 
	margin-left: 4px; 
	-webkit-transition: 0.1s linear;
}

.layer .visible.specialVis{
	margin-right: 2px;
}

.layer .checkedVis{ background-color: rgba(255,255,255,1); }
.layer span.size { font-size: 75%; color: rgba(255,255,255,0.7); }

#layerSettings {
	background-color: rgba(0,0,0,0.95);
	padding-top: 5px;
	margin-top: 1px;
	display: none;
}

/* --- Entities ------------------------------------------------------------------ */
h3#entityClass {
	border-bottom: 1px solid rgba(255,255,255,0.2);
	padding: 5px;
	padding-left: 10px;
}

#entitySettings {
	background-color: rgba(0,0,0,0.95);
	margin-top: 1px;
	display: none;
}

#entityDefinitions {
	max-height: 220px;
	overflow: auto;
}

div.entityDefinition {
	color: #aaa;
	padding: 2px 0;
	border-bottom: 1px solid rgba(255,255,255,0.2);
	cursor: pointer;
}

div.entityDefinition:hover {
	background-color: rgba(255,255,255,0.1);
}

div.entityDefinition .key {
	width: 50%;
	display: block;
	float: left;
	margin: 0 0px 0 0;
	padding: 0;
	text-align: right;
	color: #fff;
	overflow: hidden;
}

div.entityDefinition .value {
	padding: 0 0 0 2px;
	color: #fff;
}

dl#entityDefinitionInput {
	padding: 8px 0;
}

dl#entityDefinitionInput dt {
	width: 40px;
	display: block;
	float: left;
	margin: 0 4px 0 0;
	padding: 4px 0 0 0;
	text-align: right;
}

dl#entityDefinitionInput dd {
	display: block;
	margin: 0;
	padding: 2px 0;
}

#entityKey, #entityValue {
}

#entityMenu {
	background-color: rgba(0,0,0,0.9);
	display: none;
	position: absolute;
	min-width: 100px;
	max-height:300px;
	overflow-y: scroll;
	z-index: 1000;
}

#entityMenu div {
	padding: 3px;
	padding-left: 8px;
	color: #fff;
	cursor: pointer;
	border-top: 1px solid rgba(255,255,255,0);
	border-bottom: 1px solid rgba(255,255,255,0);
	-webkit-transition: 0.1s linear;
}

#entityMenu div:hover {
	background-color: rgba(255,255,255,0.2);
	border-top: 1px solid rgba(255,255,255,0.2);
	border-bottom: 1px solid rgba(255,255,255,0.2);
}

/* --- Dialogs ------------------------------------------------------------------ */

.selectFileDialog {
	background-color: rgba(0,0,0,0.9);
	border: 1px solid white;
	border-top: 1px solid rgba(255,255,255,0.4);
	display: none;
	position: absolute;
	overflow: hidden;
	-webkit-box-shadow: 0px 0px 10px rgba(0,0,0,1);
	
	max-height: 300px;
	overflow-y: scroll;
}

.selectFileDialog a {
	padding: 4px;
	color: #fff;
	display: block;
	text-decoration: none;
	border-top: 1px solid rgba(255,255,255,0);
	border-bottom: 1px solid rgba(255,255,255,0);
}

.selectFileDialog a:hover {
	background-color: rgba(255,255,255,0.2);
	border-top: 1px solid rgba(255,255,255,0.2);
	border-bottom: 1px solid rgba(255,255,255,0.2);
}

div.modalDialogBackground {
	background-color: rgba(0,0,0,0.7);
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	display: none;
	z-index: 100;
}

div.modalDialogBox {
	width: 300px;
	margin-left: -170px;
	background-color: rgba(0,0,0,0.9);
	border: 1px solid rgb(100,100,100);
	-webkit-box-shadow: 0px 0px 10px rgba(0,0,0,1);
	position: absolute;
	top: 20%;
	left: 50%;
	padding: 20px;
}

div.modalDialogText {
	font-size: 180%;
	font-weight: bold;
}

div.modalDialogButtons {
	margin-top: 20px;
	text-align: right;
}

div.modalDialogButtons input.button {
	min-width: 100px;
	text-align: center;
	margin-left: 10px;
}

input.modalDialogPath {
	margin-top: 20px;
	width: 100%;
	outline: none;
}

input.modalDialogPath:focus {
	outline: none;
	border: 1px solid rgb(100,100,100);
}

#headerMenu {
	position:relative;
	z-index:10;
	height:47px;
	width:100%;
	background: #131314;
	background: -webkit-gradient(linear, left bottom, left top, color-stop(0,#000000), color-stop(1,#2e3033));
	background: -moz-linear-gradient(center bottom, #000000 0%, #2e3033 100%);
	background: -o-linear-gradient(#2e3033, #000000);
}

#headerMenu span.headerTitle {
	display:inline-block;
	font-weight:bold;
	font-size:200%;
	padding-left:20px;
	padding-top:7px;
	color:rgba(0,0,0,0.1);
	text-shadow:0px -1px 0px rgba(255,255,255,0.4);
}

#headerMenu span.unsavedTitle {
	display:inline-block;
	font-weight:bold;
	font-size:240%;
	padding-left:0px;
	color:#cc0000;
	text-shadow:0px 1px 1px rgba(0,0,0,0.1);
}

#headerMenu span.headerFloat {
	float: right;
}

div#zoomIndicator {
	font-weight: bold;
	font-size: 300%;
	position: absolute;
	left: 50px;
	top: 30px;
	color: #fff;
	display: none;
}

input#toggleSidebar {
	width: 200px;
	height: 47px;
	text-indent: -99999px;
	background: url(arrow.png) 50% -37px no-repeat;
	-webkit-transition: 0s linear;
	opacity: 0.25;
	padding: 0px;
}

input#toggleSidebar.active{
	background-position: 50% 10px;
}

input[type="checkbox"] {
	position: relative; 
	margin: 0; 
	border: 0; 
	width: 10px; 
	height: 10px;
	display: inline-block; 
	-webkit-appearance: none; 
	-webkit-transition: 0.1s linear;
}
input[type="checkbox"] { 
	background-color:rgba(255,255,255,0.2); 
}
input[type="checkbox"]:checked { 
	background-color: rgba(255,255,255,1); 
}
input[type="checkbox"]:hover { 
	cursor: pointer; 
}
input[type="checkbox"]:disabled {
	background-color: rgba(255,255,255,0.1); 
}

::-webkit-scrollbar { width: 2px; }
::-webkit-scrollbar-button:start:decrement,
::-webkit-scrollbar-button:end:increment { display: block; height: 2px; }
::-webkit-scrollbar-button:vertical:increment { background-color: transparent; }
::-webkit-scrollbar-track-piece { background-color: rgba(0,0,0,0); }
::-webkit-scrollbar-thumb:vertical { background-color: rgba(255,255,255,1); }
