ig.module('game.entities.objects.parking-slot')
.requires('plugins.utils.entity-extended') // Assuming LevelData and GameColors are globally available
.defines(function (){

    EntityParkingSlot = ig.EntityExtended.extend({
        zIndex: 2,
        name: null,
        vertices: [], 
        shape: null,
        color: null, 
        isOccupied: false,
        type: 3, 
        checkAgainst: ig.Entity.TYPE.NONE,
        collides: ig.Entity.COLLIDES.NEVER, 
        isClickable: true,
        
        // Default color indicator configuration
        colorIndicator: {
            radius: 10
        },

        SNAP_LENGTH_SCALE: 1,
        SNAP_WIDTH_SCALE: 1,

        
        // Points to determine orientation
        frontPoint: null,
        backPoint: null,
        showOrientationPoints: true,
        frontPointColor: '#00FF00', // Green for front
        backPointColor: '#FF0000',   // Red for back
        orientationPointsRadius: 5,
        
        init: function (x, y, settings){
            this.worldShape = null;
            this.parent(x, y, settings); 

            this.name = settings.name;
            this.color = settings.color;
            
            // Apply custom colorIndicator settings if provided
            if (settings.colorIndicator) {
                this.colorIndicator.radius = settings.colorIndicator.radius || this.colorIndicator.radius;
            }

            // Convert world vertices into local vertices
            var worldVertices = settings.vertices;
            if (worldVertices && worldVertices.length > 0) {
                var minX = worldVertices[0].x, minY = worldVertices[0].y;
                var maxX = worldVertices[0].x, maxY = worldVertices[0].y;
                for (var i = 1; i < worldVertices.length; i++) {
                    minX = Math.min(minX, worldVertices[i].x);
                    minY = Math.min(minY, worldVertices[i].y);
                    maxX = Math.max(maxX, worldVertices[i].x);
                    maxY = Math.max(maxY, worldVertices[i].y);
                }
                
                this.pos = { x: minX, y: minY };
                this.size = { x: maxX - minX, y: maxY - minY };
                
                var currentPos = this.pos;
                this.vertices = worldVertices.map(function (v) { 
                    return { x: v.x - currentPos.x, y: v.y - currentPos.y }; 
                });
                
                if (ig.SAT && typeof ig.SAT.Shape === 'function' && typeof ig.SAT.Vector2D === 'function') {
                    this.shape = new ig.SAT.Shape(this.vertices.map(function (v) { 
                        return new ig.SAT.Vector2D(v.x, v.y); 
                    }));
                } else {
                    console.error("EntityParkingSlot: ig.SAT.Shape or ig.SAT.Vector2D is not available for slot: " + this.name);
                    this.shape = { pointList: [] }; 
                }

                if (this.shape && this.shape.pointList && this.shape.pointList.length > 0) {
                    var worldVerticesList = this.shape.pointList.map(function (v) {
                        return new ig.SAT.Vector2D(v.x + this.pos.x, v.y + this.pos.y);
                    }.bind(this));
                    this.worldShape = new ig.SAT.Shape(worldVerticesList);
                } else {
                    this.worldShape = new ig.SAT.Shape([]);
                    console.error("EntityParkingSlot: Could not create worldShape for slot: " + this.name + " due to missing local shape.");
                }
            } else {
                console.error("EntityParkingSlot: No vertices provided for slot: " + this.name);
                this.size = { x: 0, y: 0 };
                this.vertices = [];
                this.shape = { pointList: [] };
                
                this.worldShape = new ig.SAT.Shape([]);
                console.error("EntityParkingSlot: worldShape set to empty due to no vertices for slot: " + this.name);
            }

            if (typeof LevelData !== 'undefined' && LevelData.COLLISION_TYPES && LevelData.COLLISION_TYPES.PARKING_SLOT !== undefined) {
                this.type = LevelData.COLLISION_TYPES.PARKING_SLOT;
            }

            // New geometry calculation
            if (worldVertices && worldVertices.length > 0 && ig.utils && ig.utils.getParkingSlotGeometry) {
                var geometry = ig.utils.getParkingSlotGeometry(worldVertices);
                if (geometry) {
                    this.frontPoint = geometry.frontPoint;
                    this.backPoint = geometry.backPoint;
                    this.angle = geometry.angle; // Initial angle
                } else {
                    console.error("EntityParkingSlot: Could not calculate geometry for " + this.name);
                    this.angle = 0;
                }
            } else {
                this.angle = 0; // Fallback
            }

            // Handle manual overrides and swapping
            if (settings.swapPoints) {
                var temp = this.frontPoint;
                this.frontPoint = this.backPoint;
                this.backPoint = temp;
            }
            if (settings.frontPoint) {
                this.frontPoint = settings.frontPoint;
            }
            if (settings.backPoint) {
                this.backPoint = settings.backPoint;
            }

            // Final angle calculation based on the definitive points
            if (this.frontPoint && this.backPoint) {
                this.angle = Math.atan2(this.frontPoint.y - this.backPoint.y, this.frontPoint.x - this.backPoint.x);
            }
        },

        setOccupied: function (status){
            this.isOccupied = status;
        },
        
        isCurrentlyOccupied: function (){
            return this.isOccupied;
        },

        
        /**
         * Gets the position of the color indicator, which is the front point.
         * @returns {Object} The x and y coordinates of the indicator
         */
        getIndicatorPosition: function () {
            return this.frontPoint;
        },

        /**
         * Gets the front point of the parking slot.
         * @returns {Object} The x and y coordinates of the front point.
         */
        getFrontPoint: function () {
            return this.frontPoint;
        },

        /**
         * Gets the back point of the parking slot.
         * @returns {Object} The x and y coordinates of the back point.
         */
        getBackPoint: function () {
            return this.backPoint;
        },
        
        /**
         * Gets the angle of the parking slot in radians, derived from front and back points.
         * @returns {number} - Angle in radians
         */
        getSlotAngle: function () {
            return this.angle !== undefined ? this.angle : 0;
        },

        // Helper function to check if a point is within a slot's snap rectangle
        _isPointInSlotSnapRect: function (parkingSlot, trajectoryEndpoint) {
            var snapRectLength = parkingSlot.slotLength * this.SNAP_LENGTH_SCALE;
            var snapRectWidth = parkingSlot.slotWidth * this.SNAP_WIDTH_SCALE;
            var tx = trajectoryEndpoint.x - parkingSlot.center.x;
            var ty = trajectoryEndpoint.y - parkingSlot.center.y;
            var cos_theta = Math.cos(-parkingSlot.orientationAngle);
            var sin_theta = Math.sin(-parkingSlot.orientationAngle);
            var localX = tx * cos_theta - ty * sin_theta;
            var localY = tx * sin_theta + ty * cos_theta;

            return Math.abs(localX) <= snapRectLength / 2 && Math.abs(localY) <= snapRectWidth / 2;
        },

        draw: function (){
            this.parent(); 

            var ctx = ig.system.context;
            if (!ctx || !this.shape || !this.shape.pointList || this.shape.pointList.length === 0) {
                return; 
            }

            var screenX = this.pos.x - ig.game.screen.x;
            var screenY = this.pos.y - ig.game.screen.y;

            // Draw the parking slot
            // ctx.save();
            // ctx.fillStyle = "yellow";
            // ctx.globalAlpha = 0.5;
            // ctx.strokeStyle = "black";
            // ctx.lineWidth = 1;
            
            // ctx.beginPath();
            // ctx.moveTo(screenX + this.shape.pointList[0].x, screenY + this.shape.pointList[0].y);
            // for (var i = 1; i < this.shape.pointList.length; i++) {
            //     ctx.lineTo(screenX + this.shape.pointList[i].x, screenY + this.shape.pointList[i].y);
            // }
            // ctx.closePath();
            // ctx.fill();
            // ctx.stroke();
            // ctx.restore();

            // Draw the color indicator using anchor settings
            var indicatorPos = this.getIndicatorPosition();
            var indicatorX = indicatorPos.x - ig.game.screen.x;
            var indicatorY = indicatorPos.y - ig.game.screen.y;
            var indicatorRadius = this.colorIndicator.radius;
            var indicatorFillColor = 'rgba(128, 128, 128, 1)'; 

            if (typeof GameColors !== 'undefined' && this.color !== undefined) {
                switch (this.color) {
                    case GameColors.RED:    indicatorFillColor = 'rgba(255, 0, 0, 1)'; break;
                    case GameColors.BLUE:   indicatorFillColor = 'rgba(0, 0, 255, 1)'; break;
                    case GameColors.GREEN:  indicatorFillColor = 'rgba(0, 255, 0, 1)'; break;
                    case GameColors.YELLOW: indicatorFillColor = 'rgba(255, 255, 0, 1)'; break;
                    case GameColors.PURPLE: indicatorFillColor = 'rgba(128, 0, 128, 1)'; break;
                    case GameColors.ORANGE: indicatorFillColor = 'rgba(255, 165, 0, 1)'; break;
                }
            }
            
            ctx.save();
            ctx.fillStyle = indicatorFillColor;
            ctx.beginPath();
            ctx.arc(indicatorX, indicatorY, indicatorRadius, 0, Math.PI * 2); 
            ctx.fill();
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.restore();

            // Draw the occupied indicator
            if (!ig.game.debugMode) return;
            if (this.isOccupied) {
                var occMinX = screenX; 
                var occMinY = screenY;
                var occMaxX = screenX + this.size.x; 
                var occMaxY = screenY + this.size.y;

                ctx.save();
                ctx.strokeStyle = 'red';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(occMinX, occMinY);
                ctx.lineTo(occMaxX, occMaxY);
                ctx.moveTo(occMaxX, occMinY);
                ctx.lineTo(occMinX, occMaxY);
                ctx.stroke();
                ctx.restore();
            }
            this.drawParkingSlotSnapAreas(); 

            if (this.showOrientationPoints && this.frontPoint && this.backPoint) {
                var ctx = ig.system.context;
                var fpx = this.frontPoint.x - ig.game.screen.x;
                var fpy = this.frontPoint.y - ig.game.screen.y;
                var bpx = this.backPoint.x - ig.game.screen.x;
                var bpy = this.backPoint.y - ig.game.screen.y;

                // Draw line between points
                ctx.beginPath();
                ctx.moveTo(bpx, bpy);
                ctx.lineTo(fpx, fpy);
                ctx.strokeStyle = '#FFFFFF';
                ctx.lineWidth = 1;
                ctx.setLineDash([5, 5]);
                ctx.stroke();
                ctx.setLineDash([]);

                // Draw back point (red)
                ctx.beginPath();
                ctx.arc(bpx, bpy, this.orientationPointsRadius, 0, 2 * Math.PI);
                ctx.fillStyle = this.backPointColor;
                ctx.fill();
                ctx.strokeStyle = '#333333';
                ctx.lineWidth = 1;
                ctx.stroke();

                // Draw front point (green)
                ctx.beginPath();
                ctx.arc(fpx, fpy, this.orientationPointsRadius, 0, 2 * Math.PI);
                ctx.fillStyle = this.frontPointColor;
                ctx.fill();
                ctx.strokeStyle = '#333333';
                ctx.lineWidth = 1;
                ctx.stroke();
            }
        },

        drawParkingSlotSnapAreas: function () {
            var ctx = ig.system.context;
            ctx.save();
            ctx.strokeStyle = 'rgba(255, 0, 255, 0.3)'; 
            ctx.lineWidth = 1;
            if (ctx.setLineDash) ctx.setLineDash([3, 3]);
            var snapRectLength = this.slotData.slotLength * this.SNAP_LENGTH_SCALE;
            var snapRectWidth = this.slotData.slotWidth * this.SNAP_WIDTH_SCALE;
            ctx.save();
            ctx.translate(this.slotData.center.x - ig.game.screen.x, this.slotData.center.y - ig.game.screen.y); 
            ctx.rotate(this.slotData.orientationAngle);
            ctx.beginPath();
            ctx.rect(-snapRectLength / 2, -snapRectWidth / 2, snapRectLength, snapRectWidth);
            ctx.stroke();
            ctx.restore();
            if (ctx.setLineDash) ctx.setLineDash([]);
            ctx.restore();
        }
    });
});
