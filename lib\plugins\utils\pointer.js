/**
 * New Pointer Plugin by Nam
 */
ig.module('plugins.utils.pointer')
.requires(
    'impact.game',
    'impact.entity'
).defines(function () {
    ig.Game.inject({
        update: function () {
            this.parent();

            // Only perform checks if there's a relevant input event
            if (ig.input.pressed('click') || ig.input.state('click') || ig.input.released('click')) {
                // The entity that we think is under the pointer
                var targetObject = null;
                // Set the highest z-index to -Infinity to make sure the first entity we find has the highest z-index
                var highestZIndex = -Infinity;

                // Loop through all entities in reverse order
                for (var i = this.entities.length - 1; i >= 0; i--) {
                    var e = this.entities[i];

                    // Check if the entity is under the pointer
                    // The reason we check for underPointer() is to allow entities to override
                    // this method and change how we determine if they are under the pointer
                    // The reason we check for isinvisible === false is to allow entities to
                    // be exempted from the checking
                    if (!e.isInvisible && e.underPointer()) {
                        // Check if this entity has a higher z-index
                        if (e.zIndex > highestZIndex) {
                            highestZIndex = e.zIndex;
                            if (e.isClickable) {
                                targetObject = e;
                            }
                        }
                    } else if (e.isInvisible) {
                        continue;
                    }
                }

                // If we have a targetObject, then we need to check which event occurred
                if (targetObject) {
                    if (ig.input.pressed('click') && typeof targetObject.clicked === 'function') {
                        targetObject.clicked();
                    }
                    if (ig.input.state('click') && typeof targetObject.clicking === 'function') {
                        targetObject.clicking();
                    }
                    if (ig.input.released('click') && typeof targetObject.released === 'function') {
                        targetObject.released();
                    }
                }
            }
        }
    });

    ig.Entity.inject({
        isClickable: false,
        isInvisible: false,
        /* DEFAULT IS FOR THE IN GAME ENTITIES */
        underPointer: function () {
            var p = ig.game.io.getClickPos();
            var p2 = {
                x: p.x + ig.game.screen.x,
                y: p.y + ig.game.screen.y
            };
            return this.containPoint(p2);
        },

        // underPointer: function() {
        //     // Get click position in world coordinates
        //     var p = ig.game.io.getClickPos();
            
        //     // If we're using screen coordinates (for UI), use them directly
        //     var clickPos = {
        //         x: p.x + ig.game.screen.x,
        //         y: p.y + ig.game.screen.y
        //     };
            
        //     // If the truck is rotated, account for rotation in hit detection
        //     if (this.angle !== 0) {
        //         return this.containsRotatedPointer(clickPos);
        //     } else {
        //         // Use simple rectangle check for non-rotated truck
        //         return clickPos.x >= this.pos.x && 
        //                clickPos.x <= this.pos.x + this.size.x &&
        //                clickPos.y >= this.pos.y && 
        //                clickPos.y <= this.pos.y + this.size.y;
        //     }
        // },

        // containsRotatedPointer: function(point) {
        //     // Get center of rectangle
        //     var centerX = this.pos.x + this.size.x / 2;
        //     var centerY = this.pos.y + this.size.y / 2;
            
        //     // Translate point to origin (relative to rectangle center)
        //     var translatedX = point.x - centerX;
        //     var translatedY = point.y - centerY;
            
        //     // Rotate point in opposite direction of rectangle rotation
        //     var rotatedX = translatedX * Math.cos(-this.angle) - translatedY * Math.sin(-this.angle);
        //     var rotatedY = translatedX * Math.sin(-this.angle) + translatedY * Math.cos(-this.angle);
            
        //     // Check if rotated point is inside rectangle (now axis-aligned)
        //     return Math.abs(rotatedX) <= this.size.x / 2 && 
        //            Math.abs(rotatedY) <= this.size.y / 2;
        // },

        /* FOR THE UI ENTITIES (EG. BUTTONS), ADD THE FOLLOWING METHOD TO THE ENTITY, TO OVERRIDE THE DEFAULT ONE */
        // underPointer: function () {
        //     var p = ig.game.io.getClickPos();
        //     return this.containPoint(p);
        // },

        containPoint: function (p) {
            var x0 = this.pos.x;
            var x1 = x0 + this.size.x;
            var y0 = this.pos.y;
            var y1 = y0 + this.size.y;
            return (p.x >= x0 && p.x < x1 && p.y >= y0 && p.y < y1);
        }
    });
});
