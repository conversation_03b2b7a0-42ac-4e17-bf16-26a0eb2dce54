# -*- coding: utf-8 -*-
"""
 MarketJS Amazon S3 Deployment System
 -----------------------------------------------------------------------
 Copyright (c) 2012 MarketJS Limited. Certain portions may come from 3rd parties and
 carry their own licensing terms and are referenced where applicable. 
 -----------------------------------------------------------------------
"""

import boto,os,re
import getopt, sys

from datetime import datetime
from boto.s3.connection import S3Connection
from boto.s3.key import Key

""" OWN SERVER """
conn = S3Connection(os.environ['AWS_ACCESS_KEY_ID'],os.environ['AWS_SECRET_ACCESS_KEY'],host="s3.ap-southeast-1.amazonaws.com")
# S3 Endpoints: s3.REGION.amazonaws.com https://docs.aws.amazon.com/general/latest/gr/s3.html
# To access, goto http://s3.ap-southeast-1.amazonaws.com/marketjs-lab3/en/game_folder/index.html
BUCKET_NAME = 'marketjs-lab3'
GAME_NAME = os.path.split(os.getcwd())[-1] # same as folder name
LANGUAGE_CODE = None

BUCKET = conn.get_bucket(BUCKET_NAME)
BUCKET_LOCATION = BUCKET.get_location()

def usage():
	print ('Options and arguments:')
	print ('-a --all	:  [uploads everything in folder]')
	
def uploadResultToS3(bucket,game_folder_name,srcDir):
	
	""" GETOPT """
	try:
		opts, args = getopt.getopt(sys.argv[1:], "mhancl:v", ["help","all","new","language"])
	except getopt.GetoptError as err:
		# print help information and exit:
		print (str(err)) # will print something like "option -a not recognized"
		usage()
		sys.exit(2)
	
	""" PARAMS """	
	verbose = False
	upload_all = False
	pushMedia = False
	pushMediaNew = False
	
	""" PARSE OPTS """
	for o, a in opts:
		if o == "-v":
			verbose = True
		elif o in ("-h", "--help"):
			usage()
			sys.exit()
		elif o in ("-n", "--new"):
			pushMediaNew = True
			print ("Upload code and new media")
		elif o in ("-a", "--all"):
			upload_all = True
			print ("upload all set True")		
		elif o in ("-l", "--language"):
			print ("language chosen:" + a)
			LANGUAGE_CODE = a
		elif o == "-m":
			pushMedia = True
		elif o == "-c":
			print ("Upload only codes")
		else:
			assert False, "unhandled option"
	
	""" BOTO """
	b = conn.get_bucket(bucket)
	k = Key(b)
	
	""" WALKING THE BUCKET """
	print ('preparing to walk the bucket named ' + b.name + '...')

	upload(k,b,game_folder_name,srcDir,"index.html",srcDir,LANGUAGE_CODE)
	upload(k,b,game_folder_name,srcDir,"game.js",srcDir,LANGUAGE_CODE)
	upload(k,b,game_folder_name,srcDir,"game.css",srcDir,LANGUAGE_CODE)

	mediaFolder = os.path.join(srcDir,"media")

	if upload_all:
		print ('uploading ALL files in media folder ...')
		for path,dir,files in os.walk(mediaFolder):
			relativePath = os.path.relpath(path, srcDir)
			if relativePath.find("promo") == -1:
				for file in files:			
					upload(k,b,game_folder_name,path,file.decode('utf8'),srcDir,LANGUAGE_CODE)
		
		brandingFolder = os.path.join(srcDir,"branding")
		for path,dir,files in os.walk(brandingFolder):
			for file in files:			
				upload(k,b,game_folder_name,path,file.decode('utf8'),srcDir,LANGUAGE_CODE)
	else:
		if pushMedia:
			""" UPLOAD SETTINGS """
			for path,dir,files in os.walk(mediaFolder):
				relativePath = os.path.relpath(path, srcDir)
				if relativePath.find("promo") == -1:
					for file in files:
						upload(k,b,game_folder_name,path,file.decode('utf8'),srcDir,LANGUAGE_CODE)
		else:
			if pushMediaNew:
				day_freshness = 1
				seconds_freshness = 86400/2
				for path,dir,files in os.walk(mediaFolder):
					relativePath = os.path.relpath(path, srcDir)
					if relativePath.find("promo") == -1:
						for file in files:			
							""" get freshness """
							last_modified_time_epoch_seconds = os.path.getmtime(os.path.join(path,file))
							last_modified_time = datetime.fromtimestamp(last_modified_time_epoch_seconds)
							delta = datetime.now()-last_modified_time
							
							if delta.days < day_freshness and delta.seconds < seconds_freshness:
								upload(k,b,game_folder_name,path,file.decode('utf8'),srcDir,LANGUAGE_CODE)
				
						
def upload(k,b,game_folder_name,path,file,srcDir,language_code):		
	print ('Preparing bucket for upload')
	k.key = language_code + '/' + game_folder_name + "/" + os.path.relpath(os.path.join(path,file),srcDir)
	k.key = re.sub(r'\\', '/', k.key) #added to avoid forward slash in k.key
	print ('sending ' + file + ' to https://s3.' + BUCKET_LOCATION + '.amazonaws.com/'  + b.name + '/' + k.key + ' ...')
	
	headers = {
		'Cache-Control': 'max-age=7776000'
	}
	
	k.set_contents_from_filename(os.path.join(path,file), headers)

	print ('file set as public ...')
	k.set_acl('public-read')						

		
""" CHECK BEFORE RUNNING """
uploadResultToS3(BUCKET_NAME,GAME_NAME, os.getcwd())
