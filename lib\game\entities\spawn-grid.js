/**
 * Spawn Grid Entity
 * 
 * Displays a grid with text labels around the edges.
 * 
 * Optimizations:
 * - Added constants for grid dimensions
 * - Pre-calculated grid lines for better performance
 * - Reduced repetitive code with helper methods
 * - Optimized array operations with pre-allocation
 * - Added caching for expensive calculations
 * - Improved drawing performance
 */
ig.module('game.entities.spawn-grid')
.requires(
	'impact.entity',
    'game.entities.ui.canvas-element',
    'game.entities.ui.drag-point'
)
.defines(function () {
    EntitySpawnGrid = ig.Entity.extend({
        // Constants for grid dimensions
        GRID_CELL_WIDTH: 160,
        GRID_CELL_HEIGHT: 120,
        
        // Pre-calculated values
        HALF_CELL_WIDTH: 80,  // 160/2
        HALF_CELL_HEIGHT: 60, // 120/2
        
        zIndex: 1,

        topText: [
            "topLeft", "top1", "top2", "top3", "top4", "top5", 
            "top6", "top7", "top8", "top9", "top10", "topRight"
        ],

        botText: [
            "botLeft", "bot1", "bot2", "bot3", "bot4", "bot5", 
            "bot6", "bot7", "bot8", "bot9", "bot10", "botRight"
        ],

        leftText: [
            "left1", "left2", "left3", "left4", "left5", "left6", "left7"
        ],

        rightText: [
            "right1", "right2", "right3", "right4", "right5", "right6", "right7"
        ],

        topTextObj: [],
        botTextObj: [],
        leftTextObj: [],
        rightTextObj: [],

        allTextObj: [],
        
        // Pre-calculated grid lines
        gridLinesCache: null,

        isHideDraw: false,

        init: function (x, y, settings){
            this.parent(x, y, settings);

            this.isHideDraw = settings.isHideDraw || false;

            this.ctx = ig.system.context;

            // Pre-calculate grid lines
            this.cacheGridLines();
            
            this.setupText();
            ig.game.sortEntitiesDeferred();
        },
        
        // Cache grid lines for better performance
        cacheGridLines: function () {
            // Pre-allocate arrays with estimated sizes
            var screenWidth = ig.system.width;
            var screenHeight = ig.system.height;
            
            // Calculate number of lines needed
            var verticalLineCount = Math.ceil(screenWidth / this.GRID_CELL_WIDTH);
            var horizontalLineCount = Math.ceil(screenHeight / this.GRID_CELL_HEIGHT);
            
            this.gridLinesCache = {
                vertical: new Array(verticalLineCount),
                horizontal: new Array(horizontalLineCount)
            };
            
            // Calculate vertical lines
            var vIndex = 0;
            var x = this.GRID_CELL_WIDTH;
            while (x < screenWidth) {
                this.gridLinesCache.vertical[vIndex++] = x;
                x += this.GRID_CELL_WIDTH;
            }
            
            // Calculate horizontal lines
            var hIndex = 0;
            var y = this.GRID_CELL_HEIGHT;
            while (y < screenHeight) {
                this.gridLinesCache.horizontal[hIndex++] = y;
                y += this.GRID_CELL_HEIGHT;
            }
            
            // Trim arrays to actual size if needed
            if (vIndex < verticalLineCount) {
                this.gridLinesCache.vertical.length = vIndex;
            }
            
            if (hIndex < horizontalLineCount) {
                this.gridLinesCache.horizontal.length = hIndex;
            }
        },

        draw: function (){
            if (this.isHideDraw == true) return;

            // Cache screen dimensions
            var screenWidth = ig.system.width;
            var screenHeight = ig.system.height;
            
            // Draw semi-transparent black background
            this.ctx.save();
            this.ctx.globalAlpha = 0.5;
            this.ctx.fillStyle = "black";
            this.ctx.fillRect(0, 0, screenWidth, screenHeight);
            this.ctx.restore();
            
            // Draw grid lines
            this.drawGrid();
        },

        drawGrid: function (){
            // Skip drawing if there are no grid lines
            if (!this.gridLinesCache || 
                (this.gridLinesCache.vertical.length === 0 && 
                 this.gridLinesCache.horizontal.length === 0)) {
                return;
            }
            
            this.ctx.save();
            this.ctx.strokeStyle = "white";
            this.ctx.lineWidth = 5;
            this.ctx.beginPath();

            var verticalLines = this.gridLinesCache.vertical;
            var horizontalLines = this.gridLinesCache.horizontal;
            var screenHeight = ig.system.height;
            var screenWidth = ig.system.width;
            
            // Draw vertical lines from cache - using cached length for better performance
            var vertLen = verticalLines.length;
            for (var i = 0; i < vertLen; i++) {
                var x = verticalLines[i];
                this.ctx.moveTo(x, 0);
                this.ctx.lineTo(x, screenHeight);
            }

            // Draw horizontal lines from cache - using cached length for better performance
            var horizLen = horizontalLines.length;
            for (var i = 0; i < horizLen; i++) {
                var y = horizontalLines[i];
                this.ctx.moveTo(0, y);
                this.ctx.lineTo(screenWidth, y);
            }

            this.ctx.stroke();
            this.ctx.closePath();
            this.ctx.restore();
        },

        // Create a text entity with common settings
        createTextEntity: function (text, pivot, posX, posY) {
            // Create settings object once to avoid repeated object creation
            var textSettings = {
                isHide: this.isHideDraw,
                zIndex: this.zIndex + 1,
                pivot: pivot,
                element: {
                    type: "text",
                    settings: {
                        fillStyle: "cyan",
                        text: text,
                        fontFamily: "arial",
                        fontWeight: "bold",
                        fontSize: 30,
                        isResizeByText: true
                    }
                }
            };
            
            // Spawn entity with prepared settings
            var newText = ig.game.spawnEntity(EntityCanvasElement, 0, 0, textSettings);

            // Set position and name
            newText.pos = { x: posX, y: posY };
            newText.name = text;
            
            return newText;
        },

        setupText: function (){
            // Pre-allocate arrays with exact sizes to avoid resizing
            var topCount = this.topText.length;
            var botCount = this.botText.length;
            var leftCount = this.leftText.length;
            var rightCount = this.rightText.length;
            var totalTextCount = topCount + botCount + leftCount + rightCount;
            
            this.topTextObj = new Array(topCount);
            this.botTextObj = new Array(botCount);
            this.leftTextObj = new Array(leftCount);
            this.rightTextObj = new Array(rightCount);
            this.allTextObj = new Array(totalTextCount);
            
            var currentIndex = 0;
            
            // Setup top text
            var x = 0;
            var y = 10;
            for (var i = 0; i < topCount; i++) {
                var el = this.topText[i];
                var newText = this.createTextEntity(
                    el, 
                    "top-center", 
                    x + this.HALF_CELL_WIDTH, 
                    y
                );
                this.topTextObj[i] = newText;
                this.allTextObj[currentIndex++] = newText;
                x += this.GRID_CELL_WIDTH;
            }

            // Setup bottom text
            x = 0;
            y = ig.system.height - 10;
            for (var i = 0; i < botCount; i++) {
                var el = this.botText[i];
                var newText = this.createTextEntity(
                    el, 
                    "bot-center", 
                    x + this.HALF_CELL_WIDTH, 
                    y
                );
                this.botTextObj[i] = newText;
                this.allTextObj[currentIndex++] = newText;
                x += this.GRID_CELL_WIDTH;
            }

            // Setup left text
            x = 10;
            y = this.GRID_CELL_HEIGHT;
            for (var i = 0; i < leftCount; i++) {
                var el = this.leftText[i];
                var newText = this.createTextEntity(
                    el, 
                    "center-left", 
                    x, 
                    y + this.HALF_CELL_HEIGHT
                );
                this.leftTextObj[i] = newText;
                this.allTextObj[currentIndex++] = newText;
                y += this.GRID_CELL_HEIGHT;
            }

            // Setup right text
            x = ig.system.width - 10;
            y = this.GRID_CELL_HEIGHT;
            for (var i = 0; i < rightCount; i++) {
                var el = this.rightText[i];
                var newText = this.createTextEntity(
                    el, 
                    "center-right", 
                    x, 
                    y + this.HALF_CELL_HEIGHT
                );
                this.rightTextObj[i] = newText;
                this.allTextObj[currentIndex++] = newText;
                y += this.GRID_CELL_HEIGHT;
            }
        }

    });

});
