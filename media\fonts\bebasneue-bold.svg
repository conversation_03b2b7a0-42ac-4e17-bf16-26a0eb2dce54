<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20201107 at Mon Sep  3 14:56:20 2018
 By 
Copyright \(c\) 2010 by Ryoichi Tsunekawa. All rights reserved.
</metadata>
<defs>
<font id="BebasNeue-Bold" horiz-adv-x="396" >
  <font-face 
    font-family="Bebas Neue"
    font-weight="700"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 5 0 0 0 0 0 0 0"
    ascent="750"
    descent="-250"
    x-height="700"
    cap-height="700"
    bbox="-137 -179 1000 894"
    underline-thickness="50"
    underline-position="-110"
    unicode-range="U+0020-F6C3"
  />
<missing-glyph horiz-adv-x="548" 
d="M498 700v-700h-448v700h448zM275 420l-63 195h-86l87 -258l-93 -272h77l69 210l68 -210h88l-93 272l87 258h-77z" />
    <glyph glyph-name=".notdef" horiz-adv-x="548" 
d="M498 700v-700h-448v700h448zM275 420l-63 195h-86l87 -258l-93 -272h77l69 210l68 -210h88l-93 272l87 258h-77z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="155" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="155" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="190" 
d="M40 408v292h110v-292l-14 -253h-82zM148 0h-106v106h106v-106z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="325" 
d="M146 700l-16 -206h-75l-15 206h106zM285 700l-16 -206h-75l-15 206h106z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="410" 
d="M154 200l-21 -200h-90l21 200h-49l8 78h49l19 183h-50l8 78h50l17 161h90l-17 -161h65l17 161h90l-17 -161h51l-8 -78h-51l-19 -183h51l-8 -78h-51l-21 -200h-90l21 200h-65zM227 278l19 183h-65l-19 -183h65z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M66.5 636.5q29.5 43.5 86.5 56.5v52h90v-52q58 -12 88 -55.5t30 -115.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -31 12.5 -55.5t30.5 -43t51 -45.5q39 -31 63 -56.5t41 -63t17 -87.5q0 -72 -30.5 -116t-88.5 -56v-51h-90v51q-59 12 -89 56t-30 116
v48h104v-55q0 -68 57 -68t57 68q0 31 -12.5 55.5t-30.5 43t-51 45.5q-39 31 -63 56.5t-41 63t-17 87.5q0 71 29.5 114.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="627" 
d="M61 676q27 29 79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230q0 54 27 83zM481 700l-276 -700h-65l276 700h65zM140 314q36 0 36 44v240q0 44 -36 44t-36 -44v-240q0 -44 36 -44zM408 420q27 29 79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29
t-79 29t-27 83v230q0 54 27 83zM487 58q36 0 36 44v240q0 44 -36 44t-36 -44v-240q0 -44 36 -44z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="414" 
d="M117 370q-39 16 -57.5 51.5t-18.5 92.5v13q0 85 41 129t123 44h126v-100h-124q-27 0 -41.5 -16t-14.5 -52v-43q0 -40 16.5 -57t49.5 -17h48v78h110v-78h29v-100h-29v-230q0 -30 2 -48.5t10 -36.5h-112q-8 22 -10 55q-29 -63 -102 -63q-60 0 -91 41t-31 119v71
q0 113 76 147zM208 92q54 0 57 61v162h-42q-38 0 -55 -20.5t-17 -68.5v-65q0 -69 57 -69z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="180" 
d="M143 700l-16 -206h-75l-15 206h106z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="251" 
d="M235 610h-27q-27 0 -39 -13.5t-12 -49.5v-394q0 -36 12 -49.5t39 -13.5h27v-90h-34q-83 0 -118.5 35t-35.5 118v394q0 83 35.5 118t118.5 35h34v-90z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="251" 
d="M168.5 665q35.5 -35 35.5 -118v-394q0 -83 -35.5 -118t-118.5 -35h-34v90h27q27 0 39 13.5t12 49.5v394q0 36 -12 49.5t-39 13.5h-27v90h34q83 0 118.5 -35z" />
    <glyph glyph-name="asterisk" unicode="*" 
d="M153 700h90l-26 -165l149 75l28 -85l-164 -28l117 -117l-72 -53l-77 148l-77 -148l-72 53l117 117l-164 28l28 85l149 -75z" />
    <glyph glyph-name="plus" unicode="+" 
d="M371 389v-78h-134v-137h-78v137h-134v78h134v135h78v-135h134z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="186" 
d="M40 106h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="270" 
d="M235 400v-100h-200v100h200z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="186" 
d="M146 0h-106v106h106v-106z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="381" 
d="M376 700l-276 -700h-95l276 700h95z" />
    <glyph glyph-name="zero" unicode="0" 
d="M74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="one" unicode="1" 
d="M171.5 623.5q23.5 11.5 35 29.5t22.5 47h74v-700h-110v534h-85v78q40 0 63.5 11.5z" />
    <glyph glyph-name="two" unicode="2" 
d="M243.5 590.5q-14.5 17.5 -42.5 17.5q-57 0 -57 -69v-75h-104v68q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5q0 -59 -19.5 -108.5t-46.5 -85.5t-70 -83q-43 -48 -63 -78t-20 -62q0 -10 1 -15h208v-100h-318v86q0 49 15 87.5t37 67.5t58 68q37 41 58 68.5t35.5 65.5
t14.5 85q0 45 -14.5 62.5z" />
    <glyph glyph-name="three" unicode="3" 
d="M237.5 590.5q-14.5 17.5 -42.5 17.5q-57 0 -57 -69v-45h-104v38q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-18q0 -57 -19 -92.5t-58 -51.5q77 -34 77 -147v-55q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v58h104v-65q0 -69 57 -69q28 0 42.5 17.5t14.5 61.5
v55q0 48 -17 68.5t-55 20.5h-37v100h43q33 0 49.5 17t16.5 57v39q0 45 -14.5 62.5z" />
    <glyph glyph-name="four" unicode="4" 
d="M214 700h120v-473h52v-100h-52v-127h-108v127h-212v100zM226 490l-111 -263h111v263z" />
    <glyph glyph-name="five" unicode="5" 
d="M139 161q0 -68 57 -68t57 68v154q0 69 -57 69t-57 -69v-21h-104l20 406h290v-100h-191l-9 -167q31 51 96 51q60 0 91 -41t31 -119v-156q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v58h104v-65z" />
    <glyph glyph-name="six" unicode="6" 
d="M201 708q80 0 122 -45.5t42 -130.5v-18h-104v25q0 69 -57 69q-31 0 -46 -19t-15 -67v-128q29 62 102 62q60 0 91 -41t31 -119v-128q0 -84 -43 -130t-124 -46t-124 46t-43 130v358q0 182 168 182zM143 287v-126q0 -68 57 -68t57 68v126q0 69 -57 69t-57 -69z" />
    <glyph glyph-name="seven" unicode="7" 
d="M363 604l-162 -604h-110l161 600h-219v100h330v-96z" />
    <glyph glyph-name="eight" unicode="8" 
d="M325.5 38q-44.5 -46 -127.5 -46t-127.5 46t-44.5 130v55q0 108 68 146q-68 37 -68 141v22q0 84 44.5 130t127.5 46t127.5 -46t44.5 -130v-22q0 -103 -68 -141q68 -38 68 -146v-55q0 -84 -44.5 -130zM198 415q62 0 62 74v39q0 44 -16.5 62t-45.5 18t-45.5 -18t-16.5 -62
v-39q0 -74 62 -74zM152.5 110q16.5 -18 45.5 -18t45 17.5t17 61.5v65q0 79 -62 79t-62 -79v-65q0 -43 16.5 -61z" />
    <glyph glyph-name="nine" unicode="9" 
d="M195 -8q-80 0 -122 45.5t-42 130.5v18h104v-25q0 -69 57 -69q31 0 46 19t15 67v128q-29 -62 -102 -62q-60 0 -91 41t-31 119v128q0 84 43 130t124 46t124 -46t43 -130v-358q0 -182 -168 -182zM253 413v126q0 68 -57 68t-57 -68v-126q0 -69 57 -69t57 69z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="186" 
d="M146 381h-106v106h106v-106zM146 0h-106v106h106v-106z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="186" 
d="M146 381h-106v106h106v-106zM40 106h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M30 389l326 120v-79l-224 -80l224 -80v-79l-326 120v78z" />
    <glyph glyph-name="equal" unicode="=" 
d="M361 392h-326v78h326v-78zM361 230h-326v78h326v-78z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M40 270l224 80l-224 80v79l326 -120v-78l-326 -120v79z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="360" 
d="M114 198q0 50 14.5 83.5t42.5 74.5q31 47 47 85t16 98q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5v-76h-104v69q0 85 41 130.5t121 45.5t121 -45.5t41 -130.5q0 -66 -19 -109t-55 -92q-29 -40 -42.5 -69t-13.5 -68q0 -22 4 -39h-98q-6 18 -6 43zM221 0h-106v106h106
v-106z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="689" 
d="M388 193q-29 -60 -91 -60q-49 0 -73.5 30.5t-24.5 89.5q0 10 2 34l11 101q8 72 37.5 110t78.5 38q67 0 88 -62l6 58h96l-30 -289v-6q0 -19 20 -19q39 0 59.5 69.5t20.5 156.5q0 98 -49 154.5t-150 56.5q-132 0 -201.5 -98t-69.5 -272q0 -124 58.5 -192.5t174.5 -68.5
q60 0 108.5 15t98.5 53l-8 -96q-47 -31 -96 -43.5t-109 -12.5q-163 0 -242 93.5t-79 256.5q0 127 41.5 229t125 161.5t202.5 59.5q148 0 213 -80t65 -214q0 -114 -31 -185.5t-75.5 -102.5t-88.5 -31q-82 0 -88 66zM297 277q0 -51 46 -51q47 0 54 63l10 95q3 29 -10 42.5
t-36 13.5q-47 0 -54 -63l-9 -85q-1 -5 -1 -15z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="407" 
d="M285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="406" 
d="M330 660.5q39 -39.5 39 -121.5v-25q0 -54 -17.5 -89t-53.5 -52q82 -32 82 -150v-57q0 -81 -42.5 -123.5t-124.5 -42.5h-173v700h166q85 0 124 -39.5zM150 415h43q33 0 49.5 17t16.5 57v39q0 38 -13.5 55t-42.5 17h-53v-185zM150 100h63q29 0 43 15.5t14 53.5v61
q0 48 -16.5 66.5t-54.5 18.5h-49v-215z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="386" 
d="M359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="408" 
d="M214 700q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v700h174zM150 100h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-500z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="368" 
d="M301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="344" 
d="M292 389v-100h-142v-289h-110v700h291v-100h-181v-211h142z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="390" 
d="M359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v129h-50v100h154z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="427" 
d="M40 0v700h110v-300h125v300h112v-700h-112v300h-125v-300h-110z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="190" 
d="M150 700v-700h-110v700h110z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="256" 
d="M48 99q30 0 46.5 14.5t16.5 52.5v534h110v-526q0 -176 -158 -176q-35 0 -53 2v100q12 -1 38 -1z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="414" 
d="M150 215v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113l-107 279z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="339" 
d="M150 700v-600h181v-100h-291v700h110z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="541" 
d="M348 700h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153l81 -497z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="427" 
d="M138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="O" unicode="O" 
d="M74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="382" 
d="M325 656q41 -44 41 -129v-91q0 -85 -41 -129t-123 -44h-52v-263h-110v700h162q82 0 123 -44zM150 363h52q27 0 40.5 15t13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237z" />
    <glyph glyph-name="Q" unicode="Q" 
d="M74 662q43 46 124 46t124 -46t43 -130v-364q0 -59 -21 -98q4 -9 10.5 -12t20.5 -3h10v-98h-15q-71 0 -95 48q-36 -13 -77 -13q-81 0 -124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="402" 
d="M262 32q-2 15 -2 54v110q0 48 -17 68.5t-55 20.5h-38v-285h-110v700h166q85 0 124 -39.5t39 -121.5v-55q0 -108 -72 -142q39 -16 56 -53.5t17 -95.5v-108q0 -30 2 -48.5t10 -36.5h-112q-6 17 -8 32zM150 385h43q33 0 49.5 17t16.5 57v69q0 38 -13.5 55t-42.5 17h-53v-215
z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="374" 
d="M67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77
t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="356" 
d="M348 700v-100h-115v-600h-110v600h-115v100h340z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="398" 
d="M145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="402" 
d="M290 700h101l-108 -700h-164l-108 700h111l84 -571z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="563" 
d="M244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="430" 
d="M292 360l123 -360h-116l-90 277l-91 -277h-103l123 360l-115 340h114l83 -258l85 258h102z" />
    <glyph glyph-name="Y" unicode="Y" 
d="M4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="370" 
d="M345 602l-209 -502h209v-100h-322v98l209 502h-199v100h312v-98z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="251" 
d="M229 700v-90h-72v-520h72v-90h-182v700h182z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="381" 
d="M281 0l-276 700h95l276 -700h-95z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="251" 
d="M22 0v90h72v520h-72v90h182v-700h-182z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M237 700l139 -310h-88l-90 210l-90 -210h-88l139 310h78z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="500" 
d="M500 -10v-80h-500v80h500z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="250" 
d="M225 737h-79l-102 111h113z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="407" 
d="M285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="406" 
d="M330 660.5q39 -39.5 39 -121.5v-25q0 -54 -17.5 -89t-53.5 -52q82 -32 82 -150v-57q0 -81 -42.5 -123.5t-124.5 -42.5h-173v700h166q85 0 124 -39.5zM150 415h43q33 0 49.5 17t16.5 57v39q0 38 -13.5 55t-42.5 17h-53v-185zM150 100h63q29 0 43 15.5t14 53.5v61
q0 48 -16.5 66.5t-54.5 18.5h-49v-215z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="386" 
d="M359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="408" 
d="M214 700q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v700h174zM150 100h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-500z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="368" 
d="M301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="344" 
d="M292 389v-100h-142v-289h-110v700h291v-100h-181v-211h142z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="390" 
d="M359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v129h-50v100h154z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="427" 
d="M40 0v700h110v-300h125v300h112v-700h-112v300h-125v-300h-110z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="190" 
d="M150 700v-700h-110v700h110z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="256" 
d="M48 99q30 0 46.5 14.5t16.5 52.5v534h110v-526q0 -176 -158 -176q-35 0 -53 2v100q12 -1 38 -1z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="414" 
d="M150 215v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113l-107 279z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="339" 
d="M150 700v-600h181v-100h-291v700h110z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="541" 
d="M348 700h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153l81 -497z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="427" 
d="M138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="o" unicode="o" 
d="M74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="377" 
d="M325 656q41 -44 41 -129v-91q0 -85 -41 -129t-123 -44h-52v-263h-110v700h162q82 0 123 -44zM150 363h52q27 0 40.5 15t13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237z" />
    <glyph glyph-name="q" unicode="q" 
d="M74 662q43 46 124 46t124 -46t43 -130v-364q0 -59 -21 -98q4 -9 10.5 -12t20.5 -3h10v-98h-15q-71 0 -95 48q-36 -13 -77 -13q-81 0 -124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="402" 
d="M262 32q-2 15 -2 54v110q0 48 -17 68.5t-55 20.5h-38v-285h-110v700h166q85 0 124 -39.5t39 -121.5v-55q0 -108 -72 -142q39 -16 56 -53.5t17 -95.5v-108q0 -30 2 -48.5t10 -36.5h-112q-6 17 -8 32zM150 385h43q33 0 49.5 17t16.5 57v69q0 38 -13.5 55t-42.5 17h-53v-215
z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="374" 
d="M67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77
t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="356" 
d="M348 700v-100h-115v-600h-110v600h-115v100h340z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="398" 
d="M145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="402" 
d="M290 700h101l-108 -700h-164l-108 700h111l84 -571z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="563" 
d="M244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="430" 
d="M292 360l123 -360h-116l-90 277l-91 -277h-103l123 360l-115 340h114l83 -258l85 258h102z" />
    <glyph glyph-name="y" unicode="y" 
d="M4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="370" 
d="M345 602l-209 -502h209v-100h-322v98l209 502h-199v100h312v-98z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="257" 
d="M151 317.5q14 -23.5 16 -56.5l5 -100q2 -40 14 -55.5t39 -15.5h16v-90h-56q-48 0 -77.5 28t-33.5 83l-8 134q-2 35 -13 47.5t-42 12.5v90q31 0 42 12.5t13 47.5l8 134q4 55 33.5 83t77.5 28h56v-90h-16q-27 0 -39 -15.5t-14 -55.5l-5 -100q-2 -33 -16 -56.5t-49 -32.5
q35 -9 49 -32.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="500" 
d="M300 765v-830h-90v830h90z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="257" 
d="M106 382.5q-14 23.5 -16 56.5l-5 100q-2 40 -14 55.5t-39 15.5h-16v90h56q48 0 77.5 -28t33.5 -83l8 -134q2 -35 13 -47.5t42 -12.5v-90q-31 0 -42 -12.5t-13 -47.5l-8 -134q-4 -55 -33.5 -83t-77.5 -28h-56v90h16q27 0 39 15.5t14 55.5l5 100q2 33 16 56.5t49 32.5
q-35 9 -49 32.5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M69.5 402.5q27.5 19.5 57.5 19.5q19 0 36.5 -8t43.5 -25q22 -14 36 -21t27 -7q16 0 28.5 11.5t37.5 45.5l52 -54q-34 -50 -61 -68t-58 -18q-19 0 -36.5 8t-43.5 25q-22 14 -36 21t-27 7q-17 0 -29.5 -11.5t-36.5 -45.5l-52 50q34 51 61.5 70.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="155" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="190" 
d="M42 700h106v-106h-106v106zM150 292v-292h-110v292l14 253h82z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M65.5 92.5q-29.5 43.5 -29.5 115.5v284q0 71 29.5 114.5t87.5 56.5v52h90v-51q60 -12 90.5 -56t30.5 -116v-48h-104v55q0 69 -57 69t-57 -69v-298q0 -68 57 -68t57 68v80h104v-73q0 -73 -30.5 -117t-90.5 -55v-51h-90v52q-58 12 -87.5 55.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M111 295q-7 20 -25 56q-23 48 -34.5 85.5t-11.5 95.5q0 85 41 130.5t121 45.5t121 -45.5t41 -130.5v-69h-104v76q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -55 10.5 -92t30.5 -81q20 -47 26 -71h121v-85h-114q-7 -71 -56 -110h189v-100h-327v96q40 0 67 30.5
t28 83.5h-81v85h67z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M69 350q0 39 18 67l-55 56l43 43l56 -56q30 19 67 19q35 0 67 -19l56 56l43 -43l-56 -56q19 -32 19 -67q0 -37 -19 -67l56 -56l-43 -43l-56 55q-29 -18 -67 -18t-68 18l-55 -55l-43 43l55 55q-18 30 -18 68zM153 305q19 -19 45 -19t45 19t19 45t-19 45t-45 19t-45 -19
t-19 -45t19 -45z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M261 271h83v-55h-91v-45h91v-55h-91v-116h-110v116h-91v55h91v45h-91v55h82l-127 429h115l81 -309l81 309h105z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="500" 
d="M300 765v-355h-90v355h90zM300 290v-355h-90v355h90z" />
    <glyph glyph-name="section" unicode="&#xa7;" 
d="M52.5 385q17.5 32 51.5 51q-32 21 -50.5 51.5t-18.5 78.5q0 63 42 102.5t120 39.5t120 -37t42 -99v-36h-104v21q0 27 -14.5 43.5t-40.5 16.5t-40.5 -14.5t-14.5 -41.5q0 -22 18 -36t58 -32q43 -19 71 -37.5t48 -51.5t20 -83q0 -39 -18 -71.5t-52 -51.5q33 -20 51.5 -50.5
t18.5 -78.5q0 -63 -42 -102.5t-120 -39.5t-120 37t-42 99v50h104v-35q0 -27 14.5 -43.5t40.5 -16.5t40.5 14.5t14.5 41.5q0 22 -18 36t-58 32q-43 19 -71 37.5t-48 51.5t-20 83q0 39 17.5 71zM155.5 273q15.5 -17 45.5 -32q24 4 39 25.5t15 50.5q0 27 -16 44t-46 32
q-24 -4 -38.5 -25t-14.5 -50q0 -28 15.5 -45z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="250" 
d="M97 737h-96v96h96v-96zM249 737h-96v96h96v-96z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="736" 
d="M65.5 534q45.5 82 125 128t177.5 46t177.5 -46t125 -128t45.5 -184t-45.5 -184t-125 -128t-177.5 -46t-177.5 46t-125 128t-45.5 184t45.5 184zM128 202q36 -66 99 -103t141 -37t141 37t99 103t36 148t-36 148t-99 103t-141 37t-141 -37t-99 -103t-36 -148t36 -148z
M477 252q0 -59 -28.5 -90.5t-84.5 -31.5t-84.5 31.5t-28.5 90.5v194q0 60 28.5 92t84.5 32t84.5 -32t28.5 -92v-36h-72v42q0 48 -39 48t-39 -48v-204q0 -47 39 -47t39 47v60h72v-56z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="278" 
d="M67 528q37 28 105 30v46q0 19 -7 28t-21 9q-17 0 -25.5 -10t-8.5 -33v-23h-66v19q0 53 26 82t77 29q95 0 95 -111v-261h-59l-4 48q-17 -53 -71 -53q-78 0 -78 101v4q0 67 37 95zM136 392q17 0 26 8.5t10 27.5v78q-72 -3 -72 -62v-9q0 -43 36 -43zM242 218h-212v69h212
v-69z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="355" 
d="M116 351l61 -273h-99l-62 273l62 257h99zM278 351l61 -273h-99l-62 273l62 257h99z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M371 174h-78v137h-268v78h346v-215z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="270" 
d="M235 400v-100h-200v100h200z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="736" 
d="M65.5 534q45.5 82 125 128t177.5 46t177.5 -46t125 -128t45.5 -184t-45.5 -184t-125 -128t-177.5 -46t-177.5 46t-125 128t-45.5 184t45.5 184zM128 202q36 -66 99 -103t141 -37t141 37t99 103t36 148t-36 148t-99 103t-141 37t-141 -37t-99 -103t-36 -148t36 -148z
M412.5 157.5q-1.5 10.5 -1.5 38.5v46q0 34 -12 48t-39 14h-26v-168h-76v428h116q58 0 85.5 -27.5t27.5 -84.5v-9q0 -39 -12.5 -63t-39.5 -36q28 -12 40 -37.5t12 -65.5v-46q0 -39 9 -59h-78q-4 11 -5.5 21.5zM334 374h31q22 0 34 11.5t12 39.5v18q0 26 -9.5 38.5t-31.5 12.5
h-36v-120z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="250" 
d="M240 741h-230v78h230v-78z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="228" 
d="M44 679q29 29 70 29t70 -29t29 -70t-29 -70t-70 -29t-70 29t-29 70t29 70zM79.5 574.5q14.5 -14.5 34.5 -14.5t34.5 14.5t14.5 34.5t-14.5 34.5t-34.5 14.5t-34.5 -14.5t-14.5 -34.5t14.5 -34.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M361 174h-326v78h124v80h-124v78h124v114h78v-114h124v-78h-124v-80h124v-78z" />
    <glyph glyph-name="uni00B2" unicode="&#xb2;" horiz-adv-x="280" 
d="M171 727q-9 11 -27 11q-36 0 -36 -44v-47h-66v43q0 54 26.5 82.5t77.5 28.5t77.5 -28.5t26.5 -82.5t-22.5 -92t-63.5 -85q-27 -29 -40 -48.5t-13 -40.5q0 -7 1 -10h132v-63h-202v54q0 43 17.5 73.5t51.5 68.5q34 37 51.5 67.5t17.5 73.5q0 28 -9 39z" />
    <glyph glyph-name="uni00B3" unicode="&#xb3;" horiz-adv-x="280" 
d="M163.5 537.5q-10.5 12.5 -34.5 12.5h-24v63h27q42 0 42 48v25q0 28 -9 39.5t-27 11.5q-36 0 -36 -44v-28h-66v23q0 54 26.5 83t77.5 29t77.5 -29t26.5 -83v-11q0 -36 -12 -58.5t-37 -32.5q49 -21 49 -93v-35q0 -54 -26.5 -83t-77.5 -29t-77.5 29t-26.5 83v36h66v-41
q0 -44 36 -44q18 0 27 11.5t9 39.5v34q0 31 -10.5 43.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="250" 
d="M101 737h-74l68 111h108z" />
    <glyph glyph-name="uni00B5" unicode="&#xb5;" 
d="M198 92q57 0 57 69v339h110v-500h-100v52q-15 -29 -40.5 -44.5t-59.5 -15.5q-10 0 -24 2v-87h-110v593h110v-339q0 -69 57 -69z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" 
d="M276 -65v685h-54v-685h-80l2 330q-62 0 -96 47t-34 124v91q0 85 41 129t123 44h178v-765h-80z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="186" 
d="M146 297h-106v106h106v-106z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="250" 
d="M100 -79v-8q0 -10 6.5 -15t15.5 -5q25 0 25 24q0 14 -9 20t-27 6h-10v71h48v-53q38 0 56 -10.5t18 -36.5q0 -37 -25 -49t-76 -12q-46 0 -71 13t-25 45v10h74z" />
    <glyph glyph-name="uni00B9" unicode="&#xb9;" horiz-adv-x="200" 
d="M76.5 753q15.5 14 26.5 42h46v-444h-70v339h-54v49q36 0 51.5 14z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="280" 
d="M61.5 676q27.5 29 78.5 29t78.5 -29t27.5 -82v-155q0 -53 -27.5 -82t-78.5 -29t-78.5 29t-27.5 82v155q0 53 27.5 82zM113 402q9 -10 27 -10q36 0 36 43v163q0 43 -36 43q-18 0 -27 -10t-9 -33v-163q0 -23 9 -33zM246 218h-212v69h212v-69z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="355" 
d="M177 351l-62 -273h-99l61 273l-61 257h99zM339 351l-62 -273h-99l61 273l-61 257h99z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="627" 
d="M116.5 658q15.5 14 26.5 42h46v-444h-70v339h-54v49q36 0 51.5 14zM501 700l-276 -700h-65l276 700h65zM498 444h76v-300h33v-64h-33v-80h-69v80h-134v64zM505 308l-71 -164h71v164z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="627" 
d="M116.5 658q15.5 14 26.5 42h46v-444h-70v339h-54v49q36 0 51.5 14zM471 700l-276 -700h-65l276 700h65zM518 376q-9 11 -27 11q-36 0 -36 -44v-47h-66v43q0 54 26.5 82.5t77.5 28.5t77.5 -28.5t26.5 -82.5t-22.5 -92t-63.5 -85q-27 -29 -40 -48.5t-13 -40.5q0 -7 1 -10
h132v-63h-202v54q0 43 17.5 73.5t51.5 68.5q34 37 51.5 67.5t17.5 73.5q0 28 -9 39z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="627" 
d="M163.5 442.5q-10.5 12.5 -34.5 12.5h-24v63h27q42 0 42 48v25q0 28 -9 39.5t-27 11.5q-36 0 -36 -44v-28h-66v23q0 54 26.5 83t77.5 29t77.5 -29t26.5 -83v-11q0 -36 -12 -58.5t-37 -32.5q49 -21 49 -93v-35q0 -54 -26.5 -83t-77.5 -29t-77.5 29t-26.5 83v36h66v-41
q0 -44 36 -44q18 0 27 11.5t9 39.5v34q0 31 -10.5 43.5zM511 700l-276 -700h-65l276 700h65zM498 444h76v-300h33v-64h-33v-80h-69v80h-134v64zM505 308l-71 -164h71v164z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="360" 
d="M139 700h106v-106h-106v106zM246 502q0 -50 -14.5 -83.5t-42.5 -74.5q-31 -47 -47 -85t-16 -98q0 -36 14 -52.5t41 -16.5t41 16.5t14 52.5v76h104v-69q0 -85 -41 -130.5t-121 -45.5t-121 45.5t-41 130.5q0 66 19 109t55 92q29 40 42.5 69t13.5 68q0 22 -4 39h98
q6 -18 6 -43z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="407" 
d="M264 737h-79l-102 111h113zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="407" 
d="M219 737h-74l68 111h108zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="407" 
d="M154 848h99l89 -111h-94l-45 55l-44 -55h-94zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="407" 
d="M268 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354
h106z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="407" 
d="M175 737h-96v96h96v-96zM327 737h-96v96h96v-96zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="407" 
d="M146 871q23 23 57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57t23 57zM181.5 792.5q8.5 -8.5 21.5 -8.5t21.5 8.5t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5t8.5 -21.5zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354
l-53 -354h106z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="580" 
d="M141 127l-32 -127h-105l187 700h361v-100h-190v-195h151v-100h-151v-205h190v-100h-300v127h-111zM252 559l-86 -337h86v337z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="386" 
d="M359 168q0 -78 -36.5 -123.5t-105.5 -51.5v-32q38 0 56 -10.5t18 -36.5q0 -37 -25 -49t-76 -12q-46 0 -71 13t-25 45v10h74v-8q0 -10 6.5 -15t15.5 -5q25 0 25 24q0 14 -9 20t-27 6h-10v51q-68 7 -103 51.5t-35 122.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5
v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="368" 
d="M249 737h-79l-102 111h113zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="368" 
d="M205 737h-74l68 111h108zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="368" 
d="M139 848h99l89 -111h-94l-45 55l-44 -55h-94zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="368" 
d="M161 737h-96v96h96v-96zM313 737h-96v96h96v-96zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="190" 
d="M156 737h-79l-102 111h113zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="190" 
d="M111 737h-74l68 111h108zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="190" 
d="M46 848h99l89 -111h-94l-45 55l-44 -55h-94zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="190" 
d="M67 737h-96v96h96v-96zM219 737h-96v96h96v-96zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="408" 
d="M4 305v90h36v305h174q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v305h-36zM150 395h68v-90h-68v-205h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-205z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="427" 
d="M278 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" 
d="M258 737h-79l-102 111h113zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" 
d="M214 737h-74l68 111h108zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" 
d="M148 848h99l89 -111h-94l-45 55l-44 -55h-94zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" 
d="M263 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364
q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" 
d="M170 737h-96v96h96v-96zM322 737h-96v96h96v-96zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M304 509l53 -53l-106 -106l106 -106l-53 -53l-106 106l-107 -106l-53 53l106 106l-105 105l53 53l106 -105z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" 
d="M31 168v364q0 84 43 130t124 46q60 0 100 -26l20 55l37 -13l-26 -71q36 -44 36 -121v-364q0 -84 -43 -130t-124 -46q-62 0 -100 25l-20 -54l-37 13l25 70q-35 46 -35 122zM253 560q-7 48 -55 48q-57 0 -57 -69v-288zM255 161v288l-113 -309q8 -48 56 -48q57 0 57 69z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="398" 
d="M260 737h-79l-102 111h113zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="398" 
d="M216 737h-74l68 111h108zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="398" 
d="M151 848h99l89 -111h-94l-45 55l-44 -55h-94zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="398" 
d="M173 737h-96v96h96v-96zM325 737h-96v96h96v-96zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" 
d="M218 737h-74l68 111h108zM4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="377" 
d="M150 700v-80h52q82 0 123 -44t41 -129v-91q0 -85 -41 -129t-123 -44h-52v-183h-110v700h110zM150 283h52q27 0 40.5 15t13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="748" 
d="M67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77
t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5zM441 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5
t-42 130.5v43h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="407" 
d="M264 737h-79l-102 111h113zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="407" 
d="M219 737h-74l68 111h108zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="407" 
d="M154 848h99l89 -111h-94l-45 55l-44 -55h-94zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="407" 
d="M268 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354
h106z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="407" 
d="M175 737h-96v96h96v-96zM327 737h-96v96h96v-96zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="407" 
d="M146 871q23 23 57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57t23 57zM181.5 792.5q8.5 -8.5 21.5 -8.5t21.5 8.5t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5t8.5 -21.5zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354
l-53 -354h106z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="580" 
d="M513 405v-100h-151v-205h190v-100h-300v127h-111l-32 -127h-105l187 700h361v-100h-190v-195h151zM252 559l-86 -337h86v337z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="386" 
d="M359 168q0 -78 -36.5 -123.5t-105.5 -51.5v-32q38 0 56 -10.5t18 -36.5q0 -37 -25 -49t-76 -12q-46 0 -71 13t-25 45v10h74v-8q0 -10 6.5 -15t15.5 -5q25 0 25 24q0 14 -9 20t-27 6h-10v51q-68 7 -103 51.5t-35 122.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5
v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="368" 
d="M249 737h-79l-102 111h113zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="368" 
d="M205 737h-74l68 111h108zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="368" 
d="M139 848h99l89 -111h-94l-45 55l-44 -55h-94zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="368" 
d="M161 737h-96v96h96v-96zM313 737h-96v96h96v-96zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="190" 
d="M156 737h-79l-102 111h113zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="190" 
d="M111 737h-74l68 111h108zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="190" 
d="M46 848h99l89 -111h-94l-45 55l-44 -55h-94zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="190" 
d="M67 737h-96v96h96v-96zM219 737h-96v96h96v-96zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="408" 
d="M4 305v90h36v305h174q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v305h-36zM150 395h68v-90h-68v-205h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-205z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="427" 
d="M278 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M258 737h-79l-102 111h113zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M214 737h-74l68 111h108zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M148 848h99l89 -111h-94l-45 55l-44 -55h-94zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M263 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364
q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M170 737h-96v96h96v-96zM322 737h-96v96h96v-96zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M251 449h-106v106h106v-106zM371 311h-346v78h346v-78zM251 144h-106v106h106v-106z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M74 662q43 46 124 46q60 0 100 -26l20 55l37 -13l-26 -71q36 -44 36 -121v-364q0 -84 -43 -130t-124 -46q-62 0 -100 25l-20 -54l-37 13l25 70q-35 46 -35 122v364q0 84 43 130zM253 560q-7 48 -55 48q-57 0 -57 -69v-288zM255 161v288l-113 -309q8 -48 56 -48
q57 0 57 69z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="398" 
d="M260 737h-79l-102 111h113zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="398" 
d="M216 737h-74l68 111h108zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="398" 
d="M151 848h99l89 -111h-94l-45 55l-44 -55h-94zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="398" 
d="M173 737h-96v96h96v-96zM325 737h-96v96h96v-96zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" 
d="M218 737h-74l68 111h108zM4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="377" 
d="M150 700v-80h52q82 0 123 -44t41 -129v-91q0 -85 -41 -129t-123 -44h-52v-183h-110v700h110zM150 283h52q27 0 40.5 15t13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" 
d="M174 737h-96v96h96v-96zM326 737h-96v96h96v-96zM4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="407" 
d="M318 741h-230v78h230v-78zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="407" 
d="M318 741h-230v78h230v-78zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="407" 
d="M315 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="407" 
d="M315 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="407" 
d="M355 0q-30 -25 -30 -58q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 42 47 73l-22 139h-135l-19 -127h-101l112 700h161l112 -700h-41zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="407" 
d="M355 0q-30 -25 -30 -58q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 42 47 73l-22 139h-135l-19 -127h-101l112 700h161l112 -700h-41zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="386" 
d="M211 737h-74l68 111h108zM359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="386" 
d="M211 737h-74l68 111h108zM359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="386" 
d="M145 848h99l89 -111h-94l-45 55l-44 -55h-94zM359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="386" 
d="M145 848h99l89 -111h-94l-45 55l-44 -55h-94zM359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="386" 
d="M243 737h-96v96h96v-96zM359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="386" 
d="M243 737h-96v96h96v-96zM359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="386" 
d="M194 792l45 56h94l-89 -111h-99l-89 111h94zM359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="386" 
d="M194 792l45 56h94l-89 -111h-99l-89 111h94zM359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="408" 
d="M194 792l45 56h94l-89 -111h-99l-89 111h94zM214 700q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v700h174zM150 100h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-500z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="408" 
d="M194 792l45 56h94l-89 -111h-99l-89 111h94zM214 700q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v700h174zM150 100h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-500z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="408" 
d="M4 305v90h36v305h174q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v305h-36zM150 395h68v-90h-68v-205h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-205z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="408" 
d="M4 305v90h36v305h174q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v305h-36zM150 395h68v-90h-68v-205h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-205z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="368" 
d="M303 741h-230v78h230v-78zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="368" 
d="M303 741h-230v78h230v-78zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="368" 
d="M300 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="368" 
d="M300 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="368" 
d="M236 737h-96v96h96v-96zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="368" 
d="M236 737h-96v96h96v-96zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="368" 
d="M301 405v-100h-151v-205h190v-100h-41q-30 -25 -30 -58q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 24 17 46t50 39h-212v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="368" 
d="M301 405v-100h-151v-205h190v-100h-41q-30 -25 -30 -58q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 24 17 46t50 39h-212v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="368" 
d="M187 792l45 56h94l-89 -111h-99l-89 111h94zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="368" 
d="M187 792l45 56h94l-89 -111h-99l-89 111h94zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="390" 
d="M145 848h99l89 -111h-94l-45 55l-44 -55h-94zM359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v129h-50v100h154z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="390" 
d="M145 848h99l89 -111h-94l-45 55l-44 -55h-94zM359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v129h-50v100h154z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="390" 
d="M306 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378
q0 -68 57 -68t57 68v129h-50v100h154z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="390" 
d="M306 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378
q0 -68 57 -68t57 68v129h-50v100h154z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="390" 
d="M242 737h-96v96h96v-96zM359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v129h-50v100h154z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="390" 
d="M242 737h-96v96h96v-96zM359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v129h-50v100h154z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" horiz-adv-x="390" 
d="M359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v129h-50v100h154zM149 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" horiz-adv-x="390" 
d="M359 390v-222q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v129h-50v100h154zM149 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="427" 
d="M164 848h99l89 -111h-94l-45 55l-44 -55h-94zM40 0v700h110v-300h125v300h112v-700h-112v300h-125v-300h-110z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="427" 
d="M164 848h99l89 -111h-94l-45 55l-44 -55h-94zM40 0v700h110v-300h125v300h112v-700h-112v300h-125v-300h-110z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="437" 
d="M45 0v502h-35v82h35v116h110v-116h125v116h112v-116h35v-82h-35v-502h-112v300h-125v-300h-110zM280 502h-125v-102h125v102z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="437" 
d="M45 0v502h-35v82h35v116h110v-116h125v116h112v-116h35v-82h-35v-502h-112v300h-125v-300h-110zM280 502h-125v-102h125v102z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="190" 
d="M160 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="190" 
d="M160 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="190" 
d="M210 741h-230v78h230v-78zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="190" 
d="M210 741h-230v78h230v-78zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="190" 
d="M207 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="190" 
d="M207 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="190" 
d="M150 700v-700h-20q-30 -25 -30 -58q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 24 17 46t50 39h-43v700h110z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="190" 
d="M150 700v-700h-20q-30 -25 -30 -58q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 24 17 46t50 39h-43v700h110z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="190" 
d="M143 737h-96v96h96v-96zM150 700v-700h-110v700h110z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="190" 
d="M150 700v-700h-110v700h110z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="446" 
d="M150 700v-700h-110v700h110zM238 99q30 0 46.5 14.5t16.5 52.5v534h110v-526q0 -176 -158 -176q-35 0 -53 2v100q12 -1 38 -1z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="446" 
d="M150 700v-700h-110v700h110zM238 99q30 0 46.5 14.5t16.5 52.5v534h110v-526q0 -176 -158 -176q-35 0 -53 2v100q12 -1 38 -1z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="256" 
d="M98 848h99l89 -111h-94l-45 55l-44 -55h-94zM48 99q30 0 46.5 14.5t16.5 52.5v534h110v-526q0 -176 -158 -176q-35 0 -53 2v100q12 -1 38 -1z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="256" 
d="M98 848h99l89 -111h-94l-45 55l-44 -55h-94zM48 99q30 0 46.5 14.5t16.5 52.5v534h110v-526q0 -176 -158 -176q-35 0 -53 2v100q12 -1 38 -1z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="414" 
d="M150 215v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113l-107 279zM169 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="414" 
d="M150 215v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113l-107 279zM169 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="414" 
d="M150 215v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113l-107 279z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="339" 
d="M146 737h-74l68 111h108zM150 700v-600h181v-100h-291v700h110z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="339" 
d="M146 737h-74l68 111h108zM150 700v-600h181v-100h-291v700h110z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="339" 
d="M150 700v-600h181v-100h-291v700h110zM140 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="339" 
d="M150 700v-600h181v-100h-291v700h110zM140 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="339" 
d="M150 700v-600h181v-100h-291v700h110zM211 700h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="339" 
d="M150 700v-600h181v-100h-291v700h110zM211 700h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="339" 
d="M150 700v-600h181v-100h-291v700h110zM316 297h-106v106h106v-106z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="339" 
d="M150 700v-600h181v-100h-291v700h110zM316 297h-106v106h106v-106z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="348" 
d="M49 247v453h110v-335l121 130v-100l-121 -130v-165h181v-100h-291v147l-49 -52v100z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="348" 
d="M49 247v453h110v-335l121 130v-100l-121 -130v-165h181v-100h-291v147l-49 -52v100z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="427" 
d="M229 737h-74l68 111h108zM138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="427" 
d="M229 737h-74l68 111h108zM138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="427" 
d="M138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507zM167 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="427" 
d="M138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507zM167 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="427" 
d="M213 792l45 56h94l-89 -111h-99l-89 111h94zM138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="427" 
d="M213 792l45 56h94l-89 -111h-99l-89 111h94zM138 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="543" 
d="M5 700h106v-95l-48 -111h-45l29 100h-42v106zM254 0h-99v700h138l113 -419v419h98v-700h-113l-137 507v-507z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" 
d="M313 741h-230v78h230v-78zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M313 741h-230v78h230v-78zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" 
d="M310 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378
q0 -69 57 -69z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" 
d="M310 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378
q0 -69 57 -69z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" 
d="M149 737h-67l50 111h100zM279 737h-66l49 111h101zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M149 737h-67l50 111h100zM279 737h-66l49 111h101zM74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="586" 
d="M519 405v-100h-151v-205h190v-100h-364q-82 0 -123 44t-41 129v354q0 85 41 129t123 44h364v-100h-190v-195h151zM196 600q-27 0 -41.5 -16t-14.5 -52v-364q0 -36 14.5 -52t41.5 -16h62v500h-62z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="586" 
d="M519 405v-100h-151v-205h190v-100h-364q-82 0 -123 44t-41 129v354q0 85 41 129t123 44h364v-100h-190v-195h151zM196 600q-27 0 -41.5 -16t-14.5 -52v-364q0 -36 14.5 -52t41.5 -16h62v500h-62z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="402" 
d="M211 737h-74l68 111h108zM262 32q-2 15 -2 54v110q0 48 -17 68.5t-55 20.5h-38v-285h-110v700h166q85 0 124 -39.5t39 -121.5v-55q0 -108 -72 -142q39 -16 56 -53.5t17 -95.5v-108q0 -30 2 -48.5t10 -36.5h-112q-6 17 -8 32zM150 385h43q33 0 49.5 17t16.5 57v69
q0 38 -13.5 55t-42.5 17h-53v-215z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="402" 
d="M211 737h-74l68 111h108zM262 32q-2 15 -2 54v110q0 48 -17 68.5t-55 20.5h-38v-285h-110v700h166q85 0 124 -39.5t39 -121.5v-55q0 -108 -72 -142q39 -16 56 -53.5t17 -95.5v-108q0 -30 2 -48.5t10 -36.5h-112q-6 17 -8 32zM150 385h43q33 0 49.5 17t16.5 57v69
q0 38 -13.5 55t-42.5 17h-53v-215z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="402" 
d="M262 32q-2 15 -2 54v110q0 48 -17 68.5t-55 20.5h-38v-285h-110v700h166q85 0 124 -39.5t39 -121.5v-55q0 -108 -72 -142q39 -16 56 -53.5t17 -95.5v-108q0 -30 2 -48.5t10 -36.5h-112q-6 17 -8 32zM150 385h43q33 0 49.5 17t16.5 57v69q0 38 -13.5 55t-42.5 17h-53v-215
zM157 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="402" 
d="M262 32q-2 15 -2 54v110q0 48 -17 68.5t-55 20.5h-38v-285h-110v700h166q85 0 124 -39.5t39 -121.5v-55q0 -108 -72 -142q39 -16 56 -53.5t17 -95.5v-108q0 -30 2 -48.5t10 -36.5h-112q-6 17 -8 32zM150 385h43q33 0 49.5 17t16.5 57v69q0 38 -13.5 55t-42.5 17h-53v-215
zM157 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="402" 
d="M194 792l45 56h94l-89 -111h-99l-89 111h94zM262 32q-2 15 -2 54v110q0 48 -17 68.5t-55 20.5h-38v-285h-110v700h166q85 0 124 -39.5t39 -121.5v-55q0 -108 -72 -142q39 -16 56 -53.5t17 -95.5v-108q0 -30 2 -48.5t10 -36.5h-112q-6 17 -8 32zM150 385h43q33 0 49.5 17
t16.5 57v69q0 38 -13.5 55t-42.5 17h-53v-215z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="402" 
d="M194 792l45 56h94l-89 -111h-99l-89 111h94zM262 32q-2 15 -2 54v110q0 48 -17 68.5t-55 20.5h-38v-285h-110v700h166q85 0 124 -39.5t39 -121.5v-55q0 -108 -72 -142q39 -16 56 -53.5t17 -95.5v-108q0 -30 2 -48.5t10 -36.5h-112q-6 17 -8 32zM150 385h43q33 0 49.5 17
t16.5 57v69q0 38 -13.5 55t-42.5 17h-53v-215z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="374" 
d="M203 737h-74l68 111h108zM67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50
q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="374" 
d="M203 737h-74l68 111h108zM67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50
q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="374" 
d="M137 848h99l89 -111h-94l-45 55l-44 -55h-94zM67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43
h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="374" 
d="M137 848h99l89 -111h-94l-45 55l-44 -55h-94zM67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43
h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="374" 
d="M67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -80 -37 -125t-109 -50v-32q38 0 56 -10.5t18 -36.5q0 -37 -25 -49t-76 -12q-46 0 -71 13t-25 45
v10h74v-8q0 -10 6.5 -15t15.5 -5q25 0 25 24q0 14 -9 20t-27 6h-10v51q-66 8 -100 53t-34 121v43h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="374" 
d="M67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -80 -37 -125t-109 -50v-32q38 0 56 -10.5t18 -36.5q0 -37 -25 -49t-76 -12q-46 0 -71 13t-25 45
v10h74v-8q0 -10 6.5 -15t15.5 -5q25 0 25 24q0 14 -9 20t-27 6h-10v51q-66 8 -100 53t-34 121v43h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="374" 
d="M187 792l45 56h94l-89 -111h-99l-89 111h94zM67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43
h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="374" 
d="M186 792l45 56h94l-89 -111h-99l-89 111h94zM67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43
h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="356" 
d="M152 -99v-10q0 -18 21 -18q26 0 26 26q0 28 -36 28h-11v73h-29v600h-115v100h340v-100h-115v-600h-31v-39q29 0 46 -15.5t17 -41.5q0 -42 -25.5 -62t-65.5 -20q-36 0 -61 18t-25 51v10h64z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="356" 
d="M152 -99v-10q0 -18 21 -18q26 0 26 26q0 28 -36 28h-11v73h-29v600h-115v100h340v-100h-115v-600h-31v-39q29 0 46 -15.5t17 -41.5q0 -42 -25.5 -62t-65.5 -20q-36 0 -61 18t-25 51v10h64z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="356" 
d="M177 792l45 56h94l-89 -111h-99l-89 111h94zM348 700v-100h-115v-600h-110v600h-115v100h340z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="356" 
d="M177 792l45 56h94l-89 -111h-99l-89 111h94zM348 700v-100h-115v-600h-110v600h-115v100h340z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="356" 
d="M123 600h-115v100h340v-100h-115v-113h66v-82h-66v-405h-110v405h-66v82h66v113z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="356" 
d="M123 600h-115v100h340v-100h-115v-113h66v-82h-66v-405h-110v405h-66v82h66v113z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="398" 
d="M265 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5
v533h110v-540z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="398" 
d="M265 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5
v533h110v-540z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="398" 
d="M315 741h-230v78h230v-78zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="398" 
d="M315 741h-230v78h230v-78zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="398" 
d="M312 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="398" 
d="M312 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="398" 
d="M143 871q23 23 57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57t23 57zM178.5 792.5q8.5 -8.5 21.5 -8.5t21.5 8.5t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5t8.5 -21.5zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5
t-42 130.5v533h110v-540z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="398" 
d="M143 871q23 23 57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57t23 57zM178.5 792.5q8.5 -8.5 21.5 -8.5t21.5 8.5t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5t8.5 -21.5zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5
t-42 130.5v533h110v-540z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="398" 
d="M151 737h-67l50 111h100zM281 737h-66l49 111h101zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="398" 
d="M151 737h-67l50 111h100zM281 737h-66l49 111h101zM145 160q0 -68 57 -68t57 68v540h104v-533q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v533h110v-540z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="398" 
d="M145 160q0 -68 57 -68t57 68v540h104v-533q0 -52 -13 -91t-50 -68q-19 -15 -26.5 -33t-7.5 -33q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 46 58 80q-17 -4 -41 -4q-80 0 -122 45.5t-42 130.5v533h110v-540z
" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="398" 
d="M145 160q0 -68 57 -68t57 68v540h104v-533q0 -52 -13 -91t-50 -68q-19 -15 -26.5 -33t-7.5 -33q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 46 58 80q-17 -4 -41 -4q-80 0 -122 45.5t-42 130.5v533h110v-540z
" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="563" 
d="M232 848h99l89 -111h-94l-45 55l-44 -55h-94zM244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="563" 
d="M232 848h99l89 -111h-94l-45 55l-44 -55h-94zM244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" 
d="M153 848h99l89 -111h-94l-45 55l-44 -55h-94zM4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" 
d="M153 848h99l89 -111h-94l-45 55l-44 -55h-94zM4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" 
d="M174 737h-96v96h96v-96zM326 737h-96v96h96v-96zM4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="370" 
d="M202 737h-74l68 111h108zM345 602l-209 -502h209v-100h-322v98l209 502h-199v100h312v-98z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="370" 
d="M202 737h-74l68 111h108zM345 602l-209 -502h209v-100h-322v98l209 502h-199v100h312v-98z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="370" 
d="M234 737h-96v96h96v-96zM345 602l-209 -502h209v-100h-322v98l209 502h-199v100h312v-98z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="370" 
d="M234 737h-96v96h96v-96zM345 602l-209 -502h209v-100h-322v98l209 502h-199v100h312v-98z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="370" 
d="M186 792l45 56h94l-89 -111h-99l-89 111h94zM345 602l-209 -502h209v-100h-322v98l209 502h-199v100h312v-98z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="370" 
d="M186 792l45 56h94l-89 -111h-99l-89 111h94zM345 602l-209 -502h209v-100h-322v98l209 502h-199v100h312v-98z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="580" 
d="M306 737h-74l68 111h108zM141 127l-32 -127h-105l187 700h361v-100h-190v-195h151v-100h-151v-205h190v-100h-300v127h-111zM252 559l-86 -337h86v337z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="580" 
d="M306 737h-74l68 111h108zM141 127l-32 -127h-105l187 700h361v-100h-190v-195h151v-100h-151v-205h190v-100h-300v127h-111zM252 559l-86 -337h86v337z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" 
d="M214 737h-74l68 111h108zM74 662q43 46 124 46q60 0 100 -26l20 55l37 -13l-26 -71q36 -44 36 -121v-364q0 -84 -43 -130t-124 -46q-62 0 -100 25l-20 -54l-37 13l25 70q-35 46 -35 122v364q0 84 43 130zM253 560q-7 48 -55 48q-57 0 -57 -69v-288zM255 161v288
l-113 -309q8 -48 56 -48q57 0 57 69z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" 
d="M214 737h-74l68 111h108zM74 662q43 46 124 46q60 0 100 -26l20 55l37 -13l-26 -71q36 -44 36 -121v-364q0 -84 -43 -130t-124 -46q-62 0 -100 25l-20 -54l-37 13l25 70q-35 46 -35 122v364q0 84 43 130zM253 560q-7 48 -55 48q-57 0 -57 -69v-288zM255 161v288
l-113 -309q8 -48 56 -48q57 0 57 69z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="374" 
d="M67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77
t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5zM138 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="374" 
d="M67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50q0 -68 57 -68t57 68q0 44 -23.5 77
t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5zM138 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="356" 
d="M348 700v-100h-115v-600h-110v600h-115v100h340zM132 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="356" 
d="M348 700v-100h-115v-600h-110v600h-115v100h340zM132 -33h92v-82l-43 -64h-39l26 54h-36v92z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="256" 
d="M48 99q30 0 46.5 14.5t16.5 52.5v534h110v-526q0 -176 -158 -176q-35 0 -53 2v100q12 -1 38 -1z" />
    <glyph glyph-name="uni02BC" unicode="&#x2bc;" horiz-adv-x="186" 
d="M40 700h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="250" 
d="M76 848h99l89 -111h-94l-45 55l-44 -55h-94z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="250" 
d="M125 792l45 56h94l-89 -111h-99l-89 111h94z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="250" 
d="M237 848q0 -54 -29 -84.5t-84 -30.5t-82.5 30.5t-28.5 84.5h65q2 -27 13 -37t33 -10q23 0 34.5 10t13.5 37h65z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="250" 
d="M173 737h-96v96h96v-96z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="250" 
d="M68 871q23 23 57 23t57 -23t23 -57t-23 -57t-57 -23t-57 23t-23 57t23 57zM103.5 792.5q8.5 -8.5 21.5 -8.5t21.5 8.5t8.5 21.5t-8.5 21.5t-21.5 8.5t-21.5 -8.5t-8.5 -21.5t8.5 -21.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="250" 
d="M166 4q-35 -27 -35 -62q0 -13 10 -20.5t25 -7.5q16 0 24.5 2.5t11.5 3.5v-56q-17 -7 -31.5 -9t-36.5 -2q-41 0 -64 16t-23 46q0 53 76 89h43z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="250" 
d="M190 819q10 6 18 23l53 -37q-25 -68 -85 -68q-15 0 -26.5 3.5t-26.5 10.5q-22 11 -37 11t-25 -6t-18 -23l-53 37q25 68 85 68q15 0 26.5 -3.5t26.5 -10.5q22 -11 37 -11t25 6z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="250" 
d="M42 737h-67l50 111h100zM172 737h-66l49 111h101z" />
    <glyph glyph-name="uni0401" unicode="&#x401;" horiz-adv-x="368" 
d="M161 737h-96v96h96v-96zM313 737h-96v96h96v-96zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="uni0410" unicode="&#x410;" horiz-adv-x="407" 
d="M285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="uni0411" unicode="&#x411;" horiz-adv-x="415" 
d="M41 700h308v-100h-198v-179h64q82 0 124 -44.5t42 -128.5v-75q0 -84 -42 -128.5t-124 -44.5h-174v700zM257 115q14 15 14 51v89q0 36 -14 51t-42 15h-64v-221h64q28 0 42 15z" />
    <glyph glyph-name="uni0412" unicode="&#x412;" horiz-adv-x="406" 
d="M330 660.5q39 -39.5 39 -121.5v-25q0 -54 -17.5 -89t-53.5 -52q82 -32 82 -150v-57q0 -81 -42.5 -123.5t-124.5 -42.5h-173v700h166q85 0 124 -39.5zM150 415h43q33 0 49.5 17t16.5 57v39q0 38 -13.5 55t-42.5 17h-53v-185zM150 100h63q29 0 43 15.5t14 53.5v61
q0 48 -16.5 66.5t-54.5 18.5h-49v-215z" />
    <glyph glyph-name="uni0413" unicode="&#x413;" horiz-adv-x="365" 
d="M41 700h290v-100h-180v-600h-110v700z" />
    <glyph glyph-name="uni0414" unicode="&#x414;" horiz-adv-x="488" 
d="M358 0h-235v-94h-107v192h43q14 17 19.5 38t7.5 53l25 511h308v-602h48v-192h-109v94zM184.5 132q-7.5 -21 -21.5 -34h144v502h-93l-20 -409q-2 -38 -9.5 -59z" />
    <glyph glyph-name="uni0415" unicode="&#x415;" horiz-adv-x="368" 
d="M301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="uni0416" unicode="&#x416;" horiz-adv-x="604" 
d="M487 700h110l-135 -284l135 -416h-113l-100 310l-27 -53v-257h-109v257l-27 54l-100 -311h-113l136 413l-136 287h110l130 -283v283h109v-283z" />
    <glyph glyph-name="uni0417" unicode="&#x417;" 
d="M237.5 590.5q-14.5 17.5 -42.5 17.5q-58 0 -58 -69v-57h-101v52q0 85 41 129.5t121 44.5t122 -45.5t42 -130.5v-18q0 -57 -19 -92.5t-58 -51.5q77 -34 77 -147v-55q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v77h104v-84q0 -69 57 -69q28 0 42.5 17.5t14.5 61.5
v55q0 48 -17 68.5t-55 20.5h-27v100h33q33 0 49.5 17t16.5 57v39q0 45 -14.5 62.5z" />
    <glyph glyph-name="uni0418" unicode="&#x418;" horiz-adv-x="427" 
d="M40 0v700h98v-443l40 154l90 289h121v-700h-99v493l-45 -172l-92 -321h-113z" />
    <glyph glyph-name="uni0419" unicode="&#x419;" horiz-adv-x="429" 
d="M333 856q0 -52 -30 -81.5t-87 -29.5q-56 0 -84.5 29t-30.5 82h68q2 -27 13 -36.5t34 -9.5q24 0 36 10t14 36h67zM290 493v-493h99v700h-121l-90 -289l-40 -154v443h-98v-700h113l92 321z" />
    <glyph glyph-name="uni041A" unicode="&#x41a;" horiz-adv-x="414" 
d="M150 215v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113l-107 279z" />
    <glyph glyph-name="uni041B" unicode="&#x41b;" horiz-adv-x="450" 
d="M49.5 103.5q13.5 4.5 20 20t7.5 47.5l18 529h311v-700h-112v600h-96l-12 -423q-3 -96 -40 -137t-120 -41h-17v100q27 0 40.5 4.5z" />
    <glyph glyph-name="uni041C" unicode="&#x41c;" horiz-adv-x="541" 
d="M348 700h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153l81 -497z" />
    <glyph glyph-name="uni041D" unicode="&#x41d;" horiz-adv-x="427" 
d="M40 0v700h110v-300h125v300h112v-700h-112v300h-125v-300h-110z" />
    <glyph glyph-name="uni041E" unicode="&#x41e;" 
d="M74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="uni041F" unicode="&#x41f;" horiz-adv-x="427" 
d="M40 0v700h347v-700h-112v600h-125v-600h-110z" />
    <glyph glyph-name="uni0420" unicode="&#x420;" horiz-adv-x="377" 
d="M325 656q41 -44 41 -129v-91q0 -85 -41 -129t-123 -44h-52v-263h-110v700h162q82 0 123 -44zM150 363h52q27 0 40.5 15t13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237z" />
    <glyph glyph-name="uni0421" unicode="&#x421;" horiz-adv-x="386" 
d="M359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="uni0422" unicode="&#x422;" horiz-adv-x="356" 
d="M348 700v-100h-115v-600h-110v600h-115v100h340z" />
    <glyph glyph-name="uni0423" unicode="&#x423;" horiz-adv-x="408" 
d="M250.5 76.5q-20.5 -38.5 -58 -58.5t-100.5 -20q-19 0 -30 1v98q7 -1 20 -1q35 0 53 13t24 45l1 6l-152 540h109l60 -239l34 -164l26 163l50 240h115l-118 -522q-13 -63 -33.5 -101.5z" />
    <glyph glyph-name="uni0424" unicode="&#x424;" horiz-adv-x="583" 
d="M195 79q-82 0 -123 44t-41 129v191q0 85 41 129t123 44h41v84h108v-84h41q82 0 123 -44t41 -129v-191q0 -85 -41 -129t-123 -44h-41v-79h-108v79h-41zM195 519q-27 0 -41 -16.5t-14 -52.5v-205q0 -70 55 -70h41v344h-41zM344 175h41q55 0 55 70v205q0 36 -14 52.5
t-41 16.5h-41v-344z" />
    <glyph glyph-name="uni0425" unicode="&#x425;" horiz-adv-x="430" 
d="M292 360l123 -360h-116l-90 277l-91 -277h-103l123 360l-115 340h114l83 -258l85 258h102z" />
    <glyph glyph-name="uni0426" unicode="&#x426;" horiz-adv-x="455" 
d="M150 700v-600h125v600h112v-602h48v-192h-107v94h-288v700h110z" />
    <glyph glyph-name="uni0427" unicode="&#x427;" horiz-adv-x="415" 
d="M374 0h-110v282q-17 -19 -43.5 -29t-50.5 -10q-66 0 -101.5 43.5t-35.5 117.5v296h110v-287q0 -34 18 -51.5t46 -17.5q27 0 42 18.5t15 53.5v284h110v-700z" />
    <glyph glyph-name="uni0428" unicode="&#x428;" horiz-adv-x="600" 
d="M40 700h110v-600h95v600h110v-600h95v600h110v-700h-520v700z" />
    <glyph glyph-name="uni0429" unicode="&#x429;" horiz-adv-x="629" 
d="M609 98v-192h-107v94h-462v700h110v-600h95v600h110v-600h95v600h110v-602h49z" />
    <glyph glyph-name="uni042A" unicode="&#x42a;" horiz-adv-x="439" 
d="M197 700v-263h52q82 0 123 -44t41 -129v-91q0 -85 -41 -129t-123 -44h-162v600h-79v100h189zM289.5 115q13.5 15 13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237h52q27 0 40.5 15z" />
    <glyph glyph-name="uni042B" unicode="&#x42b;" horiz-adv-x="579" 
d="M40 700h110v-263h52q82 0 123 -44t41 -129v-91q0 -85 -41 -129t-123 -44h-162v700zM429 700h110v-700h-110v700zM242.5 115q13.5 15 13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237h52q27 0 40.5 15z" />
    <glyph glyph-name="uni042C" unicode="&#x42c;" horiz-adv-x="392" 
d="M40 700h110v-263h52q82 0 123 -44t41 -129v-91q0 -85 -41 -129t-123 -44h-162v700zM242.5 115q13.5 15 13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237h52q27 0 40.5 15z" />
    <glyph glyph-name="uni042D" unicode="&#x42d;" horiz-adv-x="386" 
d="M130 161q0 -68 57 -68t57 68v151h-101v100h101v127q0 69 -57 69t-57 -69v-66h-102v59q0 85 41 130.5t121 45.5t122 -45.5t42 -130.5v-364q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v83h104v-90z" />
    <glyph glyph-name="uni042E" unicode="&#x42e;" horiz-adv-x="599" 
d="M150 300v-300h-110v700h110v-300h82v132q0 84 43 130t124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v132h-82zM399 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="uni042F" unicode="&#x42f;" horiz-adv-x="402" 
d="M30 36.5q2 18.5 2 48.5v108q0 58 17 95.5t56 53.5q-72 34 -72 142v55q0 82 39 121.5t124 39.5h166v-700h-110v285h-38q-38 0 -55 -20.5t-17 -68.5v-110q0 -39 -2 -54t-8 -32h-112q8 18 10 36.5zM156.5 583q-13.5 -17 -13.5 -55v-69q0 -40 16.5 -57t49.5 -17h43v215h-53
q-29 0 -42.5 -17z" />
    <glyph glyph-name="uni0430" unicode="&#x430;" horiz-adv-x="407" 
d="M285 0l-19 127h-135l-19 -127h-101l112 700h161l112 -700h-111zM251 222l-53 354l-53 -354h106z" />
    <glyph glyph-name="uni0431" unicode="&#x431;" horiz-adv-x="415" 
d="M41 700h308v-100h-198v-179h64q82 0 124 -44.5t42 -128.5v-75q0 -84 -42 -128.5t-124 -44.5h-174v700zM257 115q14 15 14 51v89q0 36 -14 51t-42 15h-64v-221h64q28 0 42 15z" />
    <glyph glyph-name="uni0432" unicode="&#x432;" horiz-adv-x="406" 
d="M330 660.5q39 -39.5 39 -121.5v-25q0 -54 -17.5 -89t-53.5 -52q82 -32 82 -150v-57q0 -81 -42.5 -123.5t-124.5 -42.5h-173v700h166q85 0 124 -39.5zM150 415h43q33 0 49.5 17t16.5 57v39q0 38 -13.5 55t-42.5 17h-53v-185zM150 100h63q29 0 43 15.5t14 53.5v61
q0 48 -16.5 66.5t-54.5 18.5h-49v-215z" />
    <glyph glyph-name="uni0433" unicode="&#x433;" horiz-adv-x="365" 
d="M41 700h290v-100h-180v-600h-110v700z" />
    <glyph glyph-name="uni0434" unicode="&#x434;" horiz-adv-x="488" 
d="M358 0h-235v-94h-107v192h43q14 17 19.5 38t7.5 53l25 511h308v-602h48v-192h-109v94zM184.5 132q-7.5 -21 -21.5 -34h144v502h-93l-20 -409q-2 -38 -9.5 -59z" />
    <glyph glyph-name="uni0435" unicode="&#x435;" horiz-adv-x="368" 
d="M301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="uni0436" unicode="&#x436;" horiz-adv-x="604" 
d="M487 700h110l-135 -284l135 -416h-113l-100 310l-27 -53v-257h-109v257l-27 54l-100 -311h-113l136 413l-136 287h110l130 -283v283h109v-283z" />
    <glyph glyph-name="uni0437" unicode="&#x437;" 
d="M237.5 590.5q-14.5 17.5 -42.5 17.5q-58 0 -58 -69v-57h-101v52q0 85 41 129.5t121 44.5t122 -45.5t42 -130.5v-18q0 -57 -19 -92.5t-58 -51.5q77 -34 77 -147v-55q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v77h104v-84q0 -69 57 -69q28 0 42.5 17.5t14.5 61.5
v55q0 48 -17 68.5t-55 20.5h-27v100h33q33 0 49.5 17t16.5 57v39q0 45 -14.5 62.5z" />
    <glyph glyph-name="uni0438" unicode="&#x438;" horiz-adv-x="427" 
d="M40 0v700h98v-443l40 154l90 289h121v-700h-99v493l-45 -172l-92 -321h-113z" />
    <glyph glyph-name="uni0439" unicode="&#x439;" horiz-adv-x="429" 
d="M333 856q0 -52 -30 -81.5t-87 -29.5q-56 0 -84.5 29t-30.5 82h68q2 -27 13 -36.5t34 -9.5q24 0 36 10t14 36h67zM290 493v-493h99v700h-121l-90 -289l-40 -154v443h-98v-700h113l92 321z" />
    <glyph glyph-name="uni043A" unicode="&#x43a;" horiz-adv-x="414" 
d="M150 215v-215h-110v700h110v-305l144 305h110l-153 -312l153 -388h-113l-107 279z" />
    <glyph glyph-name="uni043B" unicode="&#x43b;" horiz-adv-x="450" 
d="M49.5 103.5q13.5 4.5 20 20t7.5 47.5l18 529h311v-700h-112v600h-96l-12 -423q-3 -96 -40 -137t-120 -41h-17v100q27 0 40.5 4.5z" />
    <glyph glyph-name="uni043C" unicode="&#x43c;" horiz-adv-x="541" 
d="M348 700h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153l81 -497z" />
    <glyph glyph-name="uni043D" unicode="&#x43d;" horiz-adv-x="427" 
d="M40 0v700h110v-300h125v300h112v-700h-112v300h-125v-300h-110z" />
    <glyph glyph-name="uni043E" unicode="&#x43e;" 
d="M74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="uni043F" unicode="&#x43f;" horiz-adv-x="427" 
d="M40 0v700h347v-700h-112v600h-125v-600h-110z" />
    <glyph glyph-name="uni0440" unicode="&#x440;" horiz-adv-x="377" 
d="M325 656q41 -44 41 -129v-91q0 -85 -41 -129t-123 -44h-52v-263h-110v700h162q82 0 123 -44zM150 363h52q27 0 40.5 15t13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237z" />
    <glyph glyph-name="uni0441" unicode="&#x441;" horiz-adv-x="386" 
d="M359 168q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v364q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-68h-104v75q0 69 -57 69t-57 -69v-378q0 -68 57 -68t57 68v100h104v-93z" />
    <glyph glyph-name="uni0442" unicode="&#x442;" horiz-adv-x="356" 
d="M348 700v-100h-115v-600h-110v600h-115v100h340z" />
    <glyph glyph-name="uni0443" unicode="&#x443;" horiz-adv-x="408" 
d="M250.5 76.5q-20.5 -38.5 -58 -58.5t-100.5 -20q-19 0 -30 1v98q7 -1 20 -1q35 0 53 13t24 45l1 6l-152 540h109l60 -239l34 -164l26 163l50 240h115l-118 -522q-13 -63 -33.5 -101.5z" />
    <glyph glyph-name="uni0444" unicode="&#x444;" horiz-adv-x="583" 
d="M195 79q-82 0 -123 44t-41 129v191q0 85 41 129t123 44h41v84h108v-84h41q82 0 123 -44t41 -129v-191q0 -85 -41 -129t-123 -44h-41v-79h-108v79h-41zM195 519q-27 0 -41 -16.5t-14 -52.5v-205q0 -70 55 -70h41v344h-41zM344 175h41q55 0 55 70v205q0 36 -14 52.5
t-41 16.5h-41v-344z" />
    <glyph glyph-name="uni0445" unicode="&#x445;" horiz-adv-x="430" 
d="M292 360l123 -360h-116l-90 277l-91 -277h-103l123 360l-115 340h114l83 -258l85 258h102z" />
    <glyph glyph-name="uni0446" unicode="&#x446;" horiz-adv-x="455" 
d="M150 700v-600h125v600h112v-602h48v-192h-107v94h-288v700h110z" />
    <glyph glyph-name="uni0447" unicode="&#x447;" horiz-adv-x="415" 
d="M374 0h-110v282q-17 -19 -43.5 -29t-50.5 -10q-66 0 -101.5 43.5t-35.5 117.5v296h110v-287q0 -34 18 -51.5t46 -17.5q27 0 42 18.5t15 53.5v284h110v-700z" />
    <glyph glyph-name="uni0448" unicode="&#x448;" horiz-adv-x="600" 
d="M40 700h110v-600h95v600h110v-600h95v600h110v-700h-520v700z" />
    <glyph glyph-name="uni0449" unicode="&#x449;" horiz-adv-x="629" 
d="M609 98v-192h-107v94h-462v700h110v-600h95v600h110v-600h95v600h110v-602h49z" />
    <glyph glyph-name="uni044A" unicode="&#x44a;" horiz-adv-x="439" 
d="M197 700v-263h52q82 0 123 -44t41 -129v-91q0 -85 -41 -129t-123 -44h-162v600h-79v100h189zM289.5 115q13.5 15 13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237h52q27 0 40.5 15z" />
    <glyph glyph-name="uni044B" unicode="&#x44b;" horiz-adv-x="579" 
d="M40 700h110v-263h52q82 0 123 -44t41 -129v-91q0 -85 -41 -129t-123 -44h-162v700zM429 700h110v-700h-110v700zM242.5 115q13.5 15 13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237h52q27 0 40.5 15z" />
    <glyph glyph-name="uni044C" unicode="&#x44c;" horiz-adv-x="392" 
d="M40 700h110v-263h52q82 0 123 -44t41 -129v-91q0 -85 -41 -129t-123 -44h-162v700zM242.5 115q13.5 15 13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237h52q27 0 40.5 15z" />
    <glyph glyph-name="uni044D" unicode="&#x44d;" horiz-adv-x="386" 
d="M130 161q0 -68 57 -68t57 68v151h-101v100h101v127q0 69 -57 69t-57 -69v-66h-102v59q0 85 41 130.5t121 45.5t122 -45.5t42 -130.5v-364q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v83h104v-90z" />
    <glyph glyph-name="uni044E" unicode="&#x44e;" horiz-adv-x="599" 
d="M150 300v-300h-110v700h110v-300h82v132q0 84 43 130t124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v132h-82zM399 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="uni044F" unicode="&#x44f;" horiz-adv-x="402" 
d="M30 36.5q2 18.5 2 48.5v108q0 58 17 95.5t56 53.5q-72 34 -72 142v55q0 82 39 121.5t124 39.5h166v-700h-110v285h-38q-38 0 -55 -20.5t-17 -68.5v-110q0 -39 -2 -54t-8 -32h-112q8 18 10 36.5zM156.5 583q-13.5 -17 -13.5 -55v-69q0 -40 16.5 -57t49.5 -17h43v215h-53
q-29 0 -42.5 -17z" />
    <glyph glyph-name="uni0451" unicode="&#x451;" horiz-adv-x="368" 
d="M161 737h-96v96h96v-96zM313 737h-96v96h96v-96zM301 405v-100h-151v-205h190v-100h-300v700h300v-100h-190v-195h151z" />
    <glyph glyph-name="uni1E02" unicode="&#x1e02;" horiz-adv-x="406" 
d="M234 737h-96v96h96v-96zM330 660.5q39 -39.5 39 -121.5v-25q0 -54 -17.5 -89t-53.5 -52q82 -32 82 -150v-57q0 -81 -42.5 -123.5t-124.5 -42.5h-173v700h166q85 0 124 -39.5zM150 415h43q33 0 49.5 17t16.5 57v39q0 38 -13.5 55t-42.5 17h-53v-185zM150 100h63
q29 0 43 15.5t14 53.5v61q0 48 -16.5 66.5t-54.5 18.5h-49v-215z" />
    <glyph glyph-name="uni1E03" unicode="&#x1e03;" horiz-adv-x="406" 
d="M234 737h-96v96h96v-96zM330 660.5q39 -39.5 39 -121.5v-25q0 -54 -17.5 -89t-53.5 -52q82 -32 82 -150v-57q0 -81 -42.5 -123.5t-124.5 -42.5h-173v700h166q85 0 124 -39.5zM150 415h43q33 0 49.5 17t16.5 57v39q0 38 -13.5 55t-42.5 17h-53v-185zM150 100h63
q29 0 43 15.5t14 53.5v61q0 48 -16.5 66.5t-54.5 18.5h-49v-215z" />
    <glyph glyph-name="uni1E0A" unicode="&#x1e0a;" horiz-adv-x="408" 
d="M242 737h-96v96h96v-96zM214 700q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v700h174zM150 100h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-500z" />
    <glyph glyph-name="uni1E0B" unicode="&#x1e0b;" horiz-adv-x="408" 
d="M242 737h-96v96h96v-96zM214 700q82 0 123 -44t41 -129v-354q0 -85 -41 -129t-123 -44h-174v700h174zM150 100h62q27 0 41.5 16t14.5 52v364q0 36 -14.5 52t-41.5 16h-62v-500z" />
    <glyph glyph-name="uni1E1E" unicode="&#x1e1e;" horiz-adv-x="344" 
d="M230 737h-96v96h96v-96zM292 389v-100h-142v-289h-110v700h291v-100h-181v-211h142z" />
    <glyph glyph-name="uni1E1F" unicode="&#x1e1f;" horiz-adv-x="344" 
d="M230 737h-96v96h96v-96zM292 389v-100h-142v-289h-110v700h291v-100h-181v-211h142z" />
    <glyph glyph-name="uni1E40" unicode="&#x1e40;" horiz-adv-x="541" 
d="M318 737h-96v96h96v-96zM348 700h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153l81 -497z" />
    <glyph glyph-name="uni1E41" unicode="&#x1e41;" horiz-adv-x="541" 
d="M318 737h-96v96h96v-96zM348 700h153v-700h-104v502l-76 -502h-104l-82 495v-495h-96v700h153l81 -497z" />
    <glyph glyph-name="uni1E56" unicode="&#x1e56;" horiz-adv-x="377" 
d="M238 737h-96v96h96v-96zM325 656q41 -44 41 -129v-91q0 -85 -41 -129t-123 -44h-52v-263h-110v700h162q82 0 123 -44zM150 363h52q27 0 40.5 15t13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237z" />
    <glyph glyph-name="uni1E57" unicode="&#x1e57;" horiz-adv-x="377" 
d="M238 737h-96v96h96v-96zM325 656q41 -44 41 -129v-91q0 -85 -41 -129t-123 -44h-52v-263h-110v700h162q82 0 123 -44zM150 363h52q27 0 40.5 15t13.5 51v105q0 36 -13.5 51t-40.5 15h-52v-237z" />
    <glyph glyph-name="uni1E60" unicode="&#x1e60;" horiz-adv-x="374" 
d="M235 737h-96v96h96v-96zM67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50
q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="uni1E61" unicode="&#x1e61;" horiz-adv-x="374" 
d="M235 737h-96v96h96v-96zM67 662.5q41 45.5 121 45.5t121 -45.5t41 -130.5v-22h-104v29q0 36 -14 52.5t-41 16.5t-41 -16.5t-14 -52.5q0 -44 23.5 -77t70.5 -75q39 -36 63 -63.5t41 -66.5t17 -89q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v43h104v-50
q0 -68 57 -68t57 68q0 44 -23.5 77t-70.5 75q-39 36 -63 63.5t-41 66.5t-17 89q0 85 41 130.5z" />
    <glyph glyph-name="uni1E6A" unicode="&#x1e6a;" horiz-adv-x="356" 
d="M226 737h-96v96h96v-96zM348 700v-100h-115v-600h-110v600h-115v100h340z" />
    <glyph glyph-name="uni1E6B" unicode="&#x1e6b;" horiz-adv-x="356" 
d="M226 737h-96v96h96v-96zM348 700v-100h-115v-600h-110v600h-115v100h340z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="563" 
d="M342 737h-79l-102 111h113zM244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="563" 
d="M342 737h-79l-102 111h113zM244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="563" 
d="M297 737h-74l68 111h108zM244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="563" 
d="M297 737h-74l68 111h108zM244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="563" 
d="M253 737h-96v96h96v-96zM405 737h-96v96h96v-96zM244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="563" 
d="M253 737h-96v96h96v-96zM405 737h-96v96h96v-96zM244 0h-151l-78 700h107l59 -552l53 552h106l55 -556l57 556h96l-78 -700h-146l-40 373z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" 
d="M263 737h-79l-102 111h113zM4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" 
d="M263 737h-79l-102 111h113zM4 700h115l84 -319l84 319h105l-139 -468v-232h-110v232z" />
    <glyph glyph-name="figuredash" unicode="&#x2012;" 
d="M396 395v-90h-396v90h396z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="500" 
d="M500 395v-90h-500v90h500z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1000" 
d="M1000 395v-90h-1000v90h1000z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="186" 
d="M146 494h-106v95l48 111h45l-29 -100h42v-106z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="186" 
d="M40 700h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="186" 
d="M40 106h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="332" 
d="M146 494h-106v95l48 111h45l-29 -100h42v-106zM292 494h-106v95l48 111h45l-29 -100h42v-106z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="332" 
d="M40 700h106v-95l-48 -111h-45l29 100h-42v106zM186 700h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="332" 
d="M40 106h106v-95l-48 -111h-45l29 100h-42v106zM186 106h106v-95l-48 -111h-45l29 100h-42v106z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M153 525v175h90v-175h128v-85h-128v-505h-90v505h-128v85h128z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M153 525v175h90v-175h128v-85h-128v-245h128v-85h-128v-175h-90v175h-128v85h128v245h-128v85h128z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" 
d="M77.5 420q18.5 32 50.5 50.5t70 18.5t70 -18.5t50.5 -50.5t18.5 -70t-18.5 -70t-50.5 -50.5t-70 -18.5t-70 18.5t-50.5 50.5t-18.5 70t18.5 70z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="478" 
d="M146 0h-106v106h106v-106zM292 0h-106v106h106v-106zM438 0h-106v106h106v-106z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="883" 
d="M61 676q27 29 79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230q0 54 27 83zM481 700l-276 -700h-65l276 700h65zM140 314q36 0 36 44v240q0 44 -36 44t-36 -44v-240q0 -44 36 -44zM408 420q27 29 79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29
t-79 29t-27 83v230q0 54 27 83zM664 420q27 29 79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230q0 54 27 83zM487 58q36 0 36 44v240q0 44 -36 44t-36 -44v-240q0 -44 36 -44zM743 58q36 0 36 44v240q0 44 -36 44t-36 -44v-240q0 -44 36 -44z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="196" 
d="M121 351l59 -282h-104l-60 282l60 266h104z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="196" 
d="M180 351l-60 -282h-104l59 282l-59 266h104z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="67" 
d="M204 700l-276 -700h-65l276 700h65z" />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="280" 
d="M61 771q27 29 79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230q0 54 27 83zM140 409q36 0 36 44v240q0 44 -36 44t-36 -44v-240q0 -44 36 -44z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="280" 
d="M151 795h76v-300h33v-64h-33v-80h-69v80h-134v64zM158 659l-71 -164h71v164z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="280" 
d="M103 452q0 -43 36 -43t36 43v97q0 44 -36 44t-36 -44v-13h-66l13 258h184v-64h-122l-5 -106q19 33 61 33q38 0 57.5 -26.5t19.5 -75.5v-99q0 -54 -26.5 -83t-77.5 -29t-77.5 29t-26.5 83v37h66v-41z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="280" 
d="M143 800q51 0 77.5 -29t26.5 -83v-11h-66v16q0 44 -36 44q-20 0 -29.5 -12t-9.5 -43v-81q19 39 65 39q38 0 57.5 -26t19.5 -75v-81q0 -54 -27.5 -83t-78.5 -29t-78.5 29t-27.5 83v227q0 115 107 115zM106 533v-80q0 -43 36 -43t36 43v80q0 44 -36 44t-36 -44z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="280" 
d="M245 734l-103 -383h-70l102 381h-139v63h210v-61z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="280" 
d="M220.5 375.5q-28.5 -29.5 -80.5 -29.5t-80.5 29.5t-28.5 82.5v34q0 69 43 93q-43 24 -43 89v14q0 53 28.5 82.5t80.5 29.5t80.5 -29.5t28.5 -82.5v-14q0 -65 -43 -89q43 -24 43 -93v-34q0 -53 -28.5 -82.5zM140 614q39 0 39 47v25q0 51 -39 51t-39 -51v-25q0 -47 39 -47z
M140 409q39 0 39 50v42q0 50 -39 50t-39 -50v-42q0 -50 39 -50z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="280" 
d="M137 346q-51 0 -77.5 29t-26.5 83v11h66v-16q0 -44 36 -44q20 0 29.5 12t9.5 43v81q-19 -39 -65 -39q-38 0 -57.5 26t-19.5 75v81q0 54 27.5 83t78.5 29t78.5 -29t27.5 -83v-227q0 -115 -107 -115zM174 613v80q0 43 -36 43t-36 -43v-80q0 -44 36 -44t36 44z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="280" 
d="M61 355q27 29 79 29t79 -29t27 -83v-230q0 -54 -27 -83t-79 -29t-79 29t-27 83v230q0 54 27 83zM140 -7q36 0 36 44v240q0 44 -36 44t-36 -44v-240q0 -44 36 -44z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="200" 
d="M76.5 337q15.5 14 26.5 42h46v-444h-70v339h-54v49q36 0 51.5 14z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="280" 
d="M171 311q-9 11 -27 11q-36 0 -36 -44v-47h-66v43q0 54 26.5 82.5t77.5 28.5t77.5 -28.5t26.5 -82.5t-22.5 -92t-63.5 -85q-27 -29 -40 -48.5t-13 -40.5q0 -7 1 -10h132v-63h-202v54q0 43 17.5 73.5t51.5 68.5q34 37 51.5 67.5t17.5 73.5q0 28 -9 39z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="280" 
d="M163.5 121.5q-10.5 12.5 -34.5 12.5h-24v63h27q42 0 42 48v25q0 28 -9 39.5t-27 11.5q-36 0 -36 -44v-28h-66v23q0 54 26.5 83t77.5 29t77.5 -29t26.5 -83v-11q0 -36 -12 -58.5t-37 -32.5q49 -21 49 -93v-35q0 -54 -26.5 -83t-77.5 -29t-77.5 29t-26.5 83v36h66v-41
q0 -44 36 -44q18 0 27 11.5t9 39.5v34q0 31 -10.5 43.5z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="280" 
d="M151 379h76v-300h33v-64h-33v-80h-69v80h-134v64zM158 243l-71 -164h71v164z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="280" 
d="M103 36q0 -43 36 -43t36 43v97q0 44 -36 44t-36 -44v-13h-66l13 258h184v-64h-122l-5 -106q19 33 61 33q38 0 57.5 -26.5t19.5 -75.5v-99q0 -54 -26.5 -83t-77.5 -29t-77.5 29t-26.5 83v37h66v-41z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="280" 
d="M143 384q51 0 77.5 -29t26.5 -83v-11h-66v16q0 44 -36 44q-20 0 -29.5 -12t-9.5 -43v-81q19 39 65 39q38 0 57.5 -26t19.5 -75v-81q0 -54 -27.5 -83t-78.5 -29t-78.5 29t-27.5 83v227q0 115 107 115zM106 117v-80q0 -43 36 -43t36 43v80q0 44 -36 44t-36 -44z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="280" 
d="M245 318l-103 -383h-70l102 381h-139v63h210v-61z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="280" 
d="M220.5 -40.5q-28.5 -29.5 -80.5 -29.5t-80.5 29.5t-28.5 82.5v34q0 69 43 93q-43 24 -43 89v14q0 53 28.5 82.5t80.5 29.5t80.5 -29.5t28.5 -82.5v-14q0 -65 -43 -89q43 -24 43 -93v-34q0 -53 -28.5 -82.5zM140 198q39 0 39 47v25q0 51 -39 51t-39 -51v-25q0 -47 39 -47z
M140 -7q39 0 39 50v42q0 50 -39 50t-39 -50v-42q0 -50 39 -50z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="280" 
d="M137 -70q-51 0 -77.5 29t-26.5 83v11h66v-16q0 -44 36 -44q20 0 29.5 12t9.5 43v81q-19 -39 -65 -39q-38 0 -57.5 26t-19.5 75v81q0 54 27.5 83t78.5 29t78.5 -29t27.5 -83v-227q0 -115 -107 -115zM174 197v80q0 43 -36 43t-36 -43v-80q0 -44 36 -44t36 44z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M47 435v97q0 85 40.5 130.5t120.5 45.5t120.5 -45.5t40.5 -130.5v-51h-104v58q0 37 -13.5 53t-40.5 16t-40.5 -16t-13.5 -53v-104h154v-55h-154v-55h154v-55h-154v-109q0 -36 13.5 -52t40.5 -16t40.5 16t13.5 52v63h104v-56q0 -85 -40.5 -130.5t-120.5 -45.5t-120.5 45.5
t-40.5 130.5v102h-28v55h28v55h-28v55h28z" />
    <glyph glyph-name="uni20B9" unicode="&#x20b9;" 
d="M181.5 600q-20.5 15 -63.5 15h-92v85h344v-85h-84q19 -28 25 -66h59v-85h-59q-6 -70 -52 -110.5t-111 -40.5h-5l233 -313h-118l-232 313v85h92q43 0 63.5 15t20.5 51h-176v85h176q0 36 -20.5 51z" />
    <glyph glyph-name="uni2117" unicode="&#x2117;" horiz-adv-x="736" 
d="M65.5 534q45.5 82 125 128t177.5 46t177.5 -46t125 -128t45.5 -184t-45.5 -184t-125 -128t-177.5 -46t-177.5 46t-125 128t-45.5 184t45.5 184zM128 202q36 -66 99 -103t141 -37t141 37t99 103t36 148t-36 148t-99 103t-141 37t-141 -37t-99 -103t-36 -148t36 -148z
M391 555q58 0 85.5 -27.5t27.5 -84.5v-56q0 -57 -27.5 -84.5t-85.5 -27.5h-40v-148h-76v428h116zM418.5 472.5q-9.5 12.5 -31.5 12.5h-36v-140h36q22 0 31.5 12.5t9.5 38.5v38q0 26 -9.5 38.5z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="586" 
d="M226 700v-64h-73v-292h-70v292h-73v64h216zM464 700h97v-356h-66v253l-47 -253h-66l-53 251v-251h-61v356h97l52 -250z" />
    <glyph glyph-name="uni2153" unicode="&#x2153;" horiz-adv-x="600" 
 />
    <glyph glyph-name="uni2154" unicode="&#x2154;" horiz-adv-x="600" 
 />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="627" 
d="M116.5 658q15.5 14 26.5 42h46v-444h-70v339h-54v49q36 0 51.5 14zM471 700l-276 -700h-65l276 700h65zM567.5 24.5q-28.5 -29.5 -80.5 -29.5t-80.5 29.5t-28.5 82.5v34q0 69 43 93q-43 24 -43 89v14q0 53 28.5 82.5t80.5 29.5t80.5 -29.5t28.5 -82.5v-14q0 -65 -43 -89
q43 -24 43 -93v-34q0 -53 -28.5 -82.5zM487 263q39 0 39 47v25q0 51 -39 51t-39 -51v-25q0 -47 39 -47zM487 58q39 0 39 50v42q0 50 -39 50t-39 -50v-42q0 -50 39 -50z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="627" 
d="M163.5 442.5q-10.5 12.5 -34.5 12.5h-24v63h27q42 0 42 48v25q0 28 -9 39.5t-27 11.5q-36 0 -36 -44v-28h-66v23q0 54 26.5 83t77.5 29t77.5 -29t26.5 -83v-11q0 -36 -12 -58.5t-37 -32.5q49 -21 49 -93v-35q0 -54 -26.5 -83t-77.5 -29t-77.5 29t-26.5 83v36h66v-41
q0 -44 36 -44q18 0 27 11.5t9 39.5v34q0 31 -10.5 43.5zM484 700l-276 -700h-65l276 700h65zM567.5 24.5q-28.5 -29.5 -80.5 -29.5t-80.5 29.5t-28.5 82.5v34q0 69 43 93q-43 24 -43 89v14q0 53 28.5 82.5t80.5 29.5t80.5 -29.5t28.5 -82.5v-14q0 -65 -43 -89q43 -24 43 -93
v-34q0 -53 -28.5 -82.5zM487 263q39 0 39 47v25q0 51 -39 51t-39 -51v-25q0 -47 39 -47zM487 58q39 0 39 50v42q0 50 -39 50t-39 -50v-42q0 -50 39 -50z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="627" 
d="M484 700l-276 -700h-65l276 700h65zM103 357q0 -43 36 -43t36 43v97q0 44 -36 44t-36 -44v-13h-66l13 258h184v-64h-122l-5 -106q19 33 61 33q38 0 57.5 -26.5t19.5 -75.5v-99q0 -54 -26.5 -83t-77.5 -29t-77.5 29t-26.5 83v37h66v-41zM567.5 24.5
q-28.5 -29.5 -80.5 -29.5t-80.5 29.5t-28.5 82.5v34q0 69 43 93q-43 24 -43 89v14q0 53 28.5 82.5t80.5 29.5t80.5 -29.5t28.5 -82.5v-14q0 -65 -43 -89q43 -24 43 -93v-34q0 -53 -28.5 -82.5zM487 263q39 0 39 47v25q0 51 -39 51t-39 -51v-25q0 -47 39 -47zM487 58
q39 0 39 50v42q0 50 -39 50t-39 -50v-42q0 -50 39 -50z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="627" 
d="M245 639l-103 -383h-70l102 381h-139v63h210v-61zM454 700l-276 -700h-65l276 700h65zM567.5 24.5q-28.5 -29.5 -80.5 -29.5t-80.5 29.5t-28.5 82.5v34q0 69 43 93q-43 24 -43 89v14q0 53 28.5 82.5t80.5 29.5t80.5 -29.5t28.5 -82.5v-14q0 -65 -43 -89q43 -24 43 -93
v-34q0 -53 -28.5 -82.5zM487 263q39 0 39 47v25q0 51 -39 51t-39 -51v-25q0 -47 39 -47zM487 58q39 0 39 50v42q0 50 -39 50t-39 -50v-42q0 -50 39 -50z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M371 311h-346v78h346v-78z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="600" 
d="M75 250.5q-37 34.5 -37 99.5t37 99.5t105 34.5h10q43 0 72 -12t44 -39q27 51 112 51h12q67 0 103.5 -34.5t36.5 -99.5t-36.5 -99.5t-103.5 -34.5h-12q-84 0 -112 51q-15 -27 -44 -39t-72 -12h-10q-68 0 -105 34.5zM129.5 393q-14.5 -16 -14.5 -43t14.5 -42t51.5 -16h20
q35 0 51.5 15.5t16.5 42.5t-17 43t-51 16h-20q-37 0 -51.5 -16zM354.5 393q-14.5 -16 -14.5 -43t14.5 -42t51.5 -16h20q35 0 51.5 15.5t16.5 42.5t-17 43t-51 16h-20q-37 0 -51.5 -16z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M69.5 485.5q27.5 19.5 57.5 19.5q19 0 36.5 -8t43.5 -25q22 -14 36 -21t27 -7q16 0 28.5 11.5t37.5 45.5l52 -54q-34 -50 -61 -68t-58 -18q-19 0 -36.5 8t-43.5 25q-22 14 -36 21t-27 7q-17 0 -29.5 -11.5t-36.5 -45.5l-52 50q34 51 61.5 70.5zM69.5 319.5
q27.5 19.5 57.5 19.5q19 0 36.5 -8t43.5 -25q22 -14 36 -21t27 -7q16 0 28.5 11.5t37.5 45.5l52 -54q-34 -50 -61 -68t-58 -18q-19 0 -36.5 8t-43.5 25q-22 14 -36 21t-27 7q-17 0 -29.5 -11.5t-36.5 -45.5l-52 50q34 51 61.5 70.5z" />
    <glyph glyph-name="uniF6C3" unicode="&#xf6c3;" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="zero.alt" 
d="M74 662q43 46 124 46t124 -46t43 -130v-364q0 -84 -43 -130t-124 -46t-124 46t-43 130v364q0 84 43 130zM198 92q57 0 57 69v378q0 69 -57 69t-57 -69v-378q0 -69 57 -69z" />
    <glyph glyph-name="one.alt" horiz-adv-x="250" 
d="M78.5 623.5q23.5 11.5 35 29.5t22.5 47h74v-700h-110v534h-85v78q40 0 63.5 11.5z" />
    <glyph glyph-name="two.alt" horiz-adv-x="369" 
d="M229.5 590.5q-14.5 17.5 -42.5 17.5q-57 0 -57 -69v-75h-104v68q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5q0 -59 -19.5 -108.5t-46.5 -85.5t-70 -83q-43 -48 -63 -78t-20 -62q0 -10 1 -15h208v-100h-318v86q0 49 15 87.5t37 67.5t58 68q37 41 58 68.5t35.5 65.5
t14.5 85q0 45 -14.5 62.5z" />
    <glyph glyph-name="three.alt" horiz-adv-x="382" 
d="M230.5 590.5q-14.5 17.5 -42.5 17.5q-57 0 -57 -69v-45h-104v38q0 85 42 130.5t122 45.5t122 -45.5t42 -130.5v-18q0 -57 -19 -92.5t-58 -51.5q77 -34 77 -147v-55q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v58h104v-65q0 -69 57 -69q28 0 42.5 17.5t14.5 61.5
v55q0 48 -17 68.5t-55 20.5h-37v100h43q33 0 49.5 17t16.5 57v39q0 45 -14.5 62.5z" />
    <glyph glyph-name="four.alt" horiz-adv-x="397" 
d="M214 700h120v-473h52v-100h-52v-127h-110v127h-210v100zM224 486l-109 -259h109v259z" />
    <glyph glyph-name="five.alt" horiz-adv-x="383" 
d="M132 161q0 -68 57 -68t57 68v154q0 69 -57 69t-57 -69v-21h-104l20 406h290v-100h-191l-9 -167q31 51 96 51q60 0 91 -41t31 -119v-156q0 -85 -42 -130.5t-122 -45.5t-122 45.5t-42 130.5v58h104v-65z" />
    <glyph glyph-name="six.alt" horiz-adv-x="392" 
d="M199 708q80 0 122 -45.5t42 -130.5v-18h-104v25q0 69 -57 69q-31 0 -46 -19t-15 -67v-128q29 62 102 62q60 0 91 -41t31 -119v-128q0 -84 -43 -130t-124 -46t-124 46t-43 130v358q0 182 168 182zM141 287v-126q0 -68 57 -68t57 68v126q0 69 -57 69t-57 -69z" />
    <glyph glyph-name="seven.alt" horiz-adv-x="340" 
d="M330 700v-96l-152 -604h-110l151 600h-209v100h320z" />
    <glyph glyph-name="eight.alt" horiz-adv-x="398" 
d="M326.5 38q-44.5 -46 -127.5 -46t-127.5 46t-44.5 130v55q0 108 68 146q-68 37 -68 141v22q0 84 44.5 130t127.5 46t127.5 -46t44.5 -130v-22q0 -103 -68 -141q68 -38 68 -146v-55q0 -84 -44.5 -130zM199 415q62 0 62 74v39q0 44 -16.5 62t-45.5 18t-45.5 -18t-16.5 -62
v-39q0 -74 62 -74zM153.5 110q16.5 -18 45.5 -18t45 17.5t17 61.5v65q0 79 -62 79t-62 -79v-65q0 -43 16.5 -61z" />
    <glyph glyph-name="nine.alt" horiz-adv-x="392" 
d="M193 -8q-80 0 -122 45.5t-42 130.5v18h104v-25q0 -69 57 -69q31 0 46 19t15 67v128q-29 -62 -102 -62q-60 0 -91 41t-31 119v128q0 84 43 130t124 46t124 -46t43 -130v-358q0 -182 -168 -182zM251 413v126q0 68 -57 68t-57 -68v-126q0 -69 57 -69t57 69z" />
    <glyph glyph-name="Edotaaccent" horiz-adv-x="600" 
 />
    <glyph glyph-name="edotaaccent" horiz-adv-x="600" 
 />
    <glyph glyph-name="lcute" horiz-adv-x="600" 
 />
    <hkern u1="&#x22;" g2="four.alt" k="40" />
    <hkern u1="&#x27;" g2="four.alt" k="40" />
    <hkern u1="&#x28;" u2="&#x1ef3;" k="-5" />
    <hkern u1="&#x28;" u2="&#x1ef2;" k="-5" />
    <hkern u1="&#x28;" u2="&#x1e85;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e84;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e83;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e82;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e81;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e80;" k="-3" />
    <hkern u1="&#x28;" u2="&#x1e6b;" k="-8" />
    <hkern u1="&#x28;" u2="&#x1e6a;" k="-8" />
    <hkern u1="&#x28;" u2="&#x21b;" k="-8" />
    <hkern u1="&#x28;" u2="&#x21a;" k="-8" />
    <hkern u1="&#x28;" u2="&#x178;" k="-5" />
    <hkern u1="&#x28;" u2="&#x177;" k="-5" />
    <hkern u1="&#x28;" u2="&#x176;" k="-5" />
    <hkern u1="&#x28;" u2="&#x175;" k="-3" />
    <hkern u1="&#x28;" u2="&#x174;" k="-3" />
    <hkern u1="&#x28;" u2="&#x167;" k="-8" />
    <hkern u1="&#x28;" u2="&#x166;" k="-8" />
    <hkern u1="&#x28;" u2="&#x165;" k="-8" />
    <hkern u1="&#x28;" u2="&#x164;" k="-8" />
    <hkern u1="&#x28;" u2="&#x163;" k="-8" />
    <hkern u1="&#x28;" u2="&#x162;" k="-8" />
    <hkern u1="&#x28;" u2="&#xff;" k="-5" />
    <hkern u1="&#x28;" u2="&#xfd;" k="-5" />
    <hkern u1="&#x28;" u2="&#xdd;" k="-5" />
    <hkern u1="&#x28;" u2="y" k="-5" />
    <hkern u1="&#x28;" u2="w" k="-3" />
    <hkern u1="&#x28;" u2="v" k="-3" />
    <hkern u1="&#x28;" u2="t" k="-8" />
    <hkern u1="&#x28;" u2="Y" k="-5" />
    <hkern u1="&#x28;" u2="W" k="-3" />
    <hkern u1="&#x28;" u2="V" k="-3" />
    <hkern u1="&#x28;" u2="T" k="-8" />
    <hkern u1="&#x2c;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2c;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2c;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2c;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2c;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2c;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2c;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2c;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2c;" u2="&#x219;" k="5" />
    <hkern u1="&#x2c;" u2="&#x218;" k="5" />
    <hkern u1="&#x2c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2c;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2c;" u2="&#x178;" k="80" />
    <hkern u1="&#x2c;" u2="&#x177;" k="80" />
    <hkern u1="&#x2c;" u2="&#x176;" k="80" />
    <hkern u1="&#x2c;" u2="&#x175;" k="40" />
    <hkern u1="&#x2c;" u2="&#x174;" k="40" />
    <hkern u1="&#x2c;" u2="&#x173;" k="6" />
    <hkern u1="&#x2c;" u2="&#x172;" k="6" />
    <hkern u1="&#x2c;" u2="&#x171;" k="6" />
    <hkern u1="&#x2c;" u2="&#x170;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2c;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2c;" u2="&#x169;" k="6" />
    <hkern u1="&#x2c;" u2="&#x168;" k="6" />
    <hkern u1="&#x2c;" u2="&#x167;" k="60" />
    <hkern u1="&#x2c;" u2="&#x166;" k="60" />
    <hkern u1="&#x2c;" u2="&#x165;" k="60" />
    <hkern u1="&#x2c;" u2="&#x164;" k="60" />
    <hkern u1="&#x2c;" u2="&#x163;" k="60" />
    <hkern u1="&#x2c;" u2="&#x162;" k="60" />
    <hkern u1="&#x2c;" u2="&#x161;" k="5" />
    <hkern u1="&#x2c;" u2="&#x160;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2c;" u2="&#x153;" k="10" />
    <hkern u1="&#x2c;" u2="&#x152;" k="10" />
    <hkern u1="&#x2c;" u2="&#x151;" k="10" />
    <hkern u1="&#x2c;" u2="&#x150;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x123;" k="10" />
    <hkern u1="&#x2c;" u2="&#x122;" k="10" />
    <hkern u1="&#x2c;" u2="&#x121;" k="10" />
    <hkern u1="&#x2c;" u2="&#x120;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2c;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2c;" u2="&#x109;" k="10" />
    <hkern u1="&#x2c;" u2="&#x108;" k="10" />
    <hkern u1="&#x2c;" u2="&#x107;" k="10" />
    <hkern u1="&#x2c;" u2="&#x106;" k="10" />
    <hkern u1="&#x2c;" u2="&#xff;" k="80" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2c;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2c;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2c;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2c;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2c;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2c;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2c;" u2="&#xda;" k="6" />
    <hkern u1="&#x2c;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2c;" u2="y" k="80" />
    <hkern u1="&#x2c;" u2="w" k="40" />
    <hkern u1="&#x2c;" u2="v" k="60" />
    <hkern u1="&#x2c;" u2="u" k="6" />
    <hkern u1="&#x2c;" u2="t" k="60" />
    <hkern u1="&#x2c;" u2="s" k="5" />
    <hkern u1="&#x2c;" u2="q" k="10" />
    <hkern u1="&#x2c;" u2="o" k="10" />
    <hkern u1="&#x2c;" u2="g" k="10" />
    <hkern u1="&#x2c;" u2="c" k="10" />
    <hkern u1="&#x2c;" u2="Y" k="80" />
    <hkern u1="&#x2c;" u2="W" k="40" />
    <hkern u1="&#x2c;" u2="V" k="60" />
    <hkern u1="&#x2c;" u2="U" k="6" />
    <hkern u1="&#x2c;" u2="T" k="60" />
    <hkern u1="&#x2c;" u2="S" k="5" />
    <hkern u1="&#x2c;" u2="Q" k="10" />
    <hkern u1="&#x2c;" u2="O" k="10" />
    <hkern u1="&#x2c;" u2="G" k="10" />
    <hkern u1="&#x2c;" u2="C" k="10" />
    <hkern u1="&#x2d;" u2="x" k="15" />
    <hkern u1="&#x2d;" u2="v" k="10" />
    <hkern u1="&#x2d;" u2="X" k="15" />
    <hkern u1="&#x2d;" u2="V" k="10" />
    <hkern u1="&#x2e;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2e;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2e;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2e;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2e;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2e;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2e;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2e;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2e;" u2="&#x219;" k="5" />
    <hkern u1="&#x2e;" u2="&#x218;" k="5" />
    <hkern u1="&#x2e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2e;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2e;" u2="&#x178;" k="80" />
    <hkern u1="&#x2e;" u2="&#x177;" k="80" />
    <hkern u1="&#x2e;" u2="&#x176;" k="80" />
    <hkern u1="&#x2e;" u2="&#x175;" k="40" />
    <hkern u1="&#x2e;" u2="&#x174;" k="40" />
    <hkern u1="&#x2e;" u2="&#x173;" k="6" />
    <hkern u1="&#x2e;" u2="&#x172;" k="6" />
    <hkern u1="&#x2e;" u2="&#x171;" k="6" />
    <hkern u1="&#x2e;" u2="&#x170;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2e;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2e;" u2="&#x169;" k="6" />
    <hkern u1="&#x2e;" u2="&#x168;" k="6" />
    <hkern u1="&#x2e;" u2="&#x167;" k="60" />
    <hkern u1="&#x2e;" u2="&#x166;" k="60" />
    <hkern u1="&#x2e;" u2="&#x165;" k="60" />
    <hkern u1="&#x2e;" u2="&#x164;" k="60" />
    <hkern u1="&#x2e;" u2="&#x163;" k="60" />
    <hkern u1="&#x2e;" u2="&#x162;" k="60" />
    <hkern u1="&#x2e;" u2="&#x161;" k="5" />
    <hkern u1="&#x2e;" u2="&#x160;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2e;" u2="&#x153;" k="10" />
    <hkern u1="&#x2e;" u2="&#x152;" k="10" />
    <hkern u1="&#x2e;" u2="&#x151;" k="10" />
    <hkern u1="&#x2e;" u2="&#x150;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x123;" k="10" />
    <hkern u1="&#x2e;" u2="&#x122;" k="10" />
    <hkern u1="&#x2e;" u2="&#x121;" k="10" />
    <hkern u1="&#x2e;" u2="&#x120;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2e;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2e;" u2="&#x109;" k="10" />
    <hkern u1="&#x2e;" u2="&#x108;" k="10" />
    <hkern u1="&#x2e;" u2="&#x107;" k="10" />
    <hkern u1="&#x2e;" u2="&#x106;" k="10" />
    <hkern u1="&#x2e;" u2="&#xff;" k="80" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2e;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2e;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2e;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2e;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2e;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2e;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2e;" u2="&#xda;" k="6" />
    <hkern u1="&#x2e;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2e;" u2="y" k="80" />
    <hkern u1="&#x2e;" u2="w" k="40" />
    <hkern u1="&#x2e;" u2="v" k="60" />
    <hkern u1="&#x2e;" u2="u" k="6" />
    <hkern u1="&#x2e;" u2="t" k="60" />
    <hkern u1="&#x2e;" u2="s" k="5" />
    <hkern u1="&#x2e;" u2="q" k="10" />
    <hkern u1="&#x2e;" u2="o" k="10" />
    <hkern u1="&#x2e;" u2="g" k="10" />
    <hkern u1="&#x2e;" u2="c" k="10" />
    <hkern u1="&#x2e;" u2="Y" k="80" />
    <hkern u1="&#x2e;" u2="W" k="40" />
    <hkern u1="&#x2e;" u2="V" k="60" />
    <hkern u1="&#x2e;" u2="U" k="6" />
    <hkern u1="&#x2e;" u2="T" k="60" />
    <hkern u1="&#x2e;" u2="S" k="5" />
    <hkern u1="&#x2e;" u2="Q" k="10" />
    <hkern u1="&#x2e;" u2="O" k="10" />
    <hkern u1="&#x2e;" u2="G" k="10" />
    <hkern u1="&#x2e;" u2="C" k="10" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="180" />
    <hkern u1="&#x3a;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x3a;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x3a;" u2="&#x1e6b;" k="35" />
    <hkern u1="&#x3a;" u2="&#x1e6a;" k="35" />
    <hkern u1="&#x3a;" u2="&#x21b;" k="35" />
    <hkern u1="&#x3a;" u2="&#x21a;" k="35" />
    <hkern u1="&#x3a;" u2="&#x178;" k="20" />
    <hkern u1="&#x3a;" u2="&#x177;" k="20" />
    <hkern u1="&#x3a;" u2="&#x176;" k="20" />
    <hkern u1="&#x3a;" u2="&#x167;" k="35" />
    <hkern u1="&#x3a;" u2="&#x166;" k="35" />
    <hkern u1="&#x3a;" u2="&#x165;" k="35" />
    <hkern u1="&#x3a;" u2="&#x164;" k="35" />
    <hkern u1="&#x3a;" u2="&#x163;" k="35" />
    <hkern u1="&#x3a;" u2="&#x162;" k="35" />
    <hkern u1="&#x3a;" u2="&#xff;" k="20" />
    <hkern u1="&#x3a;" u2="&#xfd;" k="20" />
    <hkern u1="&#x3a;" u2="&#xdd;" k="20" />
    <hkern u1="&#x3a;" u2="y" k="20" />
    <hkern u1="&#x3a;" u2="t" k="35" />
    <hkern u1="&#x3a;" u2="Y" k="20" />
    <hkern u1="&#x3a;" u2="T" k="35" />
    <hkern u1="&#x3b;" u2="&#x1ef3;" k="20" />
    <hkern u1="&#x3b;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x3b;" u2="&#x1e6b;" k="35" />
    <hkern u1="&#x3b;" u2="&#x1e6a;" k="35" />
    <hkern u1="&#x3b;" u2="&#x21b;" k="35" />
    <hkern u1="&#x3b;" u2="&#x21a;" k="35" />
    <hkern u1="&#x3b;" u2="&#x178;" k="20" />
    <hkern u1="&#x3b;" u2="&#x177;" k="20" />
    <hkern u1="&#x3b;" u2="&#x176;" k="20" />
    <hkern u1="&#x3b;" u2="&#x167;" k="35" />
    <hkern u1="&#x3b;" u2="&#x166;" k="35" />
    <hkern u1="&#x3b;" u2="&#x165;" k="35" />
    <hkern u1="&#x3b;" u2="&#x164;" k="35" />
    <hkern u1="&#x3b;" u2="&#x163;" k="35" />
    <hkern u1="&#x3b;" u2="&#x162;" k="35" />
    <hkern u1="&#x3b;" u2="&#xff;" k="20" />
    <hkern u1="&#x3b;" u2="&#xfd;" k="20" />
    <hkern u1="&#x3b;" u2="&#xdd;" k="20" />
    <hkern u1="&#x3b;" u2="y" k="20" />
    <hkern u1="&#x3b;" u2="t" k="35" />
    <hkern u1="&#x3b;" u2="Y" k="20" />
    <hkern u1="&#x3b;" u2="T" k="35" />
    <hkern u1="A" u2="&#x201d;" k="60" />
    <hkern u1="A" u2="&#x201c;" k="60" />
    <hkern u1="A" u2="&#x2018;" k="60" />
    <hkern u1="A" u2="&#x153;" k="1" />
    <hkern u1="A" u2="&#x152;" k="1" />
    <hkern u1="A" u2="v" k="25" />
    <hkern u1="A" u2="q" k="1" />
    <hkern u1="A" u2="V" k="25" />
    <hkern u1="A" u2="Q" k="1" />
    <hkern u1="A" u2="&#x3f;" k="30" />
    <hkern u1="B" u2="&#x2026;" k="5" />
    <hkern u1="B" u2="&#x201e;" k="5" />
    <hkern u1="B" u2="&#x201d;" k="10" />
    <hkern u1="B" u2="&#x201c;" k="10" />
    <hkern u1="B" u2="&#x201a;" k="5" />
    <hkern u1="B" u2="&#x2018;" k="10" />
    <hkern u1="B" u2="x" k="3" />
    <hkern u1="B" u2="v" k="4" />
    <hkern u1="B" u2="X" k="3" />
    <hkern u1="B" u2="V" k="4" />
    <hkern u1="B" u2="&#x2e;" k="5" />
    <hkern u1="B" u2="&#x2c;" k="5" />
    <hkern u1="C" u2="&#x2026;" k="6" />
    <hkern u1="C" u2="&#x201e;" k="6" />
    <hkern u1="C" u2="&#x201d;" k="6" />
    <hkern u1="C" u2="&#x201c;" k="6" />
    <hkern u1="C" u2="&#x201a;" k="6" />
    <hkern u1="C" u2="&#x2018;" k="6" />
    <hkern u1="C" u2="x" k="11" />
    <hkern u1="C" u2="X" k="11" />
    <hkern u1="C" u2="&#x2e;" k="6" />
    <hkern u1="C" u2="&#x2c;" k="6" />
    <hkern u1="D" u2="&#x2026;" k="10" />
    <hkern u1="D" u2="&#x201e;" k="10" />
    <hkern u1="D" u2="&#x201d;" k="10" />
    <hkern u1="D" u2="&#x201c;" k="10" />
    <hkern u1="D" u2="&#x201a;" k="10" />
    <hkern u1="D" u2="&#x2018;" k="10" />
    <hkern u1="D" u2="x" k="15" />
    <hkern u1="D" u2="v" k="1" />
    <hkern u1="D" u2="X" k="15" />
    <hkern u1="D" u2="V" k="1" />
    <hkern u1="D" u2="&#x2e;" k="10" />
    <hkern u1="D" u2="&#x2c;" k="10" />
    <hkern u1="F" u2="&#x2026;" k="70" />
    <hkern u1="F" u2="&#x201e;" k="70" />
    <hkern u1="F" u2="&#x201a;" k="70" />
    <hkern u1="F" u2="&#x7d;" k="-5" />
    <hkern u1="F" u2="]" k="-5" />
    <hkern u1="F" u2="&#x2e;" k="70" />
    <hkern u1="F" u2="&#x2c;" k="70" />
    <hkern u1="F" u2="&#x29;" k="-5" />
    <hkern u1="G" u2="&#x2026;" k="10" />
    <hkern u1="G" u2="&#x201e;" k="10" />
    <hkern u1="G" u2="&#x201d;" k="10" />
    <hkern u1="G" u2="&#x201c;" k="10" />
    <hkern u1="G" u2="&#x201a;" k="10" />
    <hkern u1="G" u2="&#x2018;" k="10" />
    <hkern u1="G" u2="x" k="15" />
    <hkern u1="G" u2="v" k="1" />
    <hkern u1="G" u2="X" k="15" />
    <hkern u1="G" u2="V" k="1" />
    <hkern u1="G" u2="&#x2e;" k="10" />
    <hkern u1="G" u2="&#x2c;" k="10" />
    <hkern u1="J" u2="&#x2026;" k="6" />
    <hkern u1="J" u2="&#x201e;" k="6" />
    <hkern u1="J" u2="&#x201a;" k="6" />
    <hkern u1="J" u2="&#x2e;" k="6" />
    <hkern u1="J" u2="&#x2c;" k="6" />
    <hkern u1="K" u2="&#x153;" k="7" />
    <hkern u1="K" u2="&#x152;" k="7" />
    <hkern u1="K" u2="q" k="7" />
    <hkern u1="K" u2="Q" k="7" />
    <hkern u1="L" u2="&#x201d;" k="75" />
    <hkern u1="L" u2="&#x201c;" k="75" />
    <hkern u1="L" u2="&#x2018;" k="75" />
    <hkern u1="L" u2="&#x7d;" k="-8" />
    <hkern u1="L" u2="v" k="33" />
    <hkern u1="L" u2="]" k="-8" />
    <hkern u1="L" u2="V" k="33" />
    <hkern u1="L" u2="&#x3f;" k="30" />
    <hkern u1="L" u2="&#x29;" k="-8" />
    <hkern u1="O" u2="&#x2026;" k="10" />
    <hkern u1="O" u2="&#x201e;" k="10" />
    <hkern u1="O" u2="&#x201d;" k="10" />
    <hkern u1="O" u2="&#x201c;" k="10" />
    <hkern u1="O" u2="&#x201a;" k="10" />
    <hkern u1="O" u2="&#x2018;" k="10" />
    <hkern u1="O" u2="x" k="15" />
    <hkern u1="O" u2="v" k="1" />
    <hkern u1="O" u2="X" k="15" />
    <hkern u1="O" u2="V" k="1" />
    <hkern u1="O" u2="&#x2e;" k="10" />
    <hkern u1="O" u2="&#x2c;" k="10" />
    <hkern u1="P" u2="&#x2026;" k="80" />
    <hkern u1="P" u2="&#x201e;" k="80" />
    <hkern u1="P" u2="&#x201a;" k="80" />
    <hkern u1="P" u2="x" k="11" />
    <hkern u1="P" u2="X" k="11" />
    <hkern u1="P" u2="&#x2e;" k="80" />
    <hkern u1="P" u2="&#x2c;" k="80" />
    <hkern u1="Q" u2="&#x201d;" k="10" />
    <hkern u1="Q" u2="&#x201c;" k="10" />
    <hkern u1="Q" u2="&#x2019;" k="10" />
    <hkern u1="Q" u2="&#x2018;" k="10" />
    <hkern u1="Q" u2="&#x1ef3;" k="13" />
    <hkern u1="Q" u2="&#x1ef2;" k="13" />
    <hkern u1="Q" u2="&#x2bc;" k="10" />
    <hkern u1="Q" u2="&#x178;" k="13" />
    <hkern u1="Q" u2="&#x177;" k="13" />
    <hkern u1="Q" u2="&#x176;" k="13" />
    <hkern u1="Q" u2="&#xff;" k="13" />
    <hkern u1="Q" u2="&#xfd;" k="13" />
    <hkern u1="Q" u2="&#xdd;" k="13" />
    <hkern u1="Q" u2="y" k="13" />
    <hkern u1="Q" u2="v" k="1" />
    <hkern u1="Q" u2="Y" k="13" />
    <hkern u1="Q" u2="V" k="1" />
    <hkern u1="R" u2="v" k="2" />
    <hkern u1="R" u2="V" k="2" />
    <hkern u1="S" u2="&#x2026;" k="5" />
    <hkern u1="S" u2="&#x201e;" k="5" />
    <hkern u1="S" u2="&#x201d;" k="5" />
    <hkern u1="S" u2="&#x201c;" k="5" />
    <hkern u1="S" u2="&#x201a;" k="5" />
    <hkern u1="S" u2="&#x2018;" k="5" />
    <hkern u1="S" u2="x" k="8" />
    <hkern u1="S" u2="v" k="1" />
    <hkern u1="S" u2="X" k="8" />
    <hkern u1="S" u2="V" k="1" />
    <hkern u1="S" u2="&#x2e;" k="5" />
    <hkern u1="S" u2="&#x2c;" k="5" />
    <hkern u1="T" u2="&#x2026;" k="60" />
    <hkern u1="T" u2="&#x201e;" k="60" />
    <hkern u1="T" u2="&#x201a;" k="60" />
    <hkern u1="T" u2="&#x7d;" k="-8" />
    <hkern u1="T" u2="]" k="-8" />
    <hkern u1="T" u2="&#x3b;" k="35" />
    <hkern u1="T" u2="&#x3a;" k="35" />
    <hkern u1="T" u2="&#x2e;" k="60" />
    <hkern u1="T" u2="&#x2c;" k="60" />
    <hkern u1="T" u2="&#x29;" k="-8" />
    <hkern u1="U" u2="&#x2026;" k="6" />
    <hkern u1="U" u2="&#x201e;" k="6" />
    <hkern u1="U" u2="&#x201a;" k="6" />
    <hkern u1="U" u2="&#x2e;" k="6" />
    <hkern u1="U" u2="&#x2c;" k="6" />
    <hkern u1="V" u2="&#x2026;" k="60" />
    <hkern u1="V" u2="&#x201e;" k="60" />
    <hkern u1="V" u2="&#x201a;" k="60" />
    <hkern u1="V" u2="&#x237;" k="25" />
    <hkern u1="V" u2="&#x1ff;" k="1" />
    <hkern u1="V" u2="&#x1fe;" k="1" />
    <hkern u1="V" u2="&#x1fd;" k="46" />
    <hkern u1="V" u2="&#x1fc;" k="46" />
    <hkern u1="V" u2="&#x153;" k="1" />
    <hkern u1="V" u2="&#x152;" k="1" />
    <hkern u1="V" u2="&#x151;" k="1" />
    <hkern u1="V" u2="&#x150;" k="1" />
    <hkern u1="V" u2="&#x14f;" k="1" />
    <hkern u1="V" u2="&#x14e;" k="1" />
    <hkern u1="V" u2="&#x14d;" k="1" />
    <hkern u1="V" u2="&#x14c;" k="1" />
    <hkern u1="V" u2="&#x135;" k="25" />
    <hkern u1="V" u2="&#x134;" k="25" />
    <hkern u1="V" u2="&#x133;" k="25" />
    <hkern u1="V" u2="&#x132;" k="25" />
    <hkern u1="V" u2="&#x123;" k="1" />
    <hkern u1="V" u2="&#x122;" k="1" />
    <hkern u1="V" u2="&#x121;" k="1" />
    <hkern u1="V" u2="&#x120;" k="1" />
    <hkern u1="V" u2="&#x11f;" k="1" />
    <hkern u1="V" u2="&#x11e;" k="1" />
    <hkern u1="V" u2="&#x11d;" k="1" />
    <hkern u1="V" u2="&#x11c;" k="1" />
    <hkern u1="V" u2="&#x10d;" k="1" />
    <hkern u1="V" u2="&#x10c;" k="1" />
    <hkern u1="V" u2="&#x10b;" k="1" />
    <hkern u1="V" u2="&#x10a;" k="1" />
    <hkern u1="V" u2="&#x109;" k="1" />
    <hkern u1="V" u2="&#x108;" k="1" />
    <hkern u1="V" u2="&#x107;" k="1" />
    <hkern u1="V" u2="&#x106;" k="1" />
    <hkern u1="V" u2="&#x105;" k="25" />
    <hkern u1="V" u2="&#x104;" k="25" />
    <hkern u1="V" u2="&#x103;" k="25" />
    <hkern u1="V" u2="&#x102;" k="25" />
    <hkern u1="V" u2="&#x101;" k="25" />
    <hkern u1="V" u2="&#x100;" k="25" />
    <hkern u1="V" u2="&#xf8;" k="1" />
    <hkern u1="V" u2="&#xf6;" k="1" />
    <hkern u1="V" u2="&#xf5;" k="1" />
    <hkern u1="V" u2="&#xf4;" k="1" />
    <hkern u1="V" u2="&#xf3;" k="1" />
    <hkern u1="V" u2="&#xf2;" k="1" />
    <hkern u1="V" u2="&#xe7;" k="1" />
    <hkern u1="V" u2="&#xe6;" k="46" />
    <hkern u1="V" u2="&#xe5;" k="25" />
    <hkern u1="V" u2="&#xe4;" k="25" />
    <hkern u1="V" u2="&#xe3;" k="25" />
    <hkern u1="V" u2="&#xe2;" k="25" />
    <hkern u1="V" u2="&#xe1;" k="25" />
    <hkern u1="V" u2="&#xe0;" k="25" />
    <hkern u1="V" u2="&#xd8;" k="1" />
    <hkern u1="V" u2="&#xd6;" k="1" />
    <hkern u1="V" u2="&#xd5;" k="1" />
    <hkern u1="V" u2="&#xd4;" k="1" />
    <hkern u1="V" u2="&#xd3;" k="1" />
    <hkern u1="V" u2="&#xd2;" k="1" />
    <hkern u1="V" u2="&#xc7;" k="1" />
    <hkern u1="V" u2="&#xc6;" k="46" />
    <hkern u1="V" u2="&#xc5;" k="25" />
    <hkern u1="V" u2="&#xc4;" k="25" />
    <hkern u1="V" u2="&#xc3;" k="25" />
    <hkern u1="V" u2="&#xc2;" k="25" />
    <hkern u1="V" u2="&#xc1;" k="25" />
    <hkern u1="V" u2="&#xc0;" k="25" />
    <hkern u1="V" u2="&#xad;" k="10" />
    <hkern u1="V" u2="&#x7d;" k="-3" />
    <hkern u1="V" u2="q" k="1" />
    <hkern u1="V" u2="o" k="1" />
    <hkern u1="V" u2="j" k="25" />
    <hkern u1="V" u2="g" k="1" />
    <hkern u1="V" u2="c" k="1" />
    <hkern u1="V" u2="a" k="25" />
    <hkern u1="V" u2="]" k="-3" />
    <hkern u1="V" u2="Q" k="1" />
    <hkern u1="V" u2="O" k="1" />
    <hkern u1="V" u2="J" k="25" />
    <hkern u1="V" u2="G" k="1" />
    <hkern u1="V" u2="C" k="1" />
    <hkern u1="V" u2="A" k="25" />
    <hkern u1="V" u2="&#x2e;" k="60" />
    <hkern u1="V" u2="&#x2d;" k="10" />
    <hkern u1="V" u2="&#x2c;" k="60" />
    <hkern u1="V" u2="&#x29;" k="-3" />
    <hkern u1="W" u2="&#x2026;" k="40" />
    <hkern u1="W" u2="&#x201e;" k="40" />
    <hkern u1="W" u2="&#x201a;" k="40" />
    <hkern u1="W" u2="&#x7d;" k="-3" />
    <hkern u1="W" u2="]" k="-3" />
    <hkern u1="W" u2="&#x2e;" k="40" />
    <hkern u1="W" u2="&#x2c;" k="40" />
    <hkern u1="W" u2="&#x29;" k="-3" />
    <hkern u1="X" u2="&#x201d;" k="2" />
    <hkern u1="X" u2="&#x201c;" k="2" />
    <hkern u1="X" u2="&#x2019;" k="2" />
    <hkern u1="X" u2="&#x2018;" k="2" />
    <hkern u1="X" u2="&#x1e61;" k="8" />
    <hkern u1="X" u2="&#x1e60;" k="8" />
    <hkern u1="X" u2="&#x2bc;" k="2" />
    <hkern u1="X" u2="&#x219;" k="8" />
    <hkern u1="X" u2="&#x218;" k="8" />
    <hkern u1="X" u2="&#x1ff;" k="15" />
    <hkern u1="X" u2="&#x1fe;" k="15" />
    <hkern u1="X" u2="&#x161;" k="8" />
    <hkern u1="X" u2="&#x160;" k="8" />
    <hkern u1="X" u2="&#x15f;" k="8" />
    <hkern u1="X" u2="&#x15e;" k="8" />
    <hkern u1="X" u2="&#x15d;" k="8" />
    <hkern u1="X" u2="&#x15c;" k="8" />
    <hkern u1="X" u2="&#x15b;" k="8" />
    <hkern u1="X" u2="&#x15a;" k="8" />
    <hkern u1="X" u2="&#x153;" k="15" />
    <hkern u1="X" u2="&#x152;" k="15" />
    <hkern u1="X" u2="&#x151;" k="15" />
    <hkern u1="X" u2="&#x150;" k="15" />
    <hkern u1="X" u2="&#x14f;" k="15" />
    <hkern u1="X" u2="&#x14e;" k="15" />
    <hkern u1="X" u2="&#x14d;" k="15" />
    <hkern u1="X" u2="&#x14c;" k="15" />
    <hkern u1="X" u2="&#x123;" k="15" />
    <hkern u1="X" u2="&#x122;" k="15" />
    <hkern u1="X" u2="&#x121;" k="15" />
    <hkern u1="X" u2="&#x120;" k="15" />
    <hkern u1="X" u2="&#x11f;" k="15" />
    <hkern u1="X" u2="&#x11e;" k="15" />
    <hkern u1="X" u2="&#x11d;" k="15" />
    <hkern u1="X" u2="&#x11c;" k="15" />
    <hkern u1="X" u2="&#x10d;" k="15" />
    <hkern u1="X" u2="&#x10c;" k="15" />
    <hkern u1="X" u2="&#x10b;" k="15" />
    <hkern u1="X" u2="&#x10a;" k="15" />
    <hkern u1="X" u2="&#x109;" k="15" />
    <hkern u1="X" u2="&#x108;" k="15" />
    <hkern u1="X" u2="&#x107;" k="15" />
    <hkern u1="X" u2="&#x106;" k="15" />
    <hkern u1="X" u2="&#xf8;" k="15" />
    <hkern u1="X" u2="&#xf6;" k="15" />
    <hkern u1="X" u2="&#xf5;" k="15" />
    <hkern u1="X" u2="&#xf4;" k="15" />
    <hkern u1="X" u2="&#xf3;" k="15" />
    <hkern u1="X" u2="&#xf2;" k="15" />
    <hkern u1="X" u2="&#xe7;" k="15" />
    <hkern u1="X" u2="&#xdf;" k="8" />
    <hkern u1="X" u2="&#xd8;" k="15" />
    <hkern u1="X" u2="&#xd6;" k="15" />
    <hkern u1="X" u2="&#xd5;" k="15" />
    <hkern u1="X" u2="&#xd4;" k="15" />
    <hkern u1="X" u2="&#xd3;" k="15" />
    <hkern u1="X" u2="&#xd2;" k="15" />
    <hkern u1="X" u2="&#xc7;" k="15" />
    <hkern u1="X" u2="&#xad;" k="15" />
    <hkern u1="X" u2="s" k="8" />
    <hkern u1="X" u2="q" k="15" />
    <hkern u1="X" u2="o" k="15" />
    <hkern u1="X" u2="g" k="15" />
    <hkern u1="X" u2="c" k="15" />
    <hkern u1="X" u2="S" k="8" />
    <hkern u1="X" u2="Q" k="15" />
    <hkern u1="X" u2="O" k="15" />
    <hkern u1="X" u2="G" k="15" />
    <hkern u1="X" u2="C" k="15" />
    <hkern u1="X" u2="&#x2d;" k="15" />
    <hkern u1="Y" u2="&#x2026;" k="80" />
    <hkern u1="Y" u2="&#x201e;" k="80" />
    <hkern u1="Y" u2="&#x201a;" k="80" />
    <hkern u1="Y" u2="&#x153;" k="15" />
    <hkern u1="Y" u2="&#x152;" k="15" />
    <hkern u1="Y" u2="&#x7d;" k="-5" />
    <hkern u1="Y" u2="q" k="15" />
    <hkern u1="Y" u2="]" k="-5" />
    <hkern u1="Y" u2="Q" k="15" />
    <hkern u1="Y" u2="&#x3b;" k="20" />
    <hkern u1="Y" u2="&#x3a;" k="20" />
    <hkern u1="Y" u2="&#x2e;" k="80" />
    <hkern u1="Y" u2="&#x2c;" k="80" />
    <hkern u1="Y" u2="&#x29;" k="-5" />
    <hkern u1="[" u2="&#x1ef3;" k="-5" />
    <hkern u1="[" u2="&#x1ef2;" k="-5" />
    <hkern u1="[" u2="&#x1e85;" k="-3" />
    <hkern u1="[" u2="&#x1e84;" k="-3" />
    <hkern u1="[" u2="&#x1e83;" k="-3" />
    <hkern u1="[" u2="&#x1e82;" k="-3" />
    <hkern u1="[" u2="&#x1e81;" k="-3" />
    <hkern u1="[" u2="&#x1e80;" k="-3" />
    <hkern u1="[" u2="&#x1e6b;" k="-8" />
    <hkern u1="[" u2="&#x1e6a;" k="-8" />
    <hkern u1="[" u2="&#x21b;" k="-8" />
    <hkern u1="[" u2="&#x21a;" k="-8" />
    <hkern u1="[" u2="&#x178;" k="-5" />
    <hkern u1="[" u2="&#x177;" k="-5" />
    <hkern u1="[" u2="&#x176;" k="-5" />
    <hkern u1="[" u2="&#x175;" k="-3" />
    <hkern u1="[" u2="&#x174;" k="-3" />
    <hkern u1="[" u2="&#x167;" k="-8" />
    <hkern u1="[" u2="&#x166;" k="-8" />
    <hkern u1="[" u2="&#x165;" k="-8" />
    <hkern u1="[" u2="&#x164;" k="-8" />
    <hkern u1="[" u2="&#x163;" k="-8" />
    <hkern u1="[" u2="&#x162;" k="-8" />
    <hkern u1="[" u2="&#xff;" k="-5" />
    <hkern u1="[" u2="&#xfd;" k="-5" />
    <hkern u1="[" u2="&#xdd;" k="-5" />
    <hkern u1="[" u2="y" k="-5" />
    <hkern u1="[" u2="w" k="-3" />
    <hkern u1="[" u2="v" k="-3" />
    <hkern u1="[" u2="t" k="-8" />
    <hkern u1="[" u2="Y" k="-5" />
    <hkern u1="[" u2="W" k="-3" />
    <hkern u1="[" u2="V" k="-3" />
    <hkern u1="[" u2="T" k="-8" />
    <hkern u1="a" u2="&#x201d;" k="60" />
    <hkern u1="a" u2="&#x201c;" k="60" />
    <hkern u1="a" u2="&#x2018;" k="60" />
    <hkern u1="a" u2="&#x153;" k="1" />
    <hkern u1="a" u2="&#x152;" k="1" />
    <hkern u1="a" u2="v" k="25" />
    <hkern u1="a" u2="q" k="1" />
    <hkern u1="a" u2="V" k="25" />
    <hkern u1="a" u2="Q" k="1" />
    <hkern u1="a" u2="&#x3f;" k="30" />
    <hkern u1="b" u2="&#x2026;" k="5" />
    <hkern u1="b" u2="&#x201e;" k="5" />
    <hkern u1="b" u2="&#x201d;" k="10" />
    <hkern u1="b" u2="&#x201c;" k="10" />
    <hkern u1="b" u2="&#x201a;" k="5" />
    <hkern u1="b" u2="&#x2018;" k="10" />
    <hkern u1="b" u2="x" k="3" />
    <hkern u1="b" u2="v" k="4" />
    <hkern u1="b" u2="X" k="3" />
    <hkern u1="b" u2="V" k="4" />
    <hkern u1="b" u2="&#x2e;" k="5" />
    <hkern u1="b" u2="&#x2c;" k="5" />
    <hkern u1="c" u2="&#x2026;" k="6" />
    <hkern u1="c" u2="&#x201e;" k="6" />
    <hkern u1="c" u2="&#x201d;" k="6" />
    <hkern u1="c" u2="&#x201c;" k="6" />
    <hkern u1="c" u2="&#x201a;" k="6" />
    <hkern u1="c" u2="&#x2018;" k="6" />
    <hkern u1="c" u2="x" k="11" />
    <hkern u1="c" u2="X" k="11" />
    <hkern u1="c" u2="&#x2e;" k="6" />
    <hkern u1="c" u2="&#x2c;" k="6" />
    <hkern u1="d" u2="&#x2026;" k="10" />
    <hkern u1="d" u2="&#x201e;" k="10" />
    <hkern u1="d" u2="&#x201d;" k="10" />
    <hkern u1="d" u2="&#x201c;" k="10" />
    <hkern u1="d" u2="&#x201a;" k="10" />
    <hkern u1="d" u2="&#x2018;" k="10" />
    <hkern u1="d" u2="x" k="15" />
    <hkern u1="d" u2="v" k="1" />
    <hkern u1="d" u2="X" k="15" />
    <hkern u1="d" u2="V" k="1" />
    <hkern u1="d" u2="&#x2e;" k="10" />
    <hkern u1="d" u2="&#x2c;" k="10" />
    <hkern u1="f" u2="&#x2026;" k="70" />
    <hkern u1="f" u2="&#x201e;" k="70" />
    <hkern u1="f" u2="&#x201a;" k="70" />
    <hkern u1="f" u2="&#x7d;" k="-5" />
    <hkern u1="f" u2="]" k="-5" />
    <hkern u1="f" u2="&#x2e;" k="70" />
    <hkern u1="f" u2="&#x2c;" k="70" />
    <hkern u1="f" u2="&#x29;" k="-5" />
    <hkern u1="g" u2="&#x2026;" k="10" />
    <hkern u1="g" u2="&#x201e;" k="10" />
    <hkern u1="g" u2="&#x201d;" k="10" />
    <hkern u1="g" u2="&#x201c;" k="10" />
    <hkern u1="g" u2="&#x201a;" k="10" />
    <hkern u1="g" u2="&#x2018;" k="10" />
    <hkern u1="g" u2="x" k="15" />
    <hkern u1="g" u2="v" k="1" />
    <hkern u1="g" u2="X" k="15" />
    <hkern u1="g" u2="V" k="1" />
    <hkern u1="g" u2="&#x2e;" k="10" />
    <hkern u1="g" u2="&#x2c;" k="10" />
    <hkern u1="j" u2="&#x2026;" k="6" />
    <hkern u1="j" u2="&#x201e;" k="6" />
    <hkern u1="j" u2="&#x201a;" k="6" />
    <hkern u1="j" u2="&#x2e;" k="6" />
    <hkern u1="j" u2="&#x2c;" k="6" />
    <hkern u1="k" u2="&#x153;" k="7" />
    <hkern u1="k" u2="&#x152;" k="7" />
    <hkern u1="k" u2="q" k="7" />
    <hkern u1="k" u2="Q" k="7" />
    <hkern u1="l" u2="&#x201d;" k="75" />
    <hkern u1="l" u2="&#x201c;" k="75" />
    <hkern u1="l" u2="&#x2018;" k="75" />
    <hkern u1="l" u2="&#x7d;" k="-8" />
    <hkern u1="l" u2="v" k="33" />
    <hkern u1="l" u2="]" k="-8" />
    <hkern u1="l" u2="V" k="33" />
    <hkern u1="l" u2="&#x3f;" k="30" />
    <hkern u1="l" u2="&#x29;" k="-8" />
    <hkern u1="o" u2="&#x2026;" k="10" />
    <hkern u1="o" u2="&#x201e;" k="10" />
    <hkern u1="o" u2="&#x201d;" k="10" />
    <hkern u1="o" u2="&#x201c;" k="10" />
    <hkern u1="o" u2="&#x201a;" k="10" />
    <hkern u1="o" u2="&#x2018;" k="10" />
    <hkern u1="o" u2="x" k="15" />
    <hkern u1="o" u2="v" k="1" />
    <hkern u1="o" u2="X" k="15" />
    <hkern u1="o" u2="V" k="1" />
    <hkern u1="o" u2="&#x2e;" k="10" />
    <hkern u1="o" u2="&#x2c;" k="10" />
    <hkern u1="p" u2="&#x2026;" k="80" />
    <hkern u1="p" u2="&#x201e;" k="80" />
    <hkern u1="p" u2="&#x201a;" k="80" />
    <hkern u1="p" u2="x" k="11" />
    <hkern u1="p" u2="X" k="11" />
    <hkern u1="p" u2="&#x2e;" k="80" />
    <hkern u1="p" u2="&#x2c;" k="80" />
    <hkern u1="q" u2="&#x201d;" k="10" />
    <hkern u1="q" u2="&#x201c;" k="10" />
    <hkern u1="q" u2="&#x2019;" k="10" />
    <hkern u1="q" u2="&#x2018;" k="10" />
    <hkern u1="q" u2="&#x1ef3;" k="13" />
    <hkern u1="q" u2="&#x1ef2;" k="13" />
    <hkern u1="q" u2="&#x2bc;" k="10" />
    <hkern u1="q" u2="&#x178;" k="13" />
    <hkern u1="q" u2="&#x177;" k="13" />
    <hkern u1="q" u2="&#x176;" k="13" />
    <hkern u1="q" u2="&#xff;" k="13" />
    <hkern u1="q" u2="&#xfd;" k="13" />
    <hkern u1="q" u2="&#xdd;" k="13" />
    <hkern u1="q" u2="y" k="13" />
    <hkern u1="q" u2="v" k="1" />
    <hkern u1="q" u2="Y" k="13" />
    <hkern u1="q" u2="V" k="1" />
    <hkern u1="r" u2="v" k="2" />
    <hkern u1="r" u2="V" k="2" />
    <hkern u1="s" u2="&#x2026;" k="5" />
    <hkern u1="s" u2="&#x201e;" k="5" />
    <hkern u1="s" u2="&#x201d;" k="5" />
    <hkern u1="s" u2="&#x201c;" k="5" />
    <hkern u1="s" u2="&#x201a;" k="5" />
    <hkern u1="s" u2="&#x2018;" k="5" />
    <hkern u1="s" u2="x" k="8" />
    <hkern u1="s" u2="v" k="1" />
    <hkern u1="s" u2="X" k="8" />
    <hkern u1="s" u2="V" k="1" />
    <hkern u1="s" u2="&#x2e;" k="5" />
    <hkern u1="s" u2="&#x2c;" k="5" />
    <hkern u1="t" u2="&#x2026;" k="60" />
    <hkern u1="t" u2="&#x201e;" k="60" />
    <hkern u1="t" u2="&#x201a;" k="60" />
    <hkern u1="t" u2="&#x7d;" k="-8" />
    <hkern u1="t" u2="]" k="-8" />
    <hkern u1="t" u2="&#x3b;" k="35" />
    <hkern u1="t" u2="&#x3a;" k="35" />
    <hkern u1="t" u2="&#x2e;" k="60" />
    <hkern u1="t" u2="&#x2c;" k="60" />
    <hkern u1="t" u2="&#x29;" k="-8" />
    <hkern u1="u" u2="&#x2026;" k="6" />
    <hkern u1="u" u2="&#x201e;" k="6" />
    <hkern u1="u" u2="&#x201a;" k="6" />
    <hkern u1="u" u2="&#x2e;" k="6" />
    <hkern u1="u" u2="&#x2c;" k="6" />
    <hkern u1="v" u2="&#x2026;" k="60" />
    <hkern u1="v" u2="&#x201e;" k="60" />
    <hkern u1="v" u2="&#x201a;" k="60" />
    <hkern u1="v" u2="&#x237;" k="25" />
    <hkern u1="v" u2="&#x1ff;" k="1" />
    <hkern u1="v" u2="&#x1fe;" k="1" />
    <hkern u1="v" u2="&#x1fd;" k="46" />
    <hkern u1="v" u2="&#x1fc;" k="46" />
    <hkern u1="v" u2="&#x153;" k="1" />
    <hkern u1="v" u2="&#x152;" k="1" />
    <hkern u1="v" u2="&#x151;" k="1" />
    <hkern u1="v" u2="&#x150;" k="1" />
    <hkern u1="v" u2="&#x14f;" k="1" />
    <hkern u1="v" u2="&#x14e;" k="1" />
    <hkern u1="v" u2="&#x14d;" k="1" />
    <hkern u1="v" u2="&#x14c;" k="1" />
    <hkern u1="v" u2="&#x135;" k="25" />
    <hkern u1="v" u2="&#x134;" k="25" />
    <hkern u1="v" u2="&#x133;" k="25" />
    <hkern u1="v" u2="&#x132;" k="25" />
    <hkern u1="v" u2="&#x123;" k="1" />
    <hkern u1="v" u2="&#x122;" k="1" />
    <hkern u1="v" u2="&#x121;" k="1" />
    <hkern u1="v" u2="&#x120;" k="1" />
    <hkern u1="v" u2="&#x11f;" k="1" />
    <hkern u1="v" u2="&#x11e;" k="1" />
    <hkern u1="v" u2="&#x11d;" k="1" />
    <hkern u1="v" u2="&#x11c;" k="1" />
    <hkern u1="v" u2="&#x10d;" k="1" />
    <hkern u1="v" u2="&#x10c;" k="1" />
    <hkern u1="v" u2="&#x10b;" k="1" />
    <hkern u1="v" u2="&#x10a;" k="1" />
    <hkern u1="v" u2="&#x109;" k="1" />
    <hkern u1="v" u2="&#x108;" k="1" />
    <hkern u1="v" u2="&#x107;" k="1" />
    <hkern u1="v" u2="&#x106;" k="1" />
    <hkern u1="v" u2="&#x105;" k="25" />
    <hkern u1="v" u2="&#x104;" k="25" />
    <hkern u1="v" u2="&#x103;" k="25" />
    <hkern u1="v" u2="&#x102;" k="25" />
    <hkern u1="v" u2="&#x101;" k="25" />
    <hkern u1="v" u2="&#x100;" k="25" />
    <hkern u1="v" u2="&#xf8;" k="1" />
    <hkern u1="v" u2="&#xf6;" k="1" />
    <hkern u1="v" u2="&#xf5;" k="1" />
    <hkern u1="v" u2="&#xf4;" k="1" />
    <hkern u1="v" u2="&#xf3;" k="1" />
    <hkern u1="v" u2="&#xf2;" k="1" />
    <hkern u1="v" u2="&#xe7;" k="1" />
    <hkern u1="v" u2="&#xe6;" k="46" />
    <hkern u1="v" u2="&#xe5;" k="25" />
    <hkern u1="v" u2="&#xe4;" k="25" />
    <hkern u1="v" u2="&#xe3;" k="25" />
    <hkern u1="v" u2="&#xe2;" k="25" />
    <hkern u1="v" u2="&#xe1;" k="25" />
    <hkern u1="v" u2="&#xe0;" k="25" />
    <hkern u1="v" u2="&#xd8;" k="1" />
    <hkern u1="v" u2="&#xd6;" k="1" />
    <hkern u1="v" u2="&#xd5;" k="1" />
    <hkern u1="v" u2="&#xd4;" k="1" />
    <hkern u1="v" u2="&#xd3;" k="1" />
    <hkern u1="v" u2="&#xd2;" k="1" />
    <hkern u1="v" u2="&#xc7;" k="1" />
    <hkern u1="v" u2="&#xc6;" k="46" />
    <hkern u1="v" u2="&#xc5;" k="25" />
    <hkern u1="v" u2="&#xc4;" k="25" />
    <hkern u1="v" u2="&#xc3;" k="25" />
    <hkern u1="v" u2="&#xc2;" k="25" />
    <hkern u1="v" u2="&#xc1;" k="25" />
    <hkern u1="v" u2="&#xc0;" k="25" />
    <hkern u1="v" u2="&#xad;" k="10" />
    <hkern u1="v" u2="&#x7d;" k="-3" />
    <hkern u1="v" u2="q" k="1" />
    <hkern u1="v" u2="o" k="1" />
    <hkern u1="v" u2="j" k="25" />
    <hkern u1="v" u2="g" k="1" />
    <hkern u1="v" u2="c" k="1" />
    <hkern u1="v" u2="a" k="25" />
    <hkern u1="v" u2="]" k="-3" />
    <hkern u1="v" u2="Q" k="1" />
    <hkern u1="v" u2="O" k="1" />
    <hkern u1="v" u2="J" k="25" />
    <hkern u1="v" u2="G" k="1" />
    <hkern u1="v" u2="C" k="1" />
    <hkern u1="v" u2="A" k="25" />
    <hkern u1="v" u2="&#x2e;" k="60" />
    <hkern u1="v" u2="&#x2d;" k="10" />
    <hkern u1="v" u2="&#x2c;" k="60" />
    <hkern u1="v" u2="&#x29;" k="-3" />
    <hkern u1="w" u2="&#x2026;" k="40" />
    <hkern u1="w" u2="&#x201e;" k="40" />
    <hkern u1="w" u2="&#x201a;" k="40" />
    <hkern u1="w" u2="&#x7d;" k="-3" />
    <hkern u1="w" u2="]" k="-3" />
    <hkern u1="w" u2="&#x2e;" k="40" />
    <hkern u1="w" u2="&#x2c;" k="40" />
    <hkern u1="w" u2="&#x29;" k="-3" />
    <hkern u1="x" u2="&#x201d;" k="2" />
    <hkern u1="x" u2="&#x201c;" k="2" />
    <hkern u1="x" u2="&#x2019;" k="2" />
    <hkern u1="x" u2="&#x2018;" k="2" />
    <hkern u1="x" u2="&#x1e61;" k="8" />
    <hkern u1="x" u2="&#x1e60;" k="8" />
    <hkern u1="x" u2="&#x2bc;" k="2" />
    <hkern u1="x" u2="&#x219;" k="8" />
    <hkern u1="x" u2="&#x218;" k="8" />
    <hkern u1="x" u2="&#x1ff;" k="15" />
    <hkern u1="x" u2="&#x1fe;" k="15" />
    <hkern u1="x" u2="&#x161;" k="8" />
    <hkern u1="x" u2="&#x160;" k="8" />
    <hkern u1="x" u2="&#x15f;" k="8" />
    <hkern u1="x" u2="&#x15e;" k="8" />
    <hkern u1="x" u2="&#x15d;" k="8" />
    <hkern u1="x" u2="&#x15c;" k="8" />
    <hkern u1="x" u2="&#x15b;" k="8" />
    <hkern u1="x" u2="&#x15a;" k="8" />
    <hkern u1="x" u2="&#x153;" k="15" />
    <hkern u1="x" u2="&#x152;" k="15" />
    <hkern u1="x" u2="&#x151;" k="15" />
    <hkern u1="x" u2="&#x150;" k="15" />
    <hkern u1="x" u2="&#x14f;" k="15" />
    <hkern u1="x" u2="&#x14e;" k="15" />
    <hkern u1="x" u2="&#x14d;" k="15" />
    <hkern u1="x" u2="&#x14c;" k="15" />
    <hkern u1="x" u2="&#x123;" k="15" />
    <hkern u1="x" u2="&#x122;" k="15" />
    <hkern u1="x" u2="&#x121;" k="15" />
    <hkern u1="x" u2="&#x120;" k="15" />
    <hkern u1="x" u2="&#x11f;" k="15" />
    <hkern u1="x" u2="&#x11e;" k="15" />
    <hkern u1="x" u2="&#x11d;" k="15" />
    <hkern u1="x" u2="&#x11c;" k="15" />
    <hkern u1="x" u2="&#x10d;" k="15" />
    <hkern u1="x" u2="&#x10c;" k="15" />
    <hkern u1="x" u2="&#x10b;" k="15" />
    <hkern u1="x" u2="&#x10a;" k="15" />
    <hkern u1="x" u2="&#x109;" k="15" />
    <hkern u1="x" u2="&#x108;" k="15" />
    <hkern u1="x" u2="&#x107;" k="15" />
    <hkern u1="x" u2="&#x106;" k="15" />
    <hkern u1="x" u2="&#xf8;" k="15" />
    <hkern u1="x" u2="&#xf6;" k="15" />
    <hkern u1="x" u2="&#xf5;" k="15" />
    <hkern u1="x" u2="&#xf4;" k="15" />
    <hkern u1="x" u2="&#xf3;" k="15" />
    <hkern u1="x" u2="&#xf2;" k="15" />
    <hkern u1="x" u2="&#xe7;" k="15" />
    <hkern u1="x" u2="&#xdf;" k="8" />
    <hkern u1="x" u2="&#xd8;" k="15" />
    <hkern u1="x" u2="&#xd6;" k="15" />
    <hkern u1="x" u2="&#xd5;" k="15" />
    <hkern u1="x" u2="&#xd4;" k="15" />
    <hkern u1="x" u2="&#xd3;" k="15" />
    <hkern u1="x" u2="&#xd2;" k="15" />
    <hkern u1="x" u2="&#xc7;" k="15" />
    <hkern u1="x" u2="&#xad;" k="15" />
    <hkern u1="x" u2="s" k="8" />
    <hkern u1="x" u2="q" k="15" />
    <hkern u1="x" u2="o" k="15" />
    <hkern u1="x" u2="g" k="15" />
    <hkern u1="x" u2="c" k="15" />
    <hkern u1="x" u2="S" k="8" />
    <hkern u1="x" u2="Q" k="15" />
    <hkern u1="x" u2="O" k="15" />
    <hkern u1="x" u2="G" k="15" />
    <hkern u1="x" u2="C" k="15" />
    <hkern u1="x" u2="&#x2d;" k="15" />
    <hkern u1="y" u2="&#x2026;" k="80" />
    <hkern u1="y" u2="&#x201e;" k="80" />
    <hkern u1="y" u2="&#x201a;" k="80" />
    <hkern u1="y" u2="&#x153;" k="15" />
    <hkern u1="y" u2="&#x152;" k="15" />
    <hkern u1="y" u2="&#x7d;" k="-5" />
    <hkern u1="y" u2="q" k="15" />
    <hkern u1="y" u2="]" k="-5" />
    <hkern u1="y" u2="Q" k="15" />
    <hkern u1="y" u2="&#x3b;" k="20" />
    <hkern u1="y" u2="&#x3a;" k="20" />
    <hkern u1="y" u2="&#x2e;" k="80" />
    <hkern u1="y" u2="&#x2c;" k="80" />
    <hkern u1="y" u2="&#x29;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1ef3;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1ef2;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x1e85;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e84;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e83;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e82;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e81;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e80;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x1e6b;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x1e6a;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x21b;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x21a;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x178;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x177;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x176;" k="-5" />
    <hkern u1="&#x7b;" u2="&#x175;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x174;" k="-3" />
    <hkern u1="&#x7b;" u2="&#x167;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x166;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x165;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x164;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x163;" k="-8" />
    <hkern u1="&#x7b;" u2="&#x162;" k="-8" />
    <hkern u1="&#x7b;" u2="&#xff;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xfd;" k="-5" />
    <hkern u1="&#x7b;" u2="&#xdd;" k="-5" />
    <hkern u1="&#x7b;" u2="y" k="-5" />
    <hkern u1="&#x7b;" u2="w" k="-3" />
    <hkern u1="&#x7b;" u2="v" k="-3" />
    <hkern u1="&#x7b;" u2="t" k="-8" />
    <hkern u1="&#x7b;" u2="Y" k="-5" />
    <hkern u1="&#x7b;" u2="W" k="-3" />
    <hkern u1="&#x7b;" u2="V" k="-3" />
    <hkern u1="&#x7b;" u2="T" k="-8" />
    <hkern u1="&#xad;" u2="x" k="15" />
    <hkern u1="&#xad;" u2="v" k="10" />
    <hkern u1="&#xad;" u2="X" k="15" />
    <hkern u1="&#xad;" u2="V" k="10" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="30" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="30" />
    <hkern u1="&#xbf;" u2="&#x1e85;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e84;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e83;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e82;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e81;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e80;" k="20" />
    <hkern u1="&#xbf;" u2="&#x1e6b;" k="42" />
    <hkern u1="&#xbf;" u2="&#x1e6a;" k="42" />
    <hkern u1="&#xbf;" u2="&#x21b;" k="42" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="42" />
    <hkern u1="&#xbf;" u2="&#x178;" k="30" />
    <hkern u1="&#xbf;" u2="&#x177;" k="30" />
    <hkern u1="&#xbf;" u2="&#x176;" k="30" />
    <hkern u1="&#xbf;" u2="&#x175;" k="20" />
    <hkern u1="&#xbf;" u2="&#x174;" k="20" />
    <hkern u1="&#xbf;" u2="&#x167;" k="42" />
    <hkern u1="&#xbf;" u2="&#x166;" k="42" />
    <hkern u1="&#xbf;" u2="&#x165;" k="42" />
    <hkern u1="&#xbf;" u2="&#x164;" k="42" />
    <hkern u1="&#xbf;" u2="&#x163;" k="42" />
    <hkern u1="&#xbf;" u2="&#x162;" k="42" />
    <hkern u1="&#xbf;" u2="&#xff;" k="30" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="30" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="30" />
    <hkern u1="&#xbf;" u2="y" k="30" />
    <hkern u1="&#xbf;" u2="w" k="20" />
    <hkern u1="&#xbf;" u2="v" k="30" />
    <hkern u1="&#xbf;" u2="t" k="42" />
    <hkern u1="&#xbf;" u2="Y" k="30" />
    <hkern u1="&#xbf;" u2="W" k="20" />
    <hkern u1="&#xbf;" u2="V" k="30" />
    <hkern u1="&#xbf;" u2="T" k="42" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc0;" u2="&#x153;" k="1" />
    <hkern u1="&#xc0;" u2="&#x152;" k="1" />
    <hkern u1="&#xc0;" u2="v" k="25" />
    <hkern u1="&#xc0;" u2="q" k="1" />
    <hkern u1="&#xc0;" u2="V" k="25" />
    <hkern u1="&#xc0;" u2="Q" k="1" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc1;" u2="&#x153;" k="1" />
    <hkern u1="&#xc1;" u2="&#x152;" k="1" />
    <hkern u1="&#xc1;" u2="v" k="25" />
    <hkern u1="&#xc1;" u2="q" k="1" />
    <hkern u1="&#xc1;" u2="V" k="25" />
    <hkern u1="&#xc1;" u2="Q" k="1" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc2;" u2="&#x153;" k="1" />
    <hkern u1="&#xc2;" u2="&#x152;" k="1" />
    <hkern u1="&#xc2;" u2="v" k="25" />
    <hkern u1="&#xc2;" u2="q" k="1" />
    <hkern u1="&#xc2;" u2="V" k="25" />
    <hkern u1="&#xc2;" u2="Q" k="1" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc3;" u2="&#x153;" k="1" />
    <hkern u1="&#xc3;" u2="&#x152;" k="1" />
    <hkern u1="&#xc3;" u2="v" k="25" />
    <hkern u1="&#xc3;" u2="q" k="1" />
    <hkern u1="&#xc3;" u2="V" k="25" />
    <hkern u1="&#xc3;" u2="Q" k="1" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc4;" u2="&#x153;" k="1" />
    <hkern u1="&#xc4;" u2="&#x152;" k="1" />
    <hkern u1="&#xc4;" u2="v" k="25" />
    <hkern u1="&#xc4;" u2="q" k="1" />
    <hkern u1="&#xc4;" u2="V" k="25" />
    <hkern u1="&#xc4;" u2="Q" k="1" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="60" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="60" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="60" />
    <hkern u1="&#xc5;" u2="&#x153;" k="1" />
    <hkern u1="&#xc5;" u2="&#x152;" k="1" />
    <hkern u1="&#xc5;" u2="v" k="25" />
    <hkern u1="&#xc5;" u2="q" k="1" />
    <hkern u1="&#xc5;" u2="V" k="25" />
    <hkern u1="&#xc5;" u2="Q" k="1" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xc7;" u2="&#x2026;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201e;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201d;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201c;" k="6" />
    <hkern u1="&#xc7;" u2="&#x201a;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2018;" k="6" />
    <hkern u1="&#xc7;" u2="x" k="11" />
    <hkern u1="&#xc7;" u2="X" k="11" />
    <hkern u1="&#xc7;" u2="&#x2e;" k="6" />
    <hkern u1="&#xc7;" u2="&#x2c;" k="6" />
    <hkern u1="&#xd0;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd0;" u2="x" k="15" />
    <hkern u1="&#xd0;" u2="v" k="1" />
    <hkern u1="&#xd0;" u2="X" k="15" />
    <hkern u1="&#xd0;" u2="V" k="1" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd2;" u2="x" k="15" />
    <hkern u1="&#xd2;" u2="v" k="1" />
    <hkern u1="&#xd2;" u2="X" k="15" />
    <hkern u1="&#xd2;" u2="V" k="1" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd3;" u2="x" k="15" />
    <hkern u1="&#xd3;" u2="v" k="1" />
    <hkern u1="&#xd3;" u2="X" k="15" />
    <hkern u1="&#xd3;" u2="V" k="1" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd4;" u2="x" k="15" />
    <hkern u1="&#xd4;" u2="v" k="1" />
    <hkern u1="&#xd4;" u2="X" k="15" />
    <hkern u1="&#xd4;" u2="V" k="1" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd5;" u2="x" k="15" />
    <hkern u1="&#xd5;" u2="v" k="1" />
    <hkern u1="&#xd5;" u2="X" k="15" />
    <hkern u1="&#xd5;" u2="V" k="1" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd6;" u2="x" k="15" />
    <hkern u1="&#xd6;" u2="v" k="1" />
    <hkern u1="&#xd6;" u2="X" k="15" />
    <hkern u1="&#xd6;" u2="V" k="1" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2026;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201e;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201d;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201c;" k="10" />
    <hkern u1="&#xd8;" u2="&#x201a;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2018;" k="10" />
    <hkern u1="&#xd8;" u2="x" k="15" />
    <hkern u1="&#xd8;" u2="v" k="1" />
    <hkern u1="&#xd8;" u2="X" k="15" />
    <hkern u1="&#xd8;" u2="V" k="1" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="10" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="10" />
    <hkern u1="&#xd9;" u2="&#x2026;" k="6" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="6" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="6" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="6" />
    <hkern u1="&#xda;" u2="&#x2026;" k="6" />
    <hkern u1="&#xda;" u2="&#x201e;" k="6" />
    <hkern u1="&#xda;" u2="&#x201a;" k="6" />
    <hkern u1="&#xda;" u2="&#x2e;" k="6" />
    <hkern u1="&#xda;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2026;" k="6" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="6" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="6" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2026;" k="6" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="6" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="6" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="6" />
    <hkern u1="&#xdd;" u2="&#x2026;" k="80" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="80" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="80" />
    <hkern u1="&#xdd;" u2="&#x153;" k="15" />
    <hkern u1="&#xdd;" u2="&#x152;" k="15" />
    <hkern u1="&#xdd;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xdd;" u2="q" k="15" />
    <hkern u1="&#xdd;" u2="]" k="-5" />
    <hkern u1="&#xdd;" u2="Q" k="15" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="20" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="20" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="80" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="80" />
    <hkern u1="&#xdd;" u2="&#x29;" k="-5" />
    <hkern u1="&#xde;" u2="&#x2026;" k="80" />
    <hkern u1="&#xde;" u2="&#x201e;" k="80" />
    <hkern u1="&#xde;" u2="&#x201a;" k="80" />
    <hkern u1="&#xde;" u2="&#x1ef3;" k="9" />
    <hkern u1="&#xde;" u2="&#x1ef2;" k="9" />
    <hkern u1="&#xde;" u2="&#x237;" k="8" />
    <hkern u1="&#xde;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xde;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xde;" u2="&#x17e;" k="10" />
    <hkern u1="&#xde;" u2="&#x17d;" k="10" />
    <hkern u1="&#xde;" u2="&#x17c;" k="10" />
    <hkern u1="&#xde;" u2="&#x17b;" k="10" />
    <hkern u1="&#xde;" u2="&#x17a;" k="10" />
    <hkern u1="&#xde;" u2="&#x179;" k="10" />
    <hkern u1="&#xde;" u2="&#x178;" k="9" />
    <hkern u1="&#xde;" u2="&#x177;" k="9" />
    <hkern u1="&#xde;" u2="&#x176;" k="9" />
    <hkern u1="&#xde;" u2="&#x135;" k="8" />
    <hkern u1="&#xde;" u2="&#x134;" k="8" />
    <hkern u1="&#xde;" u2="&#x133;" k="8" />
    <hkern u1="&#xde;" u2="&#x132;" k="8" />
    <hkern u1="&#xde;" u2="&#x105;" k="2" />
    <hkern u1="&#xde;" u2="&#x104;" k="2" />
    <hkern u1="&#xde;" u2="&#x103;" k="2" />
    <hkern u1="&#xde;" u2="&#x102;" k="2" />
    <hkern u1="&#xde;" u2="&#x101;" k="2" />
    <hkern u1="&#xde;" u2="&#x100;" k="2" />
    <hkern u1="&#xde;" u2="&#xff;" k="9" />
    <hkern u1="&#xde;" u2="&#xfd;" k="9" />
    <hkern u1="&#xde;" u2="&#xe6;" k="20" />
    <hkern u1="&#xde;" u2="&#xe5;" k="2" />
    <hkern u1="&#xde;" u2="&#xe4;" k="2" />
    <hkern u1="&#xde;" u2="&#xe3;" k="2" />
    <hkern u1="&#xde;" u2="&#xe2;" k="2" />
    <hkern u1="&#xde;" u2="&#xe1;" k="2" />
    <hkern u1="&#xde;" u2="&#xe0;" k="2" />
    <hkern u1="&#xde;" u2="&#xdd;" k="9" />
    <hkern u1="&#xde;" u2="&#xc6;" k="20" />
    <hkern u1="&#xde;" u2="&#xc5;" k="2" />
    <hkern u1="&#xde;" u2="&#xc4;" k="2" />
    <hkern u1="&#xde;" u2="&#xc3;" k="2" />
    <hkern u1="&#xde;" u2="&#xc2;" k="2" />
    <hkern u1="&#xde;" u2="&#xc1;" k="2" />
    <hkern u1="&#xde;" u2="&#xc0;" k="2" />
    <hkern u1="&#xde;" u2="z" k="10" />
    <hkern u1="&#xde;" u2="y" k="9" />
    <hkern u1="&#xde;" u2="x" k="20" />
    <hkern u1="&#xde;" u2="j" k="8" />
    <hkern u1="&#xde;" u2="a" k="2" />
    <hkern u1="&#xde;" u2="Z" k="10" />
    <hkern u1="&#xde;" u2="Y" k="9" />
    <hkern u1="&#xde;" u2="X" k="20" />
    <hkern u1="&#xde;" u2="J" k="8" />
    <hkern u1="&#xde;" u2="A" k="2" />
    <hkern u1="&#xde;" u2="&#x2e;" k="80" />
    <hkern u1="&#xde;" u2="&#x2c;" k="80" />
    <hkern u1="&#xdf;" u2="&#x2026;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201e;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201d;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201c;" k="5" />
    <hkern u1="&#xdf;" u2="&#x201a;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2018;" k="5" />
    <hkern u1="&#xdf;" u2="x" k="8" />
    <hkern u1="&#xdf;" u2="v" k="1" />
    <hkern u1="&#xdf;" u2="X" k="8" />
    <hkern u1="&#xdf;" u2="V" k="1" />
    <hkern u1="&#xdf;" u2="&#x2e;" k="5" />
    <hkern u1="&#xdf;" u2="&#x2c;" k="5" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe0;" u2="&#x153;" k="1" />
    <hkern u1="&#xe0;" u2="&#x152;" k="1" />
    <hkern u1="&#xe0;" u2="v" k="25" />
    <hkern u1="&#xe0;" u2="q" k="1" />
    <hkern u1="&#xe0;" u2="V" k="25" />
    <hkern u1="&#xe0;" u2="Q" k="1" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe1;" u2="&#x153;" k="1" />
    <hkern u1="&#xe1;" u2="&#x152;" k="1" />
    <hkern u1="&#xe1;" u2="v" k="25" />
    <hkern u1="&#xe1;" u2="q" k="1" />
    <hkern u1="&#xe1;" u2="V" k="25" />
    <hkern u1="&#xe1;" u2="Q" k="1" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe2;" u2="&#x153;" k="1" />
    <hkern u1="&#xe2;" u2="&#x152;" k="1" />
    <hkern u1="&#xe2;" u2="v" k="25" />
    <hkern u1="&#xe2;" u2="q" k="1" />
    <hkern u1="&#xe2;" u2="V" k="25" />
    <hkern u1="&#xe2;" u2="Q" k="1" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe3;" u2="&#x153;" k="1" />
    <hkern u1="&#xe3;" u2="&#x152;" k="1" />
    <hkern u1="&#xe3;" u2="v" k="25" />
    <hkern u1="&#xe3;" u2="q" k="1" />
    <hkern u1="&#xe3;" u2="V" k="25" />
    <hkern u1="&#xe3;" u2="Q" k="1" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe4;" u2="&#x153;" k="1" />
    <hkern u1="&#xe4;" u2="&#x152;" k="1" />
    <hkern u1="&#xe4;" u2="v" k="25" />
    <hkern u1="&#xe4;" u2="q" k="1" />
    <hkern u1="&#xe4;" u2="V" k="25" />
    <hkern u1="&#xe4;" u2="Q" k="1" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="60" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="60" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="60" />
    <hkern u1="&#xe5;" u2="&#x153;" k="1" />
    <hkern u1="&#xe5;" u2="&#x152;" k="1" />
    <hkern u1="&#xe5;" u2="v" k="25" />
    <hkern u1="&#xe5;" u2="q" k="1" />
    <hkern u1="&#xe5;" u2="V" k="25" />
    <hkern u1="&#xe5;" u2="Q" k="1" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="30" />
    <hkern u1="&#xe7;" u2="&#x2026;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201e;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201d;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201c;" k="6" />
    <hkern u1="&#xe7;" u2="&#x201a;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2018;" k="6" />
    <hkern u1="&#xe7;" u2="x" k="11" />
    <hkern u1="&#xe7;" u2="X" k="11" />
    <hkern u1="&#xe7;" u2="&#x2e;" k="6" />
    <hkern u1="&#xe7;" u2="&#x2c;" k="6" />
    <hkern u1="&#xf0;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf0;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf0;" u2="x" k="15" />
    <hkern u1="&#xf0;" u2="v" k="1" />
    <hkern u1="&#xf0;" u2="X" k="15" />
    <hkern u1="&#xf0;" u2="V" k="1" />
    <hkern u1="&#xf0;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf0;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf2;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf2;" u2="x" k="15" />
    <hkern u1="&#xf2;" u2="v" k="1" />
    <hkern u1="&#xf2;" u2="X" k="15" />
    <hkern u1="&#xf2;" u2="V" k="1" />
    <hkern u1="&#xf2;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf2;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf3;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf3;" u2="x" k="15" />
    <hkern u1="&#xf3;" u2="v" k="1" />
    <hkern u1="&#xf3;" u2="X" k="15" />
    <hkern u1="&#xf3;" u2="V" k="1" />
    <hkern u1="&#xf3;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf3;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf4;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf4;" u2="x" k="15" />
    <hkern u1="&#xf4;" u2="v" k="1" />
    <hkern u1="&#xf4;" u2="X" k="15" />
    <hkern u1="&#xf4;" u2="V" k="1" />
    <hkern u1="&#xf4;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf4;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf5;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf5;" u2="x" k="15" />
    <hkern u1="&#xf5;" u2="v" k="1" />
    <hkern u1="&#xf5;" u2="X" k="15" />
    <hkern u1="&#xf5;" u2="V" k="1" />
    <hkern u1="&#xf5;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf5;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf6;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf6;" u2="x" k="15" />
    <hkern u1="&#xf6;" u2="v" k="1" />
    <hkern u1="&#xf6;" u2="X" k="15" />
    <hkern u1="&#xf6;" u2="V" k="1" />
    <hkern u1="&#xf6;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf6;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2026;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201e;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="10" />
    <hkern u1="&#xf8;" u2="&#x201a;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="10" />
    <hkern u1="&#xf8;" u2="x" k="15" />
    <hkern u1="&#xf8;" u2="v" k="1" />
    <hkern u1="&#xf8;" u2="X" k="15" />
    <hkern u1="&#xf8;" u2="V" k="1" />
    <hkern u1="&#xf8;" u2="&#x2e;" k="10" />
    <hkern u1="&#xf8;" u2="&#x2c;" k="10" />
    <hkern u1="&#xf9;" u2="&#x2026;" k="6" />
    <hkern u1="&#xf9;" u2="&#x201e;" k="6" />
    <hkern u1="&#xf9;" u2="&#x201a;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2e;" k="6" />
    <hkern u1="&#xf9;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfa;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfa;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfa;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfb;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfb;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfb;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2026;" k="6" />
    <hkern u1="&#xfc;" u2="&#x201e;" k="6" />
    <hkern u1="&#xfc;" u2="&#x201a;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2e;" k="6" />
    <hkern u1="&#xfc;" u2="&#x2c;" k="6" />
    <hkern u1="&#xfd;" u2="&#x2026;" k="80" />
    <hkern u1="&#xfd;" u2="&#x201e;" k="80" />
    <hkern u1="&#xfd;" u2="&#x201a;" k="80" />
    <hkern u1="&#xfd;" u2="&#x153;" k="15" />
    <hkern u1="&#xfd;" u2="&#x152;" k="15" />
    <hkern u1="&#xfd;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xfd;" u2="q" k="15" />
    <hkern u1="&#xfd;" u2="]" k="-5" />
    <hkern u1="&#xfd;" u2="Q" k="15" />
    <hkern u1="&#xfd;" u2="&#x3b;" k="20" />
    <hkern u1="&#xfd;" u2="&#x3a;" k="20" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="80" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="80" />
    <hkern u1="&#xfd;" u2="&#x29;" k="-5" />
    <hkern u1="&#xfe;" u2="&#x2026;" k="80" />
    <hkern u1="&#xfe;" u2="&#x201e;" k="80" />
    <hkern u1="&#xfe;" u2="&#x201a;" k="80" />
    <hkern u1="&#xfe;" u2="&#x1ef3;" k="9" />
    <hkern u1="&#xfe;" u2="&#x1ef2;" k="9" />
    <hkern u1="&#xfe;" u2="&#x237;" k="8" />
    <hkern u1="&#xfe;" u2="&#x1fd;" k="20" />
    <hkern u1="&#xfe;" u2="&#x1fc;" k="20" />
    <hkern u1="&#xfe;" u2="&#x17e;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17d;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17c;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17b;" k="10" />
    <hkern u1="&#xfe;" u2="&#x17a;" k="10" />
    <hkern u1="&#xfe;" u2="&#x179;" k="10" />
    <hkern u1="&#xfe;" u2="&#x178;" k="9" />
    <hkern u1="&#xfe;" u2="&#x177;" k="9" />
    <hkern u1="&#xfe;" u2="&#x176;" k="9" />
    <hkern u1="&#xfe;" u2="&#x135;" k="8" />
    <hkern u1="&#xfe;" u2="&#x134;" k="8" />
    <hkern u1="&#xfe;" u2="&#x133;" k="8" />
    <hkern u1="&#xfe;" u2="&#x132;" k="8" />
    <hkern u1="&#xfe;" u2="&#x105;" k="2" />
    <hkern u1="&#xfe;" u2="&#x104;" k="2" />
    <hkern u1="&#xfe;" u2="&#x103;" k="2" />
    <hkern u1="&#xfe;" u2="&#x102;" k="2" />
    <hkern u1="&#xfe;" u2="&#x101;" k="2" />
    <hkern u1="&#xfe;" u2="&#x100;" k="2" />
    <hkern u1="&#xfe;" u2="&#xff;" k="9" />
    <hkern u1="&#xfe;" u2="&#xfd;" k="9" />
    <hkern u1="&#xfe;" u2="&#xe6;" k="20" />
    <hkern u1="&#xfe;" u2="&#xe5;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe4;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe3;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe2;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe1;" k="2" />
    <hkern u1="&#xfe;" u2="&#xe0;" k="2" />
    <hkern u1="&#xfe;" u2="&#xdd;" k="9" />
    <hkern u1="&#xfe;" u2="&#xc6;" k="20" />
    <hkern u1="&#xfe;" u2="&#xc5;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc4;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc3;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc2;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc1;" k="2" />
    <hkern u1="&#xfe;" u2="&#xc0;" k="2" />
    <hkern u1="&#xfe;" u2="z" k="10" />
    <hkern u1="&#xfe;" u2="y" k="9" />
    <hkern u1="&#xfe;" u2="x" k="20" />
    <hkern u1="&#xfe;" u2="j" k="8" />
    <hkern u1="&#xfe;" u2="a" k="2" />
    <hkern u1="&#xfe;" u2="Z" k="10" />
    <hkern u1="&#xfe;" u2="Y" k="9" />
    <hkern u1="&#xfe;" u2="X" k="20" />
    <hkern u1="&#xfe;" u2="J" k="8" />
    <hkern u1="&#xfe;" u2="A" k="2" />
    <hkern u1="&#xfe;" u2="&#x2e;" k="80" />
    <hkern u1="&#xfe;" u2="&#x2c;" k="80" />
    <hkern u1="&#xff;" u2="&#x2026;" k="80" />
    <hkern u1="&#xff;" u2="&#x201e;" k="80" />
    <hkern u1="&#xff;" u2="&#x201a;" k="80" />
    <hkern u1="&#xff;" u2="&#x153;" k="15" />
    <hkern u1="&#xff;" u2="&#x152;" k="15" />
    <hkern u1="&#xff;" u2="&#x7d;" k="-5" />
    <hkern u1="&#xff;" u2="q" k="15" />
    <hkern u1="&#xff;" u2="]" k="-5" />
    <hkern u1="&#xff;" u2="Q" k="15" />
    <hkern u1="&#xff;" u2="&#x3b;" k="20" />
    <hkern u1="&#xff;" u2="&#x3a;" k="20" />
    <hkern u1="&#xff;" u2="&#x2e;" k="80" />
    <hkern u1="&#xff;" u2="&#x2c;" k="80" />
    <hkern u1="&#xff;" u2="&#x29;" k="-5" />
    <hkern u1="&#x100;" u2="&#x201d;" k="60" />
    <hkern u1="&#x100;" u2="&#x201c;" k="60" />
    <hkern u1="&#x100;" u2="&#x2018;" k="60" />
    <hkern u1="&#x100;" u2="&#x153;" k="1" />
    <hkern u1="&#x100;" u2="&#x152;" k="1" />
    <hkern u1="&#x100;" u2="v" k="25" />
    <hkern u1="&#x100;" u2="q" k="1" />
    <hkern u1="&#x100;" u2="V" k="25" />
    <hkern u1="&#x100;" u2="Q" k="1" />
    <hkern u1="&#x100;" u2="&#x3f;" k="30" />
    <hkern u1="&#x101;" u2="&#x201d;" k="60" />
    <hkern u1="&#x101;" u2="&#x201c;" k="60" />
    <hkern u1="&#x101;" u2="&#x2018;" k="60" />
    <hkern u1="&#x101;" u2="&#x153;" k="1" />
    <hkern u1="&#x101;" u2="&#x152;" k="1" />
    <hkern u1="&#x101;" u2="v" k="25" />
    <hkern u1="&#x101;" u2="q" k="1" />
    <hkern u1="&#x101;" u2="V" k="25" />
    <hkern u1="&#x101;" u2="Q" k="1" />
    <hkern u1="&#x101;" u2="&#x3f;" k="30" />
    <hkern u1="&#x102;" u2="&#x201d;" k="60" />
    <hkern u1="&#x102;" u2="&#x201c;" k="60" />
    <hkern u1="&#x102;" u2="&#x2018;" k="60" />
    <hkern u1="&#x102;" u2="&#x153;" k="1" />
    <hkern u1="&#x102;" u2="&#x152;" k="1" />
    <hkern u1="&#x102;" u2="v" k="25" />
    <hkern u1="&#x102;" u2="q" k="1" />
    <hkern u1="&#x102;" u2="V" k="25" />
    <hkern u1="&#x102;" u2="Q" k="1" />
    <hkern u1="&#x102;" u2="&#x3f;" k="30" />
    <hkern u1="&#x103;" u2="&#x201d;" k="60" />
    <hkern u1="&#x103;" u2="&#x201c;" k="60" />
    <hkern u1="&#x103;" u2="&#x2018;" k="60" />
    <hkern u1="&#x103;" u2="&#x153;" k="1" />
    <hkern u1="&#x103;" u2="&#x152;" k="1" />
    <hkern u1="&#x103;" u2="v" k="25" />
    <hkern u1="&#x103;" u2="q" k="1" />
    <hkern u1="&#x103;" u2="V" k="25" />
    <hkern u1="&#x103;" u2="Q" k="1" />
    <hkern u1="&#x103;" u2="&#x3f;" k="30" />
    <hkern u1="&#x104;" u2="&#x201d;" k="60" />
    <hkern u1="&#x104;" u2="&#x201c;" k="60" />
    <hkern u1="&#x104;" u2="&#x2018;" k="60" />
    <hkern u1="&#x104;" u2="&#x153;" k="1" />
    <hkern u1="&#x104;" u2="&#x152;" k="1" />
    <hkern u1="&#x104;" u2="v" k="25" />
    <hkern u1="&#x104;" u2="q" k="1" />
    <hkern u1="&#x104;" u2="V" k="25" />
    <hkern u1="&#x104;" u2="Q" k="1" />
    <hkern u1="&#x104;" u2="&#x3f;" k="30" />
    <hkern u1="&#x105;" u2="&#x201d;" k="60" />
    <hkern u1="&#x105;" u2="&#x201c;" k="60" />
    <hkern u1="&#x105;" u2="&#x2018;" k="60" />
    <hkern u1="&#x105;" u2="&#x153;" k="1" />
    <hkern u1="&#x105;" u2="&#x152;" k="1" />
    <hkern u1="&#x105;" u2="v" k="25" />
    <hkern u1="&#x105;" u2="q" k="1" />
    <hkern u1="&#x105;" u2="V" k="25" />
    <hkern u1="&#x105;" u2="Q" k="1" />
    <hkern u1="&#x105;" u2="&#x3f;" k="30" />
    <hkern u1="&#x106;" u2="&#x2026;" k="6" />
    <hkern u1="&#x106;" u2="&#x201e;" k="6" />
    <hkern u1="&#x106;" u2="&#x201d;" k="6" />
    <hkern u1="&#x106;" u2="&#x201c;" k="6" />
    <hkern u1="&#x106;" u2="&#x201a;" k="6" />
    <hkern u1="&#x106;" u2="&#x2018;" k="6" />
    <hkern u1="&#x106;" u2="x" k="11" />
    <hkern u1="&#x106;" u2="X" k="11" />
    <hkern u1="&#x106;" u2="&#x2e;" k="6" />
    <hkern u1="&#x106;" u2="&#x2c;" k="6" />
    <hkern u1="&#x107;" u2="&#x2026;" k="6" />
    <hkern u1="&#x107;" u2="&#x201e;" k="6" />
    <hkern u1="&#x107;" u2="&#x201d;" k="6" />
    <hkern u1="&#x107;" u2="&#x201c;" k="6" />
    <hkern u1="&#x107;" u2="&#x201a;" k="6" />
    <hkern u1="&#x107;" u2="&#x2018;" k="6" />
    <hkern u1="&#x107;" u2="x" k="11" />
    <hkern u1="&#x107;" u2="X" k="11" />
    <hkern u1="&#x107;" u2="&#x2e;" k="6" />
    <hkern u1="&#x107;" u2="&#x2c;" k="6" />
    <hkern u1="&#x108;" u2="&#x2026;" k="6" />
    <hkern u1="&#x108;" u2="&#x201e;" k="6" />
    <hkern u1="&#x108;" u2="&#x201d;" k="6" />
    <hkern u1="&#x108;" u2="&#x201c;" k="6" />
    <hkern u1="&#x108;" u2="&#x201a;" k="6" />
    <hkern u1="&#x108;" u2="&#x2018;" k="6" />
    <hkern u1="&#x108;" u2="x" k="11" />
    <hkern u1="&#x108;" u2="X" k="11" />
    <hkern u1="&#x108;" u2="&#x2e;" k="6" />
    <hkern u1="&#x108;" u2="&#x2c;" k="6" />
    <hkern u1="&#x109;" u2="&#x2026;" k="6" />
    <hkern u1="&#x109;" u2="&#x201e;" k="6" />
    <hkern u1="&#x109;" u2="&#x201d;" k="6" />
    <hkern u1="&#x109;" u2="&#x201c;" k="6" />
    <hkern u1="&#x109;" u2="&#x201a;" k="6" />
    <hkern u1="&#x109;" u2="&#x2018;" k="6" />
    <hkern u1="&#x109;" u2="x" k="11" />
    <hkern u1="&#x109;" u2="X" k="11" />
    <hkern u1="&#x109;" u2="&#x2e;" k="6" />
    <hkern u1="&#x109;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10a;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10a;" u2="x" k="11" />
    <hkern u1="&#x10a;" u2="X" k="11" />
    <hkern u1="&#x10a;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10a;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10b;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10b;" u2="x" k="11" />
    <hkern u1="&#x10b;" u2="X" k="11" />
    <hkern u1="&#x10b;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10b;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10c;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10c;" u2="x" k="11" />
    <hkern u1="&#x10c;" u2="X" k="11" />
    <hkern u1="&#x10c;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10c;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2026;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201e;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201d;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201c;" k="6" />
    <hkern u1="&#x10d;" u2="&#x201a;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2018;" k="6" />
    <hkern u1="&#x10d;" u2="x" k="11" />
    <hkern u1="&#x10d;" u2="X" k="11" />
    <hkern u1="&#x10d;" u2="&#x2e;" k="6" />
    <hkern u1="&#x10d;" u2="&#x2c;" k="6" />
    <hkern u1="&#x10e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x10e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x10e;" u2="x" k="15" />
    <hkern u1="&#x10e;" u2="v" k="1" />
    <hkern u1="&#x10e;" u2="X" k="15" />
    <hkern u1="&#x10e;" u2="V" k="1" />
    <hkern u1="&#x10e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x10e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x10f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x10f;" u2="x" k="15" />
    <hkern u1="&#x10f;" u2="v" k="1" />
    <hkern u1="&#x10f;" u2="X" k="15" />
    <hkern u1="&#x10f;" u2="V" k="1" />
    <hkern u1="&#x10f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x10f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x110;" u2="&#x2026;" k="10" />
    <hkern u1="&#x110;" u2="&#x201e;" k="10" />
    <hkern u1="&#x110;" u2="&#x201d;" k="10" />
    <hkern u1="&#x110;" u2="&#x201c;" k="10" />
    <hkern u1="&#x110;" u2="&#x201a;" k="10" />
    <hkern u1="&#x110;" u2="&#x2018;" k="10" />
    <hkern u1="&#x110;" u2="x" k="15" />
    <hkern u1="&#x110;" u2="v" k="1" />
    <hkern u1="&#x110;" u2="X" k="15" />
    <hkern u1="&#x110;" u2="V" k="1" />
    <hkern u1="&#x110;" u2="&#x2e;" k="10" />
    <hkern u1="&#x110;" u2="&#x2c;" k="10" />
    <hkern u1="&#x111;" u2="&#x2026;" k="10" />
    <hkern u1="&#x111;" u2="&#x201e;" k="10" />
    <hkern u1="&#x111;" u2="&#x201d;" k="10" />
    <hkern u1="&#x111;" u2="&#x201c;" k="10" />
    <hkern u1="&#x111;" u2="&#x201a;" k="10" />
    <hkern u1="&#x111;" u2="&#x2018;" k="10" />
    <hkern u1="&#x111;" u2="x" k="15" />
    <hkern u1="&#x111;" u2="v" k="1" />
    <hkern u1="&#x111;" u2="X" k="15" />
    <hkern u1="&#x111;" u2="V" k="1" />
    <hkern u1="&#x111;" u2="&#x2e;" k="10" />
    <hkern u1="&#x111;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11c;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11c;" u2="x" k="15" />
    <hkern u1="&#x11c;" u2="v" k="1" />
    <hkern u1="&#x11c;" u2="X" k="15" />
    <hkern u1="&#x11c;" u2="V" k="1" />
    <hkern u1="&#x11c;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11c;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11d;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11d;" u2="x" k="15" />
    <hkern u1="&#x11d;" u2="v" k="1" />
    <hkern u1="&#x11d;" u2="X" k="15" />
    <hkern u1="&#x11d;" u2="V" k="1" />
    <hkern u1="&#x11d;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11d;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11e;" u2="x" k="15" />
    <hkern u1="&#x11e;" u2="v" k="1" />
    <hkern u1="&#x11e;" u2="X" k="15" />
    <hkern u1="&#x11e;" u2="V" k="1" />
    <hkern u1="&#x11e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x11f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x11f;" u2="x" k="15" />
    <hkern u1="&#x11f;" u2="v" k="1" />
    <hkern u1="&#x11f;" u2="X" k="15" />
    <hkern u1="&#x11f;" u2="V" k="1" />
    <hkern u1="&#x11f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x11f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x120;" u2="&#x2026;" k="10" />
    <hkern u1="&#x120;" u2="&#x201e;" k="10" />
    <hkern u1="&#x120;" u2="&#x201d;" k="10" />
    <hkern u1="&#x120;" u2="&#x201c;" k="10" />
    <hkern u1="&#x120;" u2="&#x201a;" k="10" />
    <hkern u1="&#x120;" u2="&#x2018;" k="10" />
    <hkern u1="&#x120;" u2="x" k="15" />
    <hkern u1="&#x120;" u2="v" k="1" />
    <hkern u1="&#x120;" u2="X" k="15" />
    <hkern u1="&#x120;" u2="V" k="1" />
    <hkern u1="&#x120;" u2="&#x2e;" k="10" />
    <hkern u1="&#x120;" u2="&#x2c;" k="10" />
    <hkern u1="&#x121;" u2="&#x2026;" k="10" />
    <hkern u1="&#x121;" u2="&#x201e;" k="10" />
    <hkern u1="&#x121;" u2="&#x201d;" k="10" />
    <hkern u1="&#x121;" u2="&#x201c;" k="10" />
    <hkern u1="&#x121;" u2="&#x201a;" k="10" />
    <hkern u1="&#x121;" u2="&#x2018;" k="10" />
    <hkern u1="&#x121;" u2="x" k="15" />
    <hkern u1="&#x121;" u2="v" k="1" />
    <hkern u1="&#x121;" u2="X" k="15" />
    <hkern u1="&#x121;" u2="V" k="1" />
    <hkern u1="&#x121;" u2="&#x2e;" k="10" />
    <hkern u1="&#x121;" u2="&#x2c;" k="10" />
    <hkern u1="&#x122;" u2="&#x2026;" k="10" />
    <hkern u1="&#x122;" u2="&#x201e;" k="10" />
    <hkern u1="&#x122;" u2="&#x201d;" k="10" />
    <hkern u1="&#x122;" u2="&#x201c;" k="10" />
    <hkern u1="&#x122;" u2="&#x201a;" k="10" />
    <hkern u1="&#x122;" u2="&#x2018;" k="10" />
    <hkern u1="&#x122;" u2="x" k="15" />
    <hkern u1="&#x122;" u2="v" k="1" />
    <hkern u1="&#x122;" u2="X" k="15" />
    <hkern u1="&#x122;" u2="V" k="1" />
    <hkern u1="&#x122;" u2="&#x2e;" k="10" />
    <hkern u1="&#x122;" u2="&#x2c;" k="10" />
    <hkern u1="&#x123;" u2="&#x2026;" k="10" />
    <hkern u1="&#x123;" u2="&#x201e;" k="10" />
    <hkern u1="&#x123;" u2="&#x201d;" k="10" />
    <hkern u1="&#x123;" u2="&#x201c;" k="10" />
    <hkern u1="&#x123;" u2="&#x201a;" k="10" />
    <hkern u1="&#x123;" u2="&#x2018;" k="10" />
    <hkern u1="&#x123;" u2="x" k="15" />
    <hkern u1="&#x123;" u2="v" k="1" />
    <hkern u1="&#x123;" u2="X" k="15" />
    <hkern u1="&#x123;" u2="V" k="1" />
    <hkern u1="&#x123;" u2="&#x2e;" k="10" />
    <hkern u1="&#x123;" u2="&#x2c;" k="10" />
    <hkern u1="&#x134;" u2="&#x2026;" k="6" />
    <hkern u1="&#x134;" u2="&#x201e;" k="6" />
    <hkern u1="&#x134;" u2="&#x201a;" k="6" />
    <hkern u1="&#x134;" u2="&#x2e;" k="6" />
    <hkern u1="&#x134;" u2="&#x2c;" k="6" />
    <hkern u1="&#x135;" u2="&#x2026;" k="6" />
    <hkern u1="&#x135;" u2="&#x201e;" k="6" />
    <hkern u1="&#x135;" u2="&#x201a;" k="6" />
    <hkern u1="&#x135;" u2="&#x2e;" k="6" />
    <hkern u1="&#x135;" u2="&#x2c;" k="6" />
    <hkern u1="&#x136;" u2="&#x153;" k="7" />
    <hkern u1="&#x136;" u2="&#x152;" k="7" />
    <hkern u1="&#x136;" u2="q" k="7" />
    <hkern u1="&#x136;" u2="Q" k="7" />
    <hkern u1="&#x137;" u2="&#x153;" k="7" />
    <hkern u1="&#x137;" u2="&#x152;" k="7" />
    <hkern u1="&#x137;" u2="q" k="7" />
    <hkern u1="&#x137;" u2="Q" k="7" />
    <hkern u1="&#x138;" u2="&#x153;" k="7" />
    <hkern u1="&#x138;" u2="&#x152;" k="7" />
    <hkern u1="&#x138;" u2="q" k="7" />
    <hkern u1="&#x138;" u2="Q" k="7" />
    <hkern u1="&#x139;" u2="&#x201d;" k="75" />
    <hkern u1="&#x139;" u2="&#x201c;" k="75" />
    <hkern u1="&#x139;" u2="&#x2018;" k="75" />
    <hkern u1="&#x139;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x139;" u2="v" k="33" />
    <hkern u1="&#x139;" u2="]" k="-8" />
    <hkern u1="&#x139;" u2="V" k="33" />
    <hkern u1="&#x139;" u2="&#x3f;" k="30" />
    <hkern u1="&#x139;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13a;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13a;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13a;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13a;" u2="v" k="33" />
    <hkern u1="&#x13a;" u2="]" k="-8" />
    <hkern u1="&#x13a;" u2="V" k="33" />
    <hkern u1="&#x13a;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13b;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13b;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13b;" u2="v" k="33" />
    <hkern u1="&#x13b;" u2="]" k="-8" />
    <hkern u1="&#x13b;" u2="V" k="33" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13c;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13c;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13c;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13c;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13c;" u2="v" k="33" />
    <hkern u1="&#x13c;" u2="]" k="-8" />
    <hkern u1="&#x13c;" u2="V" k="33" />
    <hkern u1="&#x13c;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13c;" u2="&#x29;" k="-8" />
    <hkern u1="&#x13f;" u2="&#x201d;" k="75" />
    <hkern u1="&#x13f;" u2="&#x201c;" k="75" />
    <hkern u1="&#x13f;" u2="&#x2018;" k="75" />
    <hkern u1="&#x13f;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x13f;" u2="v" k="33" />
    <hkern u1="&#x13f;" u2="]" k="-8" />
    <hkern u1="&#x13f;" u2="V" k="33" />
    <hkern u1="&#x13f;" u2="&#x3f;" k="30" />
    <hkern u1="&#x13f;" u2="&#x29;" k="-8" />
    <hkern u1="&#x140;" u2="&#x201d;" k="75" />
    <hkern u1="&#x140;" u2="&#x201c;" k="75" />
    <hkern u1="&#x140;" u2="&#x2018;" k="75" />
    <hkern u1="&#x140;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x140;" u2="v" k="33" />
    <hkern u1="&#x140;" u2="]" k="-8" />
    <hkern u1="&#x140;" u2="V" k="33" />
    <hkern u1="&#x140;" u2="&#x3f;" k="30" />
    <hkern u1="&#x140;" u2="&#x29;" k="-8" />
    <hkern u1="&#x141;" u2="&#x201d;" k="75" />
    <hkern u1="&#x141;" u2="&#x201c;" k="75" />
    <hkern u1="&#x141;" u2="&#x2018;" k="75" />
    <hkern u1="&#x141;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x141;" u2="v" k="33" />
    <hkern u1="&#x141;" u2="]" k="-8" />
    <hkern u1="&#x141;" u2="V" k="33" />
    <hkern u1="&#x141;" u2="&#x3f;" k="30" />
    <hkern u1="&#x141;" u2="&#x29;" k="-8" />
    <hkern u1="&#x142;" u2="&#x201d;" k="75" />
    <hkern u1="&#x142;" u2="&#x201c;" k="75" />
    <hkern u1="&#x142;" u2="&#x2018;" k="75" />
    <hkern u1="&#x142;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x142;" u2="v" k="33" />
    <hkern u1="&#x142;" u2="]" k="-8" />
    <hkern u1="&#x142;" u2="V" k="33" />
    <hkern u1="&#x142;" u2="&#x3f;" k="30" />
    <hkern u1="&#x142;" u2="&#x29;" k="-8" />
    <hkern u1="&#x14c;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14c;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14c;" u2="x" k="15" />
    <hkern u1="&#x14c;" u2="v" k="1" />
    <hkern u1="&#x14c;" u2="X" k="15" />
    <hkern u1="&#x14c;" u2="V" k="1" />
    <hkern u1="&#x14c;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14c;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14d;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14d;" u2="x" k="15" />
    <hkern u1="&#x14d;" u2="v" k="1" />
    <hkern u1="&#x14d;" u2="X" k="15" />
    <hkern u1="&#x14d;" u2="V" k="1" />
    <hkern u1="&#x14d;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14d;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14e;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14e;" u2="x" k="15" />
    <hkern u1="&#x14e;" u2="v" k="1" />
    <hkern u1="&#x14e;" u2="X" k="15" />
    <hkern u1="&#x14e;" u2="V" k="1" />
    <hkern u1="&#x14e;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14e;" u2="&#x2c;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2026;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201e;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201d;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201c;" k="10" />
    <hkern u1="&#x14f;" u2="&#x201a;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2018;" k="10" />
    <hkern u1="&#x14f;" u2="x" k="15" />
    <hkern u1="&#x14f;" u2="v" k="1" />
    <hkern u1="&#x14f;" u2="X" k="15" />
    <hkern u1="&#x14f;" u2="V" k="1" />
    <hkern u1="&#x14f;" u2="&#x2e;" k="10" />
    <hkern u1="&#x14f;" u2="&#x2c;" k="10" />
    <hkern u1="&#x150;" u2="&#x2026;" k="10" />
    <hkern u1="&#x150;" u2="&#x201e;" k="10" />
    <hkern u1="&#x150;" u2="&#x201d;" k="10" />
    <hkern u1="&#x150;" u2="&#x201c;" k="10" />
    <hkern u1="&#x150;" u2="&#x201a;" k="10" />
    <hkern u1="&#x150;" u2="&#x2018;" k="10" />
    <hkern u1="&#x150;" u2="x" k="15" />
    <hkern u1="&#x150;" u2="v" k="1" />
    <hkern u1="&#x150;" u2="X" k="15" />
    <hkern u1="&#x150;" u2="V" k="1" />
    <hkern u1="&#x150;" u2="&#x2e;" k="10" />
    <hkern u1="&#x150;" u2="&#x2c;" k="10" />
    <hkern u1="&#x151;" u2="&#x2026;" k="10" />
    <hkern u1="&#x151;" u2="&#x201e;" k="10" />
    <hkern u1="&#x151;" u2="&#x201d;" k="10" />
    <hkern u1="&#x151;" u2="&#x201c;" k="10" />
    <hkern u1="&#x151;" u2="&#x201a;" k="10" />
    <hkern u1="&#x151;" u2="&#x2018;" k="10" />
    <hkern u1="&#x151;" u2="x" k="15" />
    <hkern u1="&#x151;" u2="v" k="1" />
    <hkern u1="&#x151;" u2="X" k="15" />
    <hkern u1="&#x151;" u2="V" k="1" />
    <hkern u1="&#x151;" u2="&#x2e;" k="10" />
    <hkern u1="&#x151;" u2="&#x2c;" k="10" />
    <hkern u1="&#x154;" u2="v" k="2" />
    <hkern u1="&#x154;" u2="V" k="2" />
    <hkern u1="&#x155;" u2="v" k="2" />
    <hkern u1="&#x155;" u2="V" k="2" />
    <hkern u1="&#x156;" u2="v" k="2" />
    <hkern u1="&#x156;" u2="V" k="2" />
    <hkern u1="&#x157;" u2="v" k="2" />
    <hkern u1="&#x157;" u2="V" k="2" />
    <hkern u1="&#x158;" u2="v" k="2" />
    <hkern u1="&#x158;" u2="V" k="2" />
    <hkern u1="&#x159;" u2="v" k="2" />
    <hkern u1="&#x159;" u2="V" k="2" />
    <hkern u1="&#x15a;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15a;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15a;" u2="x" k="8" />
    <hkern u1="&#x15a;" u2="v" k="1" />
    <hkern u1="&#x15a;" u2="X" k="8" />
    <hkern u1="&#x15a;" u2="V" k="1" />
    <hkern u1="&#x15a;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15a;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15b;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15b;" u2="x" k="8" />
    <hkern u1="&#x15b;" u2="v" k="1" />
    <hkern u1="&#x15b;" u2="X" k="8" />
    <hkern u1="&#x15b;" u2="V" k="1" />
    <hkern u1="&#x15b;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15b;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15c;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15c;" u2="x" k="8" />
    <hkern u1="&#x15c;" u2="v" k="1" />
    <hkern u1="&#x15c;" u2="X" k="8" />
    <hkern u1="&#x15c;" u2="V" k="1" />
    <hkern u1="&#x15c;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15c;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15d;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15d;" u2="x" k="8" />
    <hkern u1="&#x15d;" u2="v" k="1" />
    <hkern u1="&#x15d;" u2="X" k="8" />
    <hkern u1="&#x15d;" u2="V" k="1" />
    <hkern u1="&#x15d;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15d;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15e;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15e;" u2="x" k="8" />
    <hkern u1="&#x15e;" u2="v" k="1" />
    <hkern u1="&#x15e;" u2="X" k="8" />
    <hkern u1="&#x15e;" u2="V" k="1" />
    <hkern u1="&#x15e;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15e;" u2="&#x2c;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2026;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201e;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201d;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201c;" k="5" />
    <hkern u1="&#x15f;" u2="&#x201a;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2018;" k="5" />
    <hkern u1="&#x15f;" u2="x" k="8" />
    <hkern u1="&#x15f;" u2="v" k="1" />
    <hkern u1="&#x15f;" u2="X" k="8" />
    <hkern u1="&#x15f;" u2="V" k="1" />
    <hkern u1="&#x15f;" u2="&#x2e;" k="5" />
    <hkern u1="&#x15f;" u2="&#x2c;" k="5" />
    <hkern u1="&#x160;" u2="&#x2026;" k="5" />
    <hkern u1="&#x160;" u2="&#x201e;" k="5" />
    <hkern u1="&#x160;" u2="&#x201d;" k="5" />
    <hkern u1="&#x160;" u2="&#x201c;" k="5" />
    <hkern u1="&#x160;" u2="&#x201a;" k="5" />
    <hkern u1="&#x160;" u2="&#x2018;" k="5" />
    <hkern u1="&#x160;" u2="x" k="8" />
    <hkern u1="&#x160;" u2="v" k="1" />
    <hkern u1="&#x160;" u2="X" k="8" />
    <hkern u1="&#x160;" u2="V" k="1" />
    <hkern u1="&#x160;" u2="&#x2e;" k="5" />
    <hkern u1="&#x160;" u2="&#x2c;" k="5" />
    <hkern u1="&#x161;" u2="&#x2026;" k="5" />
    <hkern u1="&#x161;" u2="&#x201e;" k="5" />
    <hkern u1="&#x161;" u2="&#x201d;" k="5" />
    <hkern u1="&#x161;" u2="&#x201c;" k="5" />
    <hkern u1="&#x161;" u2="&#x201a;" k="5" />
    <hkern u1="&#x161;" u2="&#x2018;" k="5" />
    <hkern u1="&#x161;" u2="x" k="8" />
    <hkern u1="&#x161;" u2="v" k="1" />
    <hkern u1="&#x161;" u2="X" k="8" />
    <hkern u1="&#x161;" u2="V" k="1" />
    <hkern u1="&#x161;" u2="&#x2e;" k="5" />
    <hkern u1="&#x161;" u2="&#x2c;" k="5" />
    <hkern u1="&#x162;" u2="&#x2026;" k="60" />
    <hkern u1="&#x162;" u2="&#x201e;" k="60" />
    <hkern u1="&#x162;" u2="&#x201a;" k="60" />
    <hkern u1="&#x162;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x162;" u2="]" k="-8" />
    <hkern u1="&#x162;" u2="&#x3b;" k="35" />
    <hkern u1="&#x162;" u2="&#x3a;" k="35" />
    <hkern u1="&#x162;" u2="&#x2e;" k="60" />
    <hkern u1="&#x162;" u2="&#x2c;" k="60" />
    <hkern u1="&#x162;" u2="&#x29;" k="-8" />
    <hkern u1="&#x163;" u2="&#x2026;" k="60" />
    <hkern u1="&#x163;" u2="&#x201e;" k="60" />
    <hkern u1="&#x163;" u2="&#x201a;" k="60" />
    <hkern u1="&#x163;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x163;" u2="]" k="-8" />
    <hkern u1="&#x163;" u2="&#x3b;" k="35" />
    <hkern u1="&#x163;" u2="&#x3a;" k="35" />
    <hkern u1="&#x163;" u2="&#x2e;" k="60" />
    <hkern u1="&#x163;" u2="&#x2c;" k="60" />
    <hkern u1="&#x163;" u2="&#x29;" k="-8" />
    <hkern u1="&#x164;" u2="&#x2026;" k="60" />
    <hkern u1="&#x164;" u2="&#x201e;" k="60" />
    <hkern u1="&#x164;" u2="&#x201a;" k="60" />
    <hkern u1="&#x164;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x164;" u2="]" k="-8" />
    <hkern u1="&#x164;" u2="&#x3b;" k="35" />
    <hkern u1="&#x164;" u2="&#x3a;" k="35" />
    <hkern u1="&#x164;" u2="&#x2e;" k="60" />
    <hkern u1="&#x164;" u2="&#x2c;" k="60" />
    <hkern u1="&#x164;" u2="&#x29;" k="-8" />
    <hkern u1="&#x165;" u2="&#x2026;" k="60" />
    <hkern u1="&#x165;" u2="&#x201e;" k="60" />
    <hkern u1="&#x165;" u2="&#x201a;" k="60" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x165;" u2="]" k="-8" />
    <hkern u1="&#x165;" u2="&#x3b;" k="35" />
    <hkern u1="&#x165;" u2="&#x3a;" k="35" />
    <hkern u1="&#x165;" u2="&#x2e;" k="60" />
    <hkern u1="&#x165;" u2="&#x2c;" k="60" />
    <hkern u1="&#x165;" u2="&#x29;" k="-8" />
    <hkern u1="&#x166;" u2="&#x2026;" k="60" />
    <hkern u1="&#x166;" u2="&#x201e;" k="60" />
    <hkern u1="&#x166;" u2="&#x201a;" k="60" />
    <hkern u1="&#x166;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x166;" u2="]" k="-8" />
    <hkern u1="&#x166;" u2="&#x3b;" k="35" />
    <hkern u1="&#x166;" u2="&#x3a;" k="35" />
    <hkern u1="&#x166;" u2="&#x2e;" k="60" />
    <hkern u1="&#x166;" u2="&#x2c;" k="60" />
    <hkern u1="&#x166;" u2="&#x29;" k="-8" />
    <hkern u1="&#x167;" u2="&#x2026;" k="60" />
    <hkern u1="&#x167;" u2="&#x201e;" k="60" />
    <hkern u1="&#x167;" u2="&#x201a;" k="60" />
    <hkern u1="&#x167;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x167;" u2="]" k="-8" />
    <hkern u1="&#x167;" u2="&#x3b;" k="35" />
    <hkern u1="&#x167;" u2="&#x3a;" k="35" />
    <hkern u1="&#x167;" u2="&#x2e;" k="60" />
    <hkern u1="&#x167;" u2="&#x2c;" k="60" />
    <hkern u1="&#x167;" u2="&#x29;" k="-8" />
    <hkern u1="&#x168;" u2="&#x2026;" k="6" />
    <hkern u1="&#x168;" u2="&#x201e;" k="6" />
    <hkern u1="&#x168;" u2="&#x201a;" k="6" />
    <hkern u1="&#x168;" u2="&#x2e;" k="6" />
    <hkern u1="&#x168;" u2="&#x2c;" k="6" />
    <hkern u1="&#x169;" u2="&#x2026;" k="6" />
    <hkern u1="&#x169;" u2="&#x201e;" k="6" />
    <hkern u1="&#x169;" u2="&#x201a;" k="6" />
    <hkern u1="&#x169;" u2="&#x2e;" k="6" />
    <hkern u1="&#x169;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16a;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16a;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16a;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16b;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16b;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16b;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16c;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16c;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16c;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16d;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16d;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16d;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16e;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16e;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16e;" u2="&#x2c;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2026;" k="6" />
    <hkern u1="&#x16f;" u2="&#x201e;" k="6" />
    <hkern u1="&#x16f;" u2="&#x201a;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2e;" k="6" />
    <hkern u1="&#x16f;" u2="&#x2c;" k="6" />
    <hkern u1="&#x170;" u2="&#x2026;" k="6" />
    <hkern u1="&#x170;" u2="&#x201e;" k="6" />
    <hkern u1="&#x170;" u2="&#x201a;" k="6" />
    <hkern u1="&#x170;" u2="&#x2e;" k="6" />
    <hkern u1="&#x170;" u2="&#x2c;" k="6" />
    <hkern u1="&#x171;" u2="&#x2026;" k="6" />
    <hkern u1="&#x171;" u2="&#x201e;" k="6" />
    <hkern u1="&#x171;" u2="&#x201a;" k="6" />
    <hkern u1="&#x171;" u2="&#x2e;" k="6" />
    <hkern u1="&#x171;" u2="&#x2c;" k="6" />
    <hkern u1="&#x172;" u2="&#x2026;" k="6" />
    <hkern u1="&#x172;" u2="&#x201e;" k="6" />
    <hkern u1="&#x172;" u2="&#x201a;" k="6" />
    <hkern u1="&#x172;" u2="&#x2e;" k="6" />
    <hkern u1="&#x172;" u2="&#x2c;" k="6" />
    <hkern u1="&#x173;" u2="&#x2026;" k="6" />
    <hkern u1="&#x173;" u2="&#x201e;" k="6" />
    <hkern u1="&#x173;" u2="&#x201a;" k="6" />
    <hkern u1="&#x173;" u2="&#x2e;" k="6" />
    <hkern u1="&#x173;" u2="&#x2c;" k="6" />
    <hkern u1="&#x174;" u2="&#x2026;" k="40" />
    <hkern u1="&#x174;" u2="&#x201e;" k="40" />
    <hkern u1="&#x174;" u2="&#x201a;" k="40" />
    <hkern u1="&#x174;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x174;" u2="]" k="-3" />
    <hkern u1="&#x174;" u2="&#x2e;" k="40" />
    <hkern u1="&#x174;" u2="&#x2c;" k="40" />
    <hkern u1="&#x174;" u2="&#x29;" k="-3" />
    <hkern u1="&#x175;" u2="&#x2026;" k="40" />
    <hkern u1="&#x175;" u2="&#x201e;" k="40" />
    <hkern u1="&#x175;" u2="&#x201a;" k="40" />
    <hkern u1="&#x175;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x175;" u2="]" k="-3" />
    <hkern u1="&#x175;" u2="&#x2e;" k="40" />
    <hkern u1="&#x175;" u2="&#x2c;" k="40" />
    <hkern u1="&#x175;" u2="&#x29;" k="-3" />
    <hkern u1="&#x176;" u2="&#x2026;" k="80" />
    <hkern u1="&#x176;" u2="&#x201e;" k="80" />
    <hkern u1="&#x176;" u2="&#x201a;" k="80" />
    <hkern u1="&#x176;" u2="&#x153;" k="15" />
    <hkern u1="&#x176;" u2="&#x152;" k="15" />
    <hkern u1="&#x176;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x176;" u2="q" k="15" />
    <hkern u1="&#x176;" u2="]" k="-5" />
    <hkern u1="&#x176;" u2="Q" k="15" />
    <hkern u1="&#x176;" u2="&#x3b;" k="20" />
    <hkern u1="&#x176;" u2="&#x3a;" k="20" />
    <hkern u1="&#x176;" u2="&#x2e;" k="80" />
    <hkern u1="&#x176;" u2="&#x2c;" k="80" />
    <hkern u1="&#x176;" u2="&#x29;" k="-5" />
    <hkern u1="&#x177;" u2="&#x2026;" k="80" />
    <hkern u1="&#x177;" u2="&#x201e;" k="80" />
    <hkern u1="&#x177;" u2="&#x201a;" k="80" />
    <hkern u1="&#x177;" u2="&#x153;" k="15" />
    <hkern u1="&#x177;" u2="&#x152;" k="15" />
    <hkern u1="&#x177;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x177;" u2="q" k="15" />
    <hkern u1="&#x177;" u2="]" k="-5" />
    <hkern u1="&#x177;" u2="Q" k="15" />
    <hkern u1="&#x177;" u2="&#x3b;" k="20" />
    <hkern u1="&#x177;" u2="&#x3a;" k="20" />
    <hkern u1="&#x177;" u2="&#x2e;" k="80" />
    <hkern u1="&#x177;" u2="&#x2c;" k="80" />
    <hkern u1="&#x177;" u2="&#x29;" k="-5" />
    <hkern u1="&#x178;" u2="&#x2026;" k="80" />
    <hkern u1="&#x178;" u2="&#x201e;" k="80" />
    <hkern u1="&#x178;" u2="&#x201a;" k="80" />
    <hkern u1="&#x178;" u2="&#x153;" k="15" />
    <hkern u1="&#x178;" u2="&#x152;" k="15" />
    <hkern u1="&#x178;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x178;" u2="q" k="15" />
    <hkern u1="&#x178;" u2="]" k="-5" />
    <hkern u1="&#x178;" u2="Q" k="15" />
    <hkern u1="&#x178;" u2="&#x3b;" k="20" />
    <hkern u1="&#x178;" u2="&#x3a;" k="20" />
    <hkern u1="&#x178;" u2="&#x2e;" k="80" />
    <hkern u1="&#x178;" u2="&#x2c;" k="80" />
    <hkern u1="&#x178;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1fe;" u2="x" k="15" />
    <hkern u1="&#x1fe;" u2="v" k="1" />
    <hkern u1="&#x1fe;" u2="X" k="15" />
    <hkern u1="&#x1fe;" u2="V" k="1" />
    <hkern u1="&#x1fe;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1fe;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1ff;" u2="x" k="15" />
    <hkern u1="&#x1ff;" u2="v" k="1" />
    <hkern u1="&#x1ff;" u2="X" k="15" />
    <hkern u1="&#x1ff;" u2="V" k="1" />
    <hkern u1="&#x1ff;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1ff;" u2="&#x2c;" k="10" />
    <hkern u1="&#x218;" u2="&#x2026;" k="5" />
    <hkern u1="&#x218;" u2="&#x201e;" k="5" />
    <hkern u1="&#x218;" u2="&#x201d;" k="5" />
    <hkern u1="&#x218;" u2="&#x201c;" k="5" />
    <hkern u1="&#x218;" u2="&#x201a;" k="5" />
    <hkern u1="&#x218;" u2="&#x2018;" k="5" />
    <hkern u1="&#x218;" u2="x" k="8" />
    <hkern u1="&#x218;" u2="v" k="1" />
    <hkern u1="&#x218;" u2="X" k="8" />
    <hkern u1="&#x218;" u2="V" k="1" />
    <hkern u1="&#x218;" u2="&#x2e;" k="5" />
    <hkern u1="&#x218;" u2="&#x2c;" k="5" />
    <hkern u1="&#x219;" u2="&#x2026;" k="5" />
    <hkern u1="&#x219;" u2="&#x201e;" k="5" />
    <hkern u1="&#x219;" u2="&#x201d;" k="5" />
    <hkern u1="&#x219;" u2="&#x201c;" k="5" />
    <hkern u1="&#x219;" u2="&#x201a;" k="5" />
    <hkern u1="&#x219;" u2="&#x2018;" k="5" />
    <hkern u1="&#x219;" u2="x" k="8" />
    <hkern u1="&#x219;" u2="v" k="1" />
    <hkern u1="&#x219;" u2="X" k="8" />
    <hkern u1="&#x219;" u2="V" k="1" />
    <hkern u1="&#x219;" u2="&#x2e;" k="5" />
    <hkern u1="&#x219;" u2="&#x2c;" k="5" />
    <hkern u1="&#x21a;" u2="&#x2026;" k="60" />
    <hkern u1="&#x21a;" u2="&#x201e;" k="60" />
    <hkern u1="&#x21a;" u2="&#x201a;" k="60" />
    <hkern u1="&#x21a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x21a;" u2="]" k="-8" />
    <hkern u1="&#x21a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x21a;" u2="&#x3a;" k="35" />
    <hkern u1="&#x21a;" u2="&#x2e;" k="60" />
    <hkern u1="&#x21a;" u2="&#x2c;" k="60" />
    <hkern u1="&#x21a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x21b;" u2="&#x2026;" k="60" />
    <hkern u1="&#x21b;" u2="&#x201e;" k="60" />
    <hkern u1="&#x21b;" u2="&#x201a;" k="60" />
    <hkern u1="&#x21b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x21b;" u2="]" k="-8" />
    <hkern u1="&#x21b;" u2="&#x3b;" k="35" />
    <hkern u1="&#x21b;" u2="&#x3a;" k="35" />
    <hkern u1="&#x21b;" u2="&#x2e;" k="60" />
    <hkern u1="&#x21b;" u2="&#x2c;" k="60" />
    <hkern u1="&#x21b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x237;" u2="&#x2026;" k="6" />
    <hkern u1="&#x237;" u2="&#x201e;" k="6" />
    <hkern u1="&#x237;" u2="&#x201a;" k="6" />
    <hkern u1="&#x237;" u2="&#x2e;" k="6" />
    <hkern u1="&#x237;" u2="&#x2c;" k="6" />
    <hkern u1="&#x2bc;" u2="&#x153;" k="10" />
    <hkern u1="&#x2bc;" u2="&#x152;" k="10" />
    <hkern u1="&#x2bc;" u2="x" k="2" />
    <hkern u1="&#x2bc;" u2="q" k="10" />
    <hkern u1="&#x2bc;" u2="X" k="2" />
    <hkern u1="&#x2bc;" u2="Q" k="10" />
    <hkern u1="&#x1e02;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e02;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e02;" u2="x" k="3" />
    <hkern u1="&#x1e02;" u2="v" k="4" />
    <hkern u1="&#x1e02;" u2="X" k="3" />
    <hkern u1="&#x1e02;" u2="V" k="4" />
    <hkern u1="&#x1e02;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e02;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e03;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e03;" u2="x" k="3" />
    <hkern u1="&#x1e03;" u2="v" k="4" />
    <hkern u1="&#x1e03;" u2="X" k="3" />
    <hkern u1="&#x1e03;" u2="V" k="4" />
    <hkern u1="&#x1e03;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e03;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e0a;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e0a;" u2="x" k="15" />
    <hkern u1="&#x1e0a;" u2="v" k="1" />
    <hkern u1="&#x1e0a;" u2="X" k="15" />
    <hkern u1="&#x1e0a;" u2="V" k="1" />
    <hkern u1="&#x1e0a;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1e0a;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2026;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201e;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201d;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201c;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x201a;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2018;" k="10" />
    <hkern u1="&#x1e0b;" u2="x" k="15" />
    <hkern u1="&#x1e0b;" u2="v" k="1" />
    <hkern u1="&#x1e0b;" u2="X" k="15" />
    <hkern u1="&#x1e0b;" u2="V" k="1" />
    <hkern u1="&#x1e0b;" u2="&#x2e;" k="10" />
    <hkern u1="&#x1e0b;" u2="&#x2c;" k="10" />
    <hkern u1="&#x1e1e;" u2="&#x2026;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x201e;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x201a;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1e1e;" u2="]" k="-5" />
    <hkern u1="&#x1e1e;" u2="&#x2e;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x2c;" k="70" />
    <hkern u1="&#x1e1e;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1e1f;" u2="&#x2026;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x201e;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x201a;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1e1f;" u2="]" k="-5" />
    <hkern u1="&#x1e1f;" u2="&#x2e;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x2c;" k="70" />
    <hkern u1="&#x1e1f;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1e56;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1e56;" u2="x" k="11" />
    <hkern u1="&#x1e56;" u2="X" k="11" />
    <hkern u1="&#x1e56;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1e56;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1e57;" u2="x" k="11" />
    <hkern u1="&#x1e57;" u2="X" k="11" />
    <hkern u1="&#x1e57;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1e57;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1e60;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201d;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201c;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2018;" k="5" />
    <hkern u1="&#x1e60;" u2="x" k="8" />
    <hkern u1="&#x1e60;" u2="v" k="1" />
    <hkern u1="&#x1e60;" u2="X" k="8" />
    <hkern u1="&#x1e60;" u2="V" k="1" />
    <hkern u1="&#x1e60;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e60;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2026;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201e;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201d;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201c;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x201a;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2018;" k="5" />
    <hkern u1="&#x1e61;" u2="x" k="8" />
    <hkern u1="&#x1e61;" u2="v" k="1" />
    <hkern u1="&#x1e61;" u2="X" k="8" />
    <hkern u1="&#x1e61;" u2="V" k="1" />
    <hkern u1="&#x1e61;" u2="&#x2e;" k="5" />
    <hkern u1="&#x1e61;" u2="&#x2c;" k="5" />
    <hkern u1="&#x1e6a;" u2="&#x2026;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x201e;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x201a;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x1e6a;" u2="]" k="-8" />
    <hkern u1="&#x1e6a;" u2="&#x3b;" k="35" />
    <hkern u1="&#x1e6a;" u2="&#x3a;" k="35" />
    <hkern u1="&#x1e6a;" u2="&#x2e;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x2c;" k="60" />
    <hkern u1="&#x1e6a;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e6b;" u2="&#x2026;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x201e;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x201a;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x7d;" k="-8" />
    <hkern u1="&#x1e6b;" u2="]" k="-8" />
    <hkern u1="&#x1e6b;" u2="&#x3b;" k="35" />
    <hkern u1="&#x1e6b;" u2="&#x3a;" k="35" />
    <hkern u1="&#x1e6b;" u2="&#x2e;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x2c;" k="60" />
    <hkern u1="&#x1e6b;" u2="&#x29;" k="-8" />
    <hkern u1="&#x1e80;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e80;" u2="]" k="-3" />
    <hkern u1="&#x1e80;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e80;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e81;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e81;" u2="]" k="-3" />
    <hkern u1="&#x1e81;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e81;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e82;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e82;" u2="]" k="-3" />
    <hkern u1="&#x1e82;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e82;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e83;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e83;" u2="]" k="-3" />
    <hkern u1="&#x1e83;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e83;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e84;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e84;" u2="]" k="-3" />
    <hkern u1="&#x1e84;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e84;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1e85;" u2="&#x2026;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x201e;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x201a;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x7d;" k="-3" />
    <hkern u1="&#x1e85;" u2="]" k="-3" />
    <hkern u1="&#x1e85;" u2="&#x2e;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x2c;" k="40" />
    <hkern u1="&#x1e85;" u2="&#x29;" k="-3" />
    <hkern u1="&#x1ef2;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x153;" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x152;" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1ef2;" u2="q" k="15" />
    <hkern u1="&#x1ef2;" u2="]" k="-5" />
    <hkern u1="&#x1ef2;" u2="Q" k="15" />
    <hkern u1="&#x1ef2;" u2="&#x3b;" k="20" />
    <hkern u1="&#x1ef2;" u2="&#x3a;" k="20" />
    <hkern u1="&#x1ef2;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1ef2;" u2="&#x29;" k="-5" />
    <hkern u1="&#x1ef3;" u2="&#x2026;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x201e;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x201a;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x153;" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x152;" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x7d;" k="-5" />
    <hkern u1="&#x1ef3;" u2="q" k="15" />
    <hkern u1="&#x1ef3;" u2="]" k="-5" />
    <hkern u1="&#x1ef3;" u2="Q" k="15" />
    <hkern u1="&#x1ef3;" u2="&#x3b;" k="20" />
    <hkern u1="&#x1ef3;" u2="&#x3a;" k="20" />
    <hkern u1="&#x1ef3;" u2="&#x2e;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x2c;" k="80" />
    <hkern u1="&#x1ef3;" u2="&#x29;" k="-5" />
    <hkern u1="&#x2018;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2018;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2018;" u2="&#x237;" k="40" />
    <hkern u1="&#x2018;" u2="&#x219;" k="5" />
    <hkern u1="&#x2018;" u2="&#x218;" k="5" />
    <hkern u1="&#x2018;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2018;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2018;" u2="&#x17e;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17d;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17c;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17b;" k="4" />
    <hkern u1="&#x2018;" u2="&#x17a;" k="4" />
    <hkern u1="&#x2018;" u2="&#x179;" k="4" />
    <hkern u1="&#x2018;" u2="&#x161;" k="5" />
    <hkern u1="&#x2018;" u2="&#x160;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2018;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2018;" u2="&#x153;" k="10" />
    <hkern u1="&#x2018;" u2="&#x152;" k="10" />
    <hkern u1="&#x2018;" u2="&#x151;" k="10" />
    <hkern u1="&#x2018;" u2="&#x150;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x135;" k="40" />
    <hkern u1="&#x2018;" u2="&#x134;" k="40" />
    <hkern u1="&#x2018;" u2="&#x133;" k="40" />
    <hkern u1="&#x2018;" u2="&#x132;" k="40" />
    <hkern u1="&#x2018;" u2="&#x123;" k="10" />
    <hkern u1="&#x2018;" u2="&#x122;" k="10" />
    <hkern u1="&#x2018;" u2="&#x121;" k="10" />
    <hkern u1="&#x2018;" u2="&#x120;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2018;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2018;" u2="&#x109;" k="10" />
    <hkern u1="&#x2018;" u2="&#x108;" k="10" />
    <hkern u1="&#x2018;" u2="&#x107;" k="10" />
    <hkern u1="&#x2018;" u2="&#x106;" k="10" />
    <hkern u1="&#x2018;" u2="&#x105;" k="60" />
    <hkern u1="&#x2018;" u2="&#x104;" k="60" />
    <hkern u1="&#x2018;" u2="&#x103;" k="60" />
    <hkern u1="&#x2018;" u2="&#x102;" k="60" />
    <hkern u1="&#x2018;" u2="&#x101;" k="60" />
    <hkern u1="&#x2018;" u2="&#x100;" k="60" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="60" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="60" />
    <hkern u1="&#x2018;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="60" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="60" />
    <hkern u1="&#x2018;" u2="z" k="4" />
    <hkern u1="&#x2018;" u2="x" k="2" />
    <hkern u1="&#x2018;" u2="s" k="5" />
    <hkern u1="&#x2018;" u2="q" k="10" />
    <hkern u1="&#x2018;" u2="o" k="10" />
    <hkern u1="&#x2018;" u2="j" k="40" />
    <hkern u1="&#x2018;" u2="g" k="10" />
    <hkern u1="&#x2018;" u2="c" k="10" />
    <hkern u1="&#x2018;" u2="a" k="60" />
    <hkern u1="&#x2018;" u2="Z" k="4" />
    <hkern u1="&#x2018;" u2="X" k="2" />
    <hkern u1="&#x2018;" u2="S" k="5" />
    <hkern u1="&#x2018;" u2="Q" k="10" />
    <hkern u1="&#x2018;" u2="O" k="10" />
    <hkern u1="&#x2018;" u2="J" k="40" />
    <hkern u1="&#x2018;" u2="G" k="10" />
    <hkern u1="&#x2018;" u2="C" k="10" />
    <hkern u1="&#x2018;" u2="A" k="60" />
    <hkern u1="&#x2019;" u2="&#x153;" k="10" />
    <hkern u1="&#x2019;" u2="&#x152;" k="10" />
    <hkern u1="&#x2019;" u2="x" k="2" />
    <hkern u1="&#x2019;" u2="q" k="10" />
    <hkern u1="&#x2019;" u2="X" k="2" />
    <hkern u1="&#x2019;" u2="Q" k="10" />
    <hkern u1="&#x201a;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x201a;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x201a;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x201a;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x201a;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x201a;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201a;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201a;" u2="&#x21b;" k="60" />
    <hkern u1="&#x201a;" u2="&#x21a;" k="60" />
    <hkern u1="&#x201a;" u2="&#x219;" k="5" />
    <hkern u1="&#x201a;" u2="&#x218;" k="5" />
    <hkern u1="&#x201a;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201a;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201a;" u2="&#x178;" k="80" />
    <hkern u1="&#x201a;" u2="&#x177;" k="80" />
    <hkern u1="&#x201a;" u2="&#x176;" k="80" />
    <hkern u1="&#x201a;" u2="&#x175;" k="40" />
    <hkern u1="&#x201a;" u2="&#x174;" k="40" />
    <hkern u1="&#x201a;" u2="&#x173;" k="6" />
    <hkern u1="&#x201a;" u2="&#x172;" k="6" />
    <hkern u1="&#x201a;" u2="&#x171;" k="6" />
    <hkern u1="&#x201a;" u2="&#x170;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16f;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16e;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16d;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16c;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16b;" k="6" />
    <hkern u1="&#x201a;" u2="&#x16a;" k="6" />
    <hkern u1="&#x201a;" u2="&#x169;" k="6" />
    <hkern u1="&#x201a;" u2="&#x168;" k="6" />
    <hkern u1="&#x201a;" u2="&#x167;" k="60" />
    <hkern u1="&#x201a;" u2="&#x166;" k="60" />
    <hkern u1="&#x201a;" u2="&#x165;" k="60" />
    <hkern u1="&#x201a;" u2="&#x164;" k="60" />
    <hkern u1="&#x201a;" u2="&#x163;" k="60" />
    <hkern u1="&#x201a;" u2="&#x162;" k="60" />
    <hkern u1="&#x201a;" u2="&#x161;" k="5" />
    <hkern u1="&#x201a;" u2="&#x160;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201a;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201a;" u2="&#x153;" k="10" />
    <hkern u1="&#x201a;" u2="&#x152;" k="10" />
    <hkern u1="&#x201a;" u2="&#x151;" k="10" />
    <hkern u1="&#x201a;" u2="&#x150;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x123;" k="10" />
    <hkern u1="&#x201a;" u2="&#x122;" k="10" />
    <hkern u1="&#x201a;" u2="&#x121;" k="10" />
    <hkern u1="&#x201a;" u2="&#x120;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201a;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201a;" u2="&#x109;" k="10" />
    <hkern u1="&#x201a;" u2="&#x108;" k="10" />
    <hkern u1="&#x201a;" u2="&#x107;" k="10" />
    <hkern u1="&#x201a;" u2="&#x106;" k="10" />
    <hkern u1="&#x201a;" u2="&#xff;" k="80" />
    <hkern u1="&#x201a;" u2="&#xfd;" k="80" />
    <hkern u1="&#x201a;" u2="&#xfc;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfb;" k="6" />
    <hkern u1="&#x201a;" u2="&#xfa;" k="6" />
    <hkern u1="&#x201a;" u2="&#xf9;" k="6" />
    <hkern u1="&#x201a;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201a;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201a;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201a;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="80" />
    <hkern u1="&#x201a;" u2="&#xdc;" k="6" />
    <hkern u1="&#x201a;" u2="&#xdb;" k="6" />
    <hkern u1="&#x201a;" u2="&#xda;" k="6" />
    <hkern u1="&#x201a;" u2="&#xd9;" k="6" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201a;" u2="y" k="80" />
    <hkern u1="&#x201a;" u2="w" k="40" />
    <hkern u1="&#x201a;" u2="v" k="60" />
    <hkern u1="&#x201a;" u2="u" k="6" />
    <hkern u1="&#x201a;" u2="t" k="60" />
    <hkern u1="&#x201a;" u2="s" k="5" />
    <hkern u1="&#x201a;" u2="q" k="10" />
    <hkern u1="&#x201a;" u2="o" k="10" />
    <hkern u1="&#x201a;" u2="g" k="10" />
    <hkern u1="&#x201a;" u2="c" k="10" />
    <hkern u1="&#x201a;" u2="Y" k="80" />
    <hkern u1="&#x201a;" u2="W" k="40" />
    <hkern u1="&#x201a;" u2="V" k="60" />
    <hkern u1="&#x201a;" u2="U" k="6" />
    <hkern u1="&#x201a;" u2="T" k="60" />
    <hkern u1="&#x201a;" u2="S" k="5" />
    <hkern u1="&#x201a;" u2="Q" k="10" />
    <hkern u1="&#x201a;" u2="O" k="10" />
    <hkern u1="&#x201a;" u2="G" k="10" />
    <hkern u1="&#x201a;" u2="C" k="10" />
    <hkern u1="&#x201c;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201c;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201c;" u2="&#x237;" k="40" />
    <hkern u1="&#x201c;" u2="&#x219;" k="5" />
    <hkern u1="&#x201c;" u2="&#x218;" k="5" />
    <hkern u1="&#x201c;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201c;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201c;" u2="&#x17e;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17d;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17c;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17b;" k="4" />
    <hkern u1="&#x201c;" u2="&#x17a;" k="4" />
    <hkern u1="&#x201c;" u2="&#x179;" k="4" />
    <hkern u1="&#x201c;" u2="&#x161;" k="5" />
    <hkern u1="&#x201c;" u2="&#x160;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201c;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201c;" u2="&#x153;" k="10" />
    <hkern u1="&#x201c;" u2="&#x152;" k="10" />
    <hkern u1="&#x201c;" u2="&#x151;" k="10" />
    <hkern u1="&#x201c;" u2="&#x150;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x135;" k="40" />
    <hkern u1="&#x201c;" u2="&#x134;" k="40" />
    <hkern u1="&#x201c;" u2="&#x133;" k="40" />
    <hkern u1="&#x201c;" u2="&#x132;" k="40" />
    <hkern u1="&#x201c;" u2="&#x123;" k="10" />
    <hkern u1="&#x201c;" u2="&#x122;" k="10" />
    <hkern u1="&#x201c;" u2="&#x121;" k="10" />
    <hkern u1="&#x201c;" u2="&#x120;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201c;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201c;" u2="&#x109;" k="10" />
    <hkern u1="&#x201c;" u2="&#x108;" k="10" />
    <hkern u1="&#x201c;" u2="&#x107;" k="10" />
    <hkern u1="&#x201c;" u2="&#x106;" k="10" />
    <hkern u1="&#x201c;" u2="&#x105;" k="60" />
    <hkern u1="&#x201c;" u2="&#x104;" k="60" />
    <hkern u1="&#x201c;" u2="&#x103;" k="60" />
    <hkern u1="&#x201c;" u2="&#x102;" k="60" />
    <hkern u1="&#x201c;" u2="&#x101;" k="60" />
    <hkern u1="&#x201c;" u2="&#x100;" k="60" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="60" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="60" />
    <hkern u1="&#x201c;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="60" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="60" />
    <hkern u1="&#x201c;" u2="z" k="4" />
    <hkern u1="&#x201c;" u2="x" k="2" />
    <hkern u1="&#x201c;" u2="s" k="5" />
    <hkern u1="&#x201c;" u2="q" k="10" />
    <hkern u1="&#x201c;" u2="o" k="10" />
    <hkern u1="&#x201c;" u2="j" k="40" />
    <hkern u1="&#x201c;" u2="g" k="10" />
    <hkern u1="&#x201c;" u2="c" k="10" />
    <hkern u1="&#x201c;" u2="a" k="60" />
    <hkern u1="&#x201c;" u2="Z" k="4" />
    <hkern u1="&#x201c;" u2="X" k="2" />
    <hkern u1="&#x201c;" u2="S" k="5" />
    <hkern u1="&#x201c;" u2="Q" k="10" />
    <hkern u1="&#x201c;" u2="O" k="10" />
    <hkern u1="&#x201c;" u2="J" k="40" />
    <hkern u1="&#x201c;" u2="G" k="10" />
    <hkern u1="&#x201c;" u2="C" k="10" />
    <hkern u1="&#x201c;" u2="A" k="60" />
    <hkern u1="&#x201d;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201d;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201d;" u2="&#x237;" k="40" />
    <hkern u1="&#x201d;" u2="&#x219;" k="5" />
    <hkern u1="&#x201d;" u2="&#x218;" k="5" />
    <hkern u1="&#x201d;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201d;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201d;" u2="&#x17e;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17d;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17c;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17b;" k="4" />
    <hkern u1="&#x201d;" u2="&#x17a;" k="4" />
    <hkern u1="&#x201d;" u2="&#x179;" k="4" />
    <hkern u1="&#x201d;" u2="&#x161;" k="5" />
    <hkern u1="&#x201d;" u2="&#x160;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201d;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201d;" u2="&#x153;" k="10" />
    <hkern u1="&#x201d;" u2="&#x152;" k="10" />
    <hkern u1="&#x201d;" u2="&#x151;" k="10" />
    <hkern u1="&#x201d;" u2="&#x150;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x135;" k="40" />
    <hkern u1="&#x201d;" u2="&#x134;" k="40" />
    <hkern u1="&#x201d;" u2="&#x133;" k="40" />
    <hkern u1="&#x201d;" u2="&#x132;" k="40" />
    <hkern u1="&#x201d;" u2="&#x123;" k="10" />
    <hkern u1="&#x201d;" u2="&#x122;" k="10" />
    <hkern u1="&#x201d;" u2="&#x121;" k="10" />
    <hkern u1="&#x201d;" u2="&#x120;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201d;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201d;" u2="&#x109;" k="10" />
    <hkern u1="&#x201d;" u2="&#x108;" k="10" />
    <hkern u1="&#x201d;" u2="&#x107;" k="10" />
    <hkern u1="&#x201d;" u2="&#x106;" k="10" />
    <hkern u1="&#x201d;" u2="&#x105;" k="60" />
    <hkern u1="&#x201d;" u2="&#x104;" k="60" />
    <hkern u1="&#x201d;" u2="&#x103;" k="60" />
    <hkern u1="&#x201d;" u2="&#x102;" k="60" />
    <hkern u1="&#x201d;" u2="&#x101;" k="60" />
    <hkern u1="&#x201d;" u2="&#x100;" k="60" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="60" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="60" />
    <hkern u1="&#x201d;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="60" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="60" />
    <hkern u1="&#x201d;" u2="z" k="4" />
    <hkern u1="&#x201d;" u2="x" k="2" />
    <hkern u1="&#x201d;" u2="s" k="5" />
    <hkern u1="&#x201d;" u2="q" k="10" />
    <hkern u1="&#x201d;" u2="o" k="10" />
    <hkern u1="&#x201d;" u2="j" k="40" />
    <hkern u1="&#x201d;" u2="g" k="10" />
    <hkern u1="&#x201d;" u2="c" k="10" />
    <hkern u1="&#x201d;" u2="a" k="60" />
    <hkern u1="&#x201d;" u2="Z" k="4" />
    <hkern u1="&#x201d;" u2="X" k="2" />
    <hkern u1="&#x201d;" u2="S" k="5" />
    <hkern u1="&#x201d;" u2="Q" k="10" />
    <hkern u1="&#x201d;" u2="O" k="10" />
    <hkern u1="&#x201d;" u2="J" k="40" />
    <hkern u1="&#x201d;" u2="G" k="10" />
    <hkern u1="&#x201d;" u2="C" k="10" />
    <hkern u1="&#x201d;" u2="A" k="60" />
    <hkern u1="&#x201e;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x201e;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x201e;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x201e;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x201e;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x201e;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x201e;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x201e;" u2="&#x21b;" k="60" />
    <hkern u1="&#x201e;" u2="&#x21a;" k="60" />
    <hkern u1="&#x201e;" u2="&#x219;" k="5" />
    <hkern u1="&#x201e;" u2="&#x218;" k="5" />
    <hkern u1="&#x201e;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x201e;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x201e;" u2="&#x178;" k="80" />
    <hkern u1="&#x201e;" u2="&#x177;" k="80" />
    <hkern u1="&#x201e;" u2="&#x176;" k="80" />
    <hkern u1="&#x201e;" u2="&#x175;" k="40" />
    <hkern u1="&#x201e;" u2="&#x174;" k="40" />
    <hkern u1="&#x201e;" u2="&#x173;" k="6" />
    <hkern u1="&#x201e;" u2="&#x172;" k="6" />
    <hkern u1="&#x201e;" u2="&#x171;" k="6" />
    <hkern u1="&#x201e;" u2="&#x170;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16f;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16e;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16d;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16c;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16b;" k="6" />
    <hkern u1="&#x201e;" u2="&#x16a;" k="6" />
    <hkern u1="&#x201e;" u2="&#x169;" k="6" />
    <hkern u1="&#x201e;" u2="&#x168;" k="6" />
    <hkern u1="&#x201e;" u2="&#x167;" k="60" />
    <hkern u1="&#x201e;" u2="&#x166;" k="60" />
    <hkern u1="&#x201e;" u2="&#x165;" k="60" />
    <hkern u1="&#x201e;" u2="&#x164;" k="60" />
    <hkern u1="&#x201e;" u2="&#x163;" k="60" />
    <hkern u1="&#x201e;" u2="&#x162;" k="60" />
    <hkern u1="&#x201e;" u2="&#x161;" k="5" />
    <hkern u1="&#x201e;" u2="&#x160;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15f;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15e;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15d;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15c;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15b;" k="5" />
    <hkern u1="&#x201e;" u2="&#x15a;" k="5" />
    <hkern u1="&#x201e;" u2="&#x153;" k="10" />
    <hkern u1="&#x201e;" u2="&#x152;" k="10" />
    <hkern u1="&#x201e;" u2="&#x151;" k="10" />
    <hkern u1="&#x201e;" u2="&#x150;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14f;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14e;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x14c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x123;" k="10" />
    <hkern u1="&#x201e;" u2="&#x122;" k="10" />
    <hkern u1="&#x201e;" u2="&#x121;" k="10" />
    <hkern u1="&#x201e;" u2="&#x120;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11f;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11e;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x11c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10d;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10c;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10b;" k="10" />
    <hkern u1="&#x201e;" u2="&#x10a;" k="10" />
    <hkern u1="&#x201e;" u2="&#x109;" k="10" />
    <hkern u1="&#x201e;" u2="&#x108;" k="10" />
    <hkern u1="&#x201e;" u2="&#x107;" k="10" />
    <hkern u1="&#x201e;" u2="&#x106;" k="10" />
    <hkern u1="&#x201e;" u2="&#xff;" k="80" />
    <hkern u1="&#x201e;" u2="&#xfd;" k="80" />
    <hkern u1="&#x201e;" u2="&#xfc;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfb;" k="6" />
    <hkern u1="&#x201e;" u2="&#xfa;" k="6" />
    <hkern u1="&#x201e;" u2="&#xf9;" k="6" />
    <hkern u1="&#x201e;" u2="&#xf8;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf6;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf5;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf4;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf3;" k="10" />
    <hkern u1="&#x201e;" u2="&#xf2;" k="10" />
    <hkern u1="&#x201e;" u2="&#xe7;" k="10" />
    <hkern u1="&#x201e;" u2="&#xdf;" k="5" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="80" />
    <hkern u1="&#x201e;" u2="&#xdc;" k="6" />
    <hkern u1="&#x201e;" u2="&#xdb;" k="6" />
    <hkern u1="&#x201e;" u2="&#xda;" k="6" />
    <hkern u1="&#x201e;" u2="&#xd9;" k="6" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="10" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="10" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="10" />
    <hkern u1="&#x201e;" u2="y" k="80" />
    <hkern u1="&#x201e;" u2="w" k="40" />
    <hkern u1="&#x201e;" u2="v" k="60" />
    <hkern u1="&#x201e;" u2="u" k="6" />
    <hkern u1="&#x201e;" u2="t" k="60" />
    <hkern u1="&#x201e;" u2="s" k="5" />
    <hkern u1="&#x201e;" u2="q" k="10" />
    <hkern u1="&#x201e;" u2="o" k="10" />
    <hkern u1="&#x201e;" u2="g" k="10" />
    <hkern u1="&#x201e;" u2="c" k="10" />
    <hkern u1="&#x201e;" u2="Y" k="80" />
    <hkern u1="&#x201e;" u2="W" k="40" />
    <hkern u1="&#x201e;" u2="V" k="60" />
    <hkern u1="&#x201e;" u2="U" k="6" />
    <hkern u1="&#x201e;" u2="T" k="60" />
    <hkern u1="&#x201e;" u2="S" k="5" />
    <hkern u1="&#x201e;" u2="Q" k="10" />
    <hkern u1="&#x201e;" u2="O" k="10" />
    <hkern u1="&#x201e;" u2="G" k="10" />
    <hkern u1="&#x201e;" u2="C" k="10" />
    <hkern u1="&#x2026;" u2="&#x1ef3;" k="80" />
    <hkern u1="&#x2026;" u2="&#x1ef2;" k="80" />
    <hkern u1="&#x2026;" u2="&#x1e85;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e84;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e83;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e82;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e81;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e80;" k="40" />
    <hkern u1="&#x2026;" u2="&#x1e6b;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1e6a;" k="60" />
    <hkern u1="&#x2026;" u2="&#x1e61;" k="5" />
    <hkern u1="&#x2026;" u2="&#x1e60;" k="5" />
    <hkern u1="&#x2026;" u2="&#x21b;" k="60" />
    <hkern u1="&#x2026;" u2="&#x21a;" k="60" />
    <hkern u1="&#x2026;" u2="&#x219;" k="5" />
    <hkern u1="&#x2026;" u2="&#x218;" k="5" />
    <hkern u1="&#x2026;" u2="&#x1ff;" k="10" />
    <hkern u1="&#x2026;" u2="&#x1fe;" k="10" />
    <hkern u1="&#x2026;" u2="&#x178;" k="80" />
    <hkern u1="&#x2026;" u2="&#x177;" k="80" />
    <hkern u1="&#x2026;" u2="&#x176;" k="80" />
    <hkern u1="&#x2026;" u2="&#x175;" k="40" />
    <hkern u1="&#x2026;" u2="&#x174;" k="40" />
    <hkern u1="&#x2026;" u2="&#x173;" k="6" />
    <hkern u1="&#x2026;" u2="&#x172;" k="6" />
    <hkern u1="&#x2026;" u2="&#x171;" k="6" />
    <hkern u1="&#x2026;" u2="&#x170;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16f;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16e;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16d;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16c;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16b;" k="6" />
    <hkern u1="&#x2026;" u2="&#x16a;" k="6" />
    <hkern u1="&#x2026;" u2="&#x169;" k="6" />
    <hkern u1="&#x2026;" u2="&#x168;" k="6" />
    <hkern u1="&#x2026;" u2="&#x167;" k="60" />
    <hkern u1="&#x2026;" u2="&#x166;" k="60" />
    <hkern u1="&#x2026;" u2="&#x165;" k="60" />
    <hkern u1="&#x2026;" u2="&#x164;" k="60" />
    <hkern u1="&#x2026;" u2="&#x163;" k="60" />
    <hkern u1="&#x2026;" u2="&#x162;" k="60" />
    <hkern u1="&#x2026;" u2="&#x161;" k="5" />
    <hkern u1="&#x2026;" u2="&#x160;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15f;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15e;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15d;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15c;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15b;" k="5" />
    <hkern u1="&#x2026;" u2="&#x15a;" k="5" />
    <hkern u1="&#x2026;" u2="&#x153;" k="10" />
    <hkern u1="&#x2026;" u2="&#x152;" k="10" />
    <hkern u1="&#x2026;" u2="&#x151;" k="10" />
    <hkern u1="&#x2026;" u2="&#x150;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14f;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14e;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x14c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x123;" k="10" />
    <hkern u1="&#x2026;" u2="&#x122;" k="10" />
    <hkern u1="&#x2026;" u2="&#x121;" k="10" />
    <hkern u1="&#x2026;" u2="&#x120;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11f;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11e;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x11c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10d;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10c;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10b;" k="10" />
    <hkern u1="&#x2026;" u2="&#x10a;" k="10" />
    <hkern u1="&#x2026;" u2="&#x109;" k="10" />
    <hkern u1="&#x2026;" u2="&#x108;" k="10" />
    <hkern u1="&#x2026;" u2="&#x107;" k="10" />
    <hkern u1="&#x2026;" u2="&#x106;" k="10" />
    <hkern u1="&#x2026;" u2="&#xff;" k="80" />
    <hkern u1="&#x2026;" u2="&#xfd;" k="80" />
    <hkern u1="&#x2026;" u2="&#xfc;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfb;" k="6" />
    <hkern u1="&#x2026;" u2="&#xfa;" k="6" />
    <hkern u1="&#x2026;" u2="&#xf9;" k="6" />
    <hkern u1="&#x2026;" u2="&#xf8;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf5;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf4;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf3;" k="10" />
    <hkern u1="&#x2026;" u2="&#xf2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xe7;" k="10" />
    <hkern u1="&#x2026;" u2="&#xdf;" k="5" />
    <hkern u1="&#x2026;" u2="&#xdd;" k="80" />
    <hkern u1="&#x2026;" u2="&#xdc;" k="6" />
    <hkern u1="&#x2026;" u2="&#xdb;" k="6" />
    <hkern u1="&#x2026;" u2="&#xda;" k="6" />
    <hkern u1="&#x2026;" u2="&#xd9;" k="6" />
    <hkern u1="&#x2026;" u2="&#xd8;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd6;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd5;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd4;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd3;" k="10" />
    <hkern u1="&#x2026;" u2="&#xd2;" k="10" />
    <hkern u1="&#x2026;" u2="&#xc7;" k="10" />
    <hkern u1="&#x2026;" u2="y" k="80" />
    <hkern u1="&#x2026;" u2="w" k="40" />
    <hkern u1="&#x2026;" u2="v" k="60" />
    <hkern u1="&#x2026;" u2="u" k="6" />
    <hkern u1="&#x2026;" u2="t" k="60" />
    <hkern u1="&#x2026;" u2="s" k="5" />
    <hkern u1="&#x2026;" u2="q" k="10" />
    <hkern u1="&#x2026;" u2="o" k="10" />
    <hkern u1="&#x2026;" u2="g" k="10" />
    <hkern u1="&#x2026;" u2="c" k="10" />
    <hkern u1="&#x2026;" u2="Y" k="80" />
    <hkern u1="&#x2026;" u2="W" k="40" />
    <hkern u1="&#x2026;" u2="V" k="60" />
    <hkern u1="&#x2026;" u2="U" k="6" />
    <hkern u1="&#x2026;" u2="T" k="60" />
    <hkern u1="&#x2026;" u2="S" k="5" />
    <hkern u1="&#x2026;" u2="Q" k="10" />
    <hkern u1="&#x2026;" u2="O" k="10" />
    <hkern u1="&#x2026;" u2="G" k="10" />
    <hkern u1="&#x2026;" u2="C" k="10" />
    <hkern g1="seven.alt" g2="seven.alt" k="-4" />
    <hkern g1="seven.alt" g2="four.alt" k="40" />
    <hkern g1="B,uni1E02"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="1" />
    <hkern g1="B,uni1E02"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="B,uni1E02"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="1" />
    <hkern g1="B,uni1E02"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="11" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="D,Eth,Dcaron,Dcroat,uni1E0A"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="F,uni1E1E"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="13" />
    <hkern g1="F,uni1E1E"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="F,uni1E1E"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="F,uni1E1E"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="J,Jcircumflex"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="J,Jcircumflex"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="K,uni0136"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="7" />
    <hkern g1="K,uni0136"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="7" />
    <hkern g1="K,uni0136"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="7" />
    <hkern g1="K,uni0136"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="5" />
    <hkern g1="K,uni0136"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="K,uni0136"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="7" />
    <hkern g1="K,uni0136"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="7" />
    <hkern g1="K,uni0136"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="7" />
    <hkern g1="K,uni0136"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="5" />
    <hkern g1="K,uni0136"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="4" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="17" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="62" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	k="50" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="hyphen,uni00AD"
	k="40" />
    <hkern g1="L,Lacute,uni013B,Ldot,Lslash"
	g2="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	k="50" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="P,uni1E56"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="P,uni1E56"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="13" />
    <hkern g1="P,uni1E56"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="R,Racute,uni0156,Rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="38" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="ae,aeacute"
	k="50" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	g2="hyphen,uni00AD"
	k="32" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="15" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="ae,aeacute"
	k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,uni00AD"
	k="4" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="AE,AEacute"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="ae,aeacute"
	k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="15" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	g2="hyphen,uni00AD"
	k="18" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="hyphen,uni00AD"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	k="38" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="49" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="hyphen,uni00AD"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="1" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	k="38" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="15" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="49" />
    <hkern g1="b,uni1E03"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="1" />
    <hkern g1="b,uni1E03"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="b,uni1E03"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="1" />
    <hkern g1="b,uni1E03"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="11" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="11" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="d,eth,dcaron,dcroat,uni1E0B"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="f,uni1E1F"
	g2="AE,AEacute"
	k="40" />
    <hkern g1="f,uni1E1F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="13" />
    <hkern g1="f,uni1E1F"
	g2="ae,aeacute"
	k="40" />
    <hkern g1="f,uni1E1F"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="f,uni1E1F"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,uni0123"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="j,jcircumflex,uni0237"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="j,jcircumflex,uni0237"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="7" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="5" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
	k="4" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="5" />
    <hkern g1="k,uni0137,kgreenlandic"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	k="4" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	k="50" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="17" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="62" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="hyphen,uni00AD"
	k="40" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	k="50" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="17" />
    <hkern g1="l,lacute,uni013C,ldot,lslash"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="62" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="15" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="15" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="1" />
    <hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="2" />
    <hkern g1="p,uni1E57"
	g2="AE,AEacute"
	k="30" />
    <hkern g1="p,uni1E57"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="13" />
    <hkern g1="p,uni1E57"
	g2="ae,aeacute"
	k="30" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="10" />
    <hkern g1="r,racute,uni0157,rcaron"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="10" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="7" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="7" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="AE,AEacute"
	k="2" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	g2="ae,aeacute"
	k="2" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="hyphen,uni00AD"
	k="32" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="AE,AEacute"
	k="50" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="38" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	g2="ae,aeacute"
	k="50" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="AE,AEacute"
	k="4" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
	g2="ae,aeacute"
	k="4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="hyphen,uni00AD"
	k="4" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="AE,AEacute"
	k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="15" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="ae,aeacute"
	k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="G,Gcircumflex,Gbreve,Gdotaccent,uni0122"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="g,gcircumflex,gbreve,gdotaccent,uni0123"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="hyphen,uni00AD"
	k="18" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute"
	k="15" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="AE,AEacute"
	k="63" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="49" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="ae,aeacute"
	k="63" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,uni0218,uni1E60"
	k="7" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
	g2="s,germandbls,sacute,scircumflex,scedilla,scaron,uni0219,uni1E61"
	k="7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="hyphen,uni00AD"
	k="15" />
    <hkern g1="hyphen,uni00AD"
	g2="T,uni0162,Tcaron,Tbar,uni021A,uni1E6A"
	k="32" />
    <hkern g1="hyphen,uni00AD"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="4" />
    <hkern g1="hyphen,uni00AD"
	g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
	k="18" />
    <hkern g1="hyphen,uni00AD"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="10" />
    <hkern g1="hyphen,uni00AD"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek"
	k="10" />
    <hkern g1="hyphen,uni00AD"
	g2="t,uni0163,tcaron,tbar,uni021B,uni1E6B"
	k="32" />
    <hkern g1="hyphen,uni00AD"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="4" />
    <hkern g1="hyphen,uni00AD"
	g2="y,yacute,ydieresis,ycircumflex,ygrave"
	k="18" />
    <hkern g1="hyphen,uni00AD"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
  </font>
</defs></svg>
