ig.module('game.entities.buttons.button-nav')
.requires(
    'plugins.utils.buttons.button-image'
)
.defines(function () {
    "use strict";

    ig.EntityButtonNext = ig.global.EntityButtonNext = EntityButtonImage.extend({
        name: 'button-next',
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-right.png'), frameCountX: 1, frameCountY: 1 },

        init: function (x, y, settings) {
            this.parent(x, y, settings);
        },

        onClickCallback: function () {
            if (this._parent && !this._parent.isTransitioning) {
                this._parent.onNext();
            }
        },

        clicked: function () {
            if (this._parent && !this._parent.isTransitioning) {
                this.parent();
            }
        }
    });

    ig.EntityButtonPrev = ig.global.EntityButtonPrev = EntityButtonImage.extend({
        name: 'button-prev',
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-left.png'), frameCountX: 1, frameCountY: 1 },

        init: function (x, y, settings) {
            this.parent(x, y, settings);
        },

        onClickCallback: function () {
            if (this._parent && !this._parent.isTransitioning) {
                this._parent.onPrev();
            }
        },

        clicked: function () {
            if (this._parent && !this._parent.isTransitioning) {
                this.parent();
            }
        }
    });
});
