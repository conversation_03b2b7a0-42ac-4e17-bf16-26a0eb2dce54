ig.module('game.entities.buttons.button-pause')
.requires(
    'plugins.utils.buttons.button-base',
    'game.entities.objects.popup-settings'
)
.defines(function () {
    "use strict";

    ig.EntityButtonPause = ig.global.EntityButtonPause = EntityButtonImage.extend({
        name: 'button-pause',
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-pause.png'), frameCountX: 1, frameCountY: 1 },

        init: function (x, y, settings) {
            this.parent(x, y, settings);
        },

        onClickCallback: function () {
            // spawn settings popup
            ig.currentCtrl.pauseGame();
            ig.game.spawnEntity(EntityPopupSettingsGame, 0, 0, { zIndex: ig.game.LAYERS.POPUP });
        }
    });
});
