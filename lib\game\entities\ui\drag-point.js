ig.module('game.entities.ui.drag-point')
.requires(
    'impact.entity'
)
.defines(function () {

    EntityDragPoint = ig.Entity.extend({
        
        collides: ig.Entity.COLLIDES.NEVER,
        type: ig.Entity.TYPE.NONE,
        
        zIndex: 99999999999999999,
        size: new Vector2(100, 100),
        clickOnce: false,
        enterOnce: false,
        isClicked: false,

        //Enable edit position x, y by dragging the object on the screen
        editPos: new Vector2(false, false),

        anchor: {
            fromObj: null,
            midX: null,
            midY: null,
            offsetX: 0,
            offsetY: 0,
            corner: "top-left", //top-left, top-right, bottom-left, bottom-right 
            objects: []
        },
  
        init: function (x, y, settings) {
            this.ctx = ig.system.context;
            this.pos = new Vector2(x, y);

            // Pre-render similar primitives or repeating objects on an offscreen canvas
            // https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API/Tutorial/Optimizing_canvas#pre-render_similar_primitives_or_repeating_objects_on_an_offscreen_canvas
            if (window.OffscreenCanvas == undefined){
                //if OffscreenCanvas Method is not supported, create an improvise
                this.offCanvas = document.createElement("canvas");
                this.offCanvas.width = this.size.x;
                this.offCanvas.height = this.size.y;

                this.offCanvasCTX = this.offCanvas.getContext("2d"); 
            }
            else {
                this.offCanvas = new OffscreenCanvas(this.size.x, this.size.y);
                this.offCanvasCTX = this.offCanvas.getContext("2d"); 
            }

            if (settings){

                if (settings.anchor){
                    for (var i = 0; i < Object.entries(settings.anchor).length; i++){
                        var key = Object.entries(settings.anchor)[i][0];
                        this.anchor[key] = settings.anchor[key];
                    }
                }

                this.editPos = settings.editPos || this.editPos;
            }

            this.pointerEvent = (this.editPos.x || this.editPos.y);

            this.pivotPoint = new Vector2(-this.size.x / 2, -this.size.y / 2);
            this.drawEllipse({ fillStyle: "cyan" }, this.size);    
            this._size = this.size;
            this.scale = 0.5;
            this.alpha = 0.75;
            this.isHide = false;
            this.setPosition();
            ig.game.sortEntitiesDeferred();
        },

        draw: function (){
            if ((this.editPos.x || this.editPos.y)){
                this.ctx.save();
                this.ctx.globalAlpha = this.alpha;
                this.drawTransform(this.pos.x, this.pos.y);
                this.drawCanvasElement(this.offCanvas);
                this.ctx.restore(); 
            }
        },

        update: function (){ if (this.pointerEvent) this.checkPointer(); },
        repos: function (){ this.setPosition(); },

        checkPointer: function (){
            if ( ig.game.io.mouse.getPos == undefined || this.isHide == true || 
                this.alpha == 0 || this.pointerEvent == false) return;

            var pointer = ig.game.io.mouse.getPos();
            var distance = Math.sqrt(Math.pow(pointer.x - this.pos.x, 2) + Math.pow(pointer.y - this.pos.y, 2));
            var longerSide = this.size.x > this.size.y ? this.size.x : this.size.y;
            
            this.isPressed = ig.input.state('click');
            var checkClick = this.isPressed && this.clickOnce;

            if (checkClick){ if (this.onClicking) this.onClicking(this); }

            if (this.isPressed == false && this.clickOnce == true){
                this.clickOnce = false;
                if (this.onReleasedOutside) this.onReleasedOutside(this);
            }

            //return if pointer is far on this object 
            if (distance > (longerSide * 2)) return;

            var collideX = pointer.x > (this.pos.x + this.pivotPoint.x) && 
                           pointer.x < (this.pos.x + this.pivotPoint.x) + this.size.x;

            var collideY = pointer.y > (this.pos.y + this.pivotPoint.y) && 
                           pointer.y < (this.pos.y + this.pivotPoint.y) + this.size.y;


            if ( (collideX && collideY) && this.isPressed && this.clickOnce == false){
                this.clickOnce = true;
            }
        },

        onReleased: function (){ 
            this.reDraw({ fillStyle: "cyan" });
            if (this.anchor.fromObj && this.anchor.fromObj.onReleased != undefined) this.anchor.fromObj.onReleased();
            this.isClicked = false;
        },
        onReleasedOutside: function (){
            this.reDraw({ fillStyle: "cyan" });
            if (this.anchor.fromObj && this.anchor.fromObj.onReleasedOutside != undefined) this.anchor.fromObj.onReleasedOutside();
            this.isClicked = false;
        },
        onClicked: function (){
            this.reDraw({ fillStyle: "red" });
            if (this.anchor.fromObj && this.anchor.fromObj.onClicked != undefined) {
                this.anchor.fromObj.onClicked();
            }
        },

        onClicking: function (){ 
            if (!this.isClicked) {
                if (this.anchor.fromObj && this.anchor.fromObj.onClicked != undefined) {
                    this.anchor.fromObj.onClicked();
                }
                this.isClicked = true;
            }
            this.onDraggingPosition(); 
            this.reDraw({ fillStyle: "red" }); 
        },

        reDraw: function (settings){
            this.clearDraw();
            this.drawEllipse(settings, this.size); 
            ig.game.sortEntitiesDeferred();
        },

        clearDraw: function (){ this.offCanvasCTX.clearRect(0, 0, this.size.x, this.size.y); },

        drawCanvasElement: function (element){
            this.ctx.save();
            //drawImage(image, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight)
            if (element) this.ctx.drawImage(element, 0, 0, this._size.x, this._size.y, this.pivotPoint.x, this.pivotPoint.y, this.size.x, this.size.y );
            this.ctx.restore();
        },

        drawTransform: function (posX, posY){

            // Sub-pixel rendering occurs when you render objects on a canvas without whole values.
            var x = Math.floor(posX);
            var y = Math.floor(posY);
            this.ctx.translate(x, y);
            this.ctx.scale(this.scale, this.scale);
        },

        drawEllipse: function (settings, size){
            var set = {
                fillStyle: null,
                x: settings.radius || (size.x / 2),
                y: settings.radius || (size.y / 2),
                radX: settings.radius || (size.x / 2),
                radY: settings.radius || (size.y / 2),
                rotation: 0,
                startAngle: 0,
                endAngle: 360,
                antiClockwise: false
            };

            for (var i = 0; i < Object.entries(settings).length; i++){
                var key = Object.entries(settings)[i][0];
                set[key] = settings[key];
            }

            this.offCanvasCTX.save();
            this.offCanvasCTX.beginPath();

            if (set.fillStyle != null){ this.offCanvasCTX.fillStyle = set.fillStyle; }

            //ellipse(x, y, radiusX, radiusY, rotation, startAngle, endAngle, anticlockwise)
            this.offCanvasCTX.ellipse(set.x, set.y, set.radX, set.radY, 
                set.rotation, set.startAngle * (Math.PI / 180), set.endAngle * (Math.PI / 180), 
                set.antiClockwise);

            if (set.fillStyle != null) this.offCanvasCTX.fill();

            this.offCanvasCTX.closePath();
            this.offCanvasCTX.restore();
        },

        setPosition: function (){

            for (var i = 0; i < this.anchor.objects.length; i++) {
                this.anchor.objects[i].pos = this.pos;
            }

            if (this.anchor.fromObj != null){
                var objSize = this.anchor.fromObj.size;
                var objPos = this.anchor.fromObj.pos; 

                if (this.anchor.midX != null) 
                     this.pos.x = objPos.x + (objSize.x * this.anchor.midX);
                else this.pos.x = objPos.x + this.anchor.offsetX;

                if (this.anchor.midY != null) 
                     this.pos.y = objPos.y + (objSize.y * this.anchor.midY);
                else this.pos.y = objPos.y + this.anchor.offsetY;
                
                return;
            }

            var midXPos = null;
            var midYPos = null;

            if (this.anchor.midX != null) midXPos = ig.system.width * this.anchor.midX;
            if (this.anchor.midY != null) midYPos = ig.system.height * this.anchor.midY;

            //top-left, top-right, bottom-left, bottom-right
            switch (this.anchor.corner){
                case "top-left": 
                    this.pos.x = midXPos || this.anchor.offsetX;
                    this.pos.y = midYPos || this.anchor.offsetY;
                break;

                case "top-right": 
                    this.pos.x = midXPos || ig.system.width - this.anchor.offsetX;
                    this.pos.y = midYPos || this.anchor.offsetY;
                break;

                case "bottom-right": 
                    this.pos.x = midXPos || ig.system.width - this.anchor.offsetX;
                    this.pos.y = midYPos || ig.system.height - this.anchor.offsetY;
                break;

                case "bottom-left": 
                    this.pos.x = midXPos || this.anchor.offsetX;
                    this.pos.y = midYPos || ig.system.height - this.anchor.offsetY;
                break;
            }            
        },

        onDraggingPosition: function (){

            if (this.editPos.x == true) this.pos.x = ig.game.io.mouse.getPos().x;
            if (this.editPos.y == true) this.pos.y = ig.game.io.mouse.getPos().y;

            for (var i = 0; i < this.anchor.objects.length; i++) {
                this.anchor.objects[i].pos = this.pos;
            }

            // Add point to truck's trajectory if it's the current truck
            if (this.anchor.fromObj && ig.currentCtrl.currentVehicle === this.anchor.fromObj) {
                // Ensure isDragging flag is set during dragging
                this.anchor.fromObj.isDragging = true;
                this.anchor.fromObj.addCoordinate(this.pos, true);
            }

            //top-left, top-right, bottom-left, bottom-right
            switch (this.anchor.corner){
                case "top-left": 
                    if (this.editPos.x == true){ this.anchor.offsetX = this.pos.x; }
                    if (this.editPos.y == true){ this.anchor.offsetY = this.pos.y; }
                break;

                case "top-right": 
                    if (this.editPos.x == true){ this.anchor.offsetX = ig.system.width - this.pos.x; }
                    if (this.editPos.y == true){ this.anchor.offsetY = this.pos.y; }
                break;

                case "bottom-right": 
                    if (this.editPos.x == true){ this.anchor.offsetX = ig.system.width - this.pos.x; }
                    if (this.editPos.y == true){ this.anchor.offsetY = ig.system.height - this.pos.y; }
                break;

                case "bottom-left": 
                    if (this.editPos.x == true){ this.anchor.offsetX = this.pos.x; }
                    if (this.editPos.y == true){ this.anchor.offsetY = ig.system.height - this.pos.y; }
                break;
            }

            if (this.anchor.midX != null) this.anchor.midX = this.pos.x / ig.system.width;
            if (this.anchor.midY != null) this.anchor.midY = this.pos.y / ig.system.height;

            if (this.anchor.fromObj != null){
                var objSize = this.anchor.fromObj.size;
                var objPos = this.anchor.fromObj.pos; 

                if (this.editPos.x == true){ 
                    if (this.anchor.midX != null) this.anchor.midX = (this.pos.x - objPos.x) / objSize.x;
                    else this.anchor.offsetX = (objPos.x - this.pos.x) * -1; 
                }

                if (this.editPos.y == true){
                    if (this.anchor.midY != null) this.anchor.midY = (this.pos.y - objPos.y) / objSize.y;
                    else this.anchor.offsetY = (this.anchor.fromObj.pos.y - this.pos.y) * -1;  
                }
            }

            // if (this.editPos.x == true){
            //     if (this.anchor.midX == null){ console.log("offsetX: " + this.anchor.offsetX); }
            //     else { console.log("MidX: " + this.anchor.midX); }
            // }

            // if (this.editPos.y == true){
            //     if (this.anchor.midY == null){ console.log("offsetY: " + this.anchor.offsetY); }
            //     else { console.log("MidY: " + this.anchor.midY); }
            // }
        }

    });

});