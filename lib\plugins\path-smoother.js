ig.module('plugins.path-smoother')
.defines(function () {
    PathSmoother = ig.Class.extend({
        /**
         * Smoothes a path using <PERSON><PERSON><PERSON>'s algorithm.
         * @param {Array<{x: number, y: number}>} points - The array of points to smooth.
         * @param {number} [iterations=1] - The number of times to apply the algorithm.
         * @param {number} [ratio=0.25] - The ratio for cutting corners.
         * @returns {Array<{x: number, y: number}>} The smoothed path.
         */
        chaikin: function (points, iterations, ratio) {
            if (iterations === undefined) iterations = 1;
            if (ratio === undefined) ratio = 0.25;

            if (points.length < 3) return points;

            var smoothed = points;
            for (var i = 0; i < iterations; i++) {
                smoothed = this._chaikinIteration(smoothed, ratio);
            }
            return smoothed;
        },

        _chaikinIteration: function (points, ratio) {
            var newPoints = [];
            if (points.length > 0) {
                newPoints.push(points[0]);
            }

            for (var i = 0; i < points.length - 1; i++) {
                var p0 = points[i];
                var p1 = points[i + 1];

                var q0 = {
                    x: (1 - ratio) * p0.x + ratio * p1.x,
                    y: (1 - ratio) * p0.y + ratio * p1.y
                };
                var q1 = {
                    x: ratio * p0.x + (1 - ratio) * p1.x,
                    y: ratio * p0.y + (1 - ratio) * p1.y
                };

                newPoints.push(q0);
                newPoints.push(q1);
            }

            if (points.length > 1) {
                newPoints.push(points[points.length - 1]);
            }
            
            return newPoints;
        }
    });
});