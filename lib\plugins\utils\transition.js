ig.module('plugins.utils.transition')
.requires(
    'impact.entity',
    'impact.timer'
)
.defines(function () {
    EntityTransition = ig.Entity.extend({
        _wmIgnore: true, // Ignore in Weltmeister editor
        zIndex: 9999, // Always on top
        fadeAlpha: 1,
        isTransitioning: false,
        color: null,
        
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.color = settings.color || 'white';
            ig.game.sortEntitiesDeferred();
        },

        fadeIn: function (duration, callback, easing) {
            if (this.isTransitioning) return;
            
            this.isTransitioning = true;
            var tweenAlpha = { fadeAlpha: 0 };
            
            new ig.TweenDef(tweenAlpha)
                .to({ fadeAlpha: 1 }, duration || 500)
                .easing(easing || ig.Tween.Easing.Quadratic.EaseIn)
                .onUpdate(function () {
                    this.fadeAlpha = tweenAlpha.fadeAlpha;
                }.bind(this))
                .onComplete(function () {
                    this.isTransitioning = false;
                    if (callback) callback();
                }.bind(this))
                .start();
        },

        fadeOut: function (duration, callback, easing, delay) {
            if (this.isTransitioning) return;
            
            this.isTransitioning = true;
            var tweenAlpha = { fadeAlpha: 1 };
            
            new ig.TweenDef(tweenAlpha)
                .to({ fadeAlpha: 0 }, duration || 700)
                .easing(easing || ig.Tween.Easing.Quadratic.EaseOut)
                .delay(delay || 0)
                .onUpdate(function () {
                    this.fadeAlpha = tweenAlpha.fadeAlpha;
                }.bind(this))
                .onComplete(function () {
                    this.isTransitioning = false;
                    if (callback) callback();
                }.bind(this))
                .start();
        },

        // Flash effect
        flash: function (duration, callback) {
            this.fadeIn(duration / 2, function () {
                this.fadeOut(duration / 2, callback);
            }.bind(this));
        },

        draw: function () {
            if (this.fadeAlpha > 0) {
                var ctx = ig.system.context;
                ctx.save();
                
                // Support for different colors
                if (this.color === 'white') {
                    ctx.fillStyle = 'rgba(255, 255, 255, ' + this.fadeAlpha + ')';
                } else if (this.color === 'black') {
                    ctx.fillStyle = 'rgba(0, 0, 0, ' + this.fadeAlpha + ')';
                } else {
                    // Support for custom colors
                    ctx.fillStyle = this.color;
                    ctx.globalAlpha = this.fadeAlpha;
                }
                
                ctx.fillRect(0, 0, ig.system.width, ig.system.height);
                ctx.restore();
            }
        }
    });
});
