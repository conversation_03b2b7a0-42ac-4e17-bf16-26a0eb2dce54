/**
 * Number Animator Plugin for ImpactJS
 * IMPORTANT: This plugin mutates the target object's properties directly.
 * 
 * Usage:
 * 1. Require the plugin in your game file:
 *    .requires('plugins.number-animator')
 * 
 * 2. Animate a number:
 *    ig.NumberAnimator.animate({
 *        targetContext: myObject,
 *        property: 'score',
 *        value: 1000,
 *        duration: 2000,
 *        decimals: 0,
 *        easing: ig.Tween.Easing.Quadratic.EaseOut, // Uses the plugin.tween's easing presets
 *        onUpdate: function(value) {
 *            console.log('Current value:', value);
 *        },
 *        onComplete: function(value) {
 *            console.log('Animation complete! Final value:', value);
 *        }
 *    });
 * 
 * 3. The plugin automatically updates animations in the game loop.
 */
ig.module('plugins.utils.number-animator')
.requires('impact.game', 'impact.timer', 'plugins.tween')
.defines(function () {
    "use strict";

    ig.NumberAnimator = ig.Class.extend({
        animations: [],
        
        init: function () {
            ig.Game.inject({
                update: function () {
                    this.parent();
                    ig.NumberAnimator.update();
                }
            });
        },

        animate: function (options) {
            if (!options.targetContext || !options.property || options.value === undefined) {
                console.error('Invalid animation options');
                return;
            }

            var animation = {
                target: options.targetContext,
                property: options.property,
                startValue: options.targetContext[options.property],
                endValue: options.value,
                duration: options.duration || 1000,
                decimals: options.decimals !== undefined ? options.decimals : 0,
                easing: options.easing || ig.Tween.Easing.Quadratic.EaseOut,
                onUpdate: options.onUpdate,
                onComplete: options.onComplete,
                startTime: ig.Timer.time,
                isComplete: false
            };

            this.animations.push(animation);
        },

        update: function () {
            var currentTime = ig.Timer.time;
            
            for (var i = this.animations.length - 1; i >= 0; i--) {
                var anim = this.animations[i];
                var elapsed = (currentTime - anim.startTime) * 1000;

                if (elapsed < anim.duration) {
                    var progress = anim.easing(elapsed / anim.duration);
                    var currentValue = anim.startValue + (anim.endValue - anim.startValue) * progress;
                    anim.target[anim.property] = this.formatNumber(currentValue, anim.decimals);
                    
                    if (typeof anim.onUpdate === 'function') {
                        anim.onUpdate(anim.target[anim.property]);
                    }
                } else if (!anim.isComplete) {
                    anim.target[anim.property] = this.formatNumber(anim.endValue, anim.decimals);
                    anim.isComplete = true;
                    
                    if (typeof anim.onComplete === 'function') {
                        anim.onComplete(anim.target[anim.property]);
                    }
                }

                if (anim.isComplete) {
                    this.animations.splice(i, 1);
                }
            }
        },

        formatNumber: function (num, decimals) {
            return Number(num.toFixed(decimals));
        }
    });

    // Create a single instance of NumberAnimator
    ig.NumberAnimator = new ig.NumberAnimator();
});
