ig.module('plugins.utils.notification')
.requires('impact.entity')
.defines(function () {

    EntityNotification = ig.Entity.extend({
        // Constants
        lineHeight: 55,
        maxLines: 3,

        // Properties
        zIndex: 1000,
        alpha: 0,
        text: '',
        fontFace: 'Arial',
        fontSize: 50,
        fontColor: '#fff',
        delayTimer: null,
        delayedFunction: null,
        notifWidth: 800,
        notifHeight: 50,
        notifPadding: 20,
        toBeRemoved: false,
        backgroundColor: '#2F463B',

        textAnchor: 'center',
        textVAlign: 'middle',
        textAlign: 'center',
        textBaseline: 'top',
        
        scale: { x: 0, y: 1 },

        notifProperties: {},
        staticInstantiate: function () {
            if (typeof EntityNotification.notificationArr === 'undefined') {
                EntityNotification.notificationArr = [];
            }
            return undefined;
        },

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.anchorCenter();
            this.handleExistingNotifications();
            this.tweenIn();

            // Precompute wrappedLines during initialization
            var ctx = ig.system.context;
            ctx.font = this.fontSize + 'px ' + this.fontFace;
            this.wrappedLines = this.wrapTextAndGetLines(ctx, this.text, this.notifWidth - 2 * this.notifPadding);

            // Use precomputed wrappedLines
            this.notifProperties.lines = this.wrappedLines;
                    
            // Adjust the width and height of the notification based on the text
            this.notifProperties.longestLineWidth = Math.max.apply(null, this.notifProperties.lines.map(function (line) {
                return ctx.measureText(line).width;
            }));

            this.notifProperties.adjustedWidth = Math.max(this.notifWidth, this.notifProperties.longestLineWidth + 2 * this.notifPadding);
            this.notifProperties.adjustedHeight = Math.min(this.notifProperties.lines.length, this.maxLines) * this.lineHeight + 2 * this.notifPadding;
 
            ig.game.sortEntitiesDeferred();
        },

        anchorCenter: function () {
            this.pos.x -= this.notifWidth * 0.5;
            this.pos.y -= this.notifHeight * 0.5;
        },

        handleExistingNotifications: function () {
            for (var i = 0; i < EntityNotification.notificationArr.length; i++) {
                EntityNotification.notificationArr[i].tweenMoveUp();
            }
            EntityNotification.notificationArr.push(this);
        },

        tweenIn: function () {
            this.tween({ alpha: 1, scale: { x: 1, y: 1 } }, 0.25, {
                easing: ig.Tween.Easing.Quadratic.EaseInOut,
                onComplete: function () {
                    this.delayTimer = new ig.Timer(1);
                    this.delayedFunction = this.tweenFadeOut;
                }.bind(this)
            }).start();
        },

        tweenFadeOut: function () {
            this.tween({ alpha: 0 }, 0.25, {
                onComplete: function () {
                    this.toBeRemoved = true;
                }.bind(this)
            }).start();
        },

        tweenMoveUp: function () {
            this.tween({ pos: { y: this.pos.y - this.notifProperties.adjustedHeight - this.notifPadding } }, 0.1).start();
        },

        handleDelayTimer: function () {
            if (this.delayTimer && this.delayTimer.delta() > 0) {
                this.delayTimer = null;
                this.delayedFunction();
                this.delayedFunction = null;
            }
        },

        getStartX: function (longestLineWidth, adjustedWidth) {
            switch (this.textAnchor) {
                case 'center':
                    return this.getCenteredStartX(longestLineWidth, adjustedWidth);
                case 'right':
                    return this.getRightAlignedStartX(longestLineWidth, adjustedWidth);
                default: // 'left'
                    return this.getLeftAlignedStartX(longestLineWidth, adjustedWidth);
            }
        },

        getCenteredStartX: function (longestLineWidth, adjustedWidth) {
            if (this.textAlign === 'center') {
                return this.pos.x + adjustedWidth / 2;
            } else if (this.textAlign === 'left') {
                return this.pos.x + adjustedWidth / 2;
            } else { // 'right'
                return this.pos.x + adjustedWidth / 2 - longestLineWidth;
            }
        },

        getRightAlignedStartX: function (longestLineWidth, adjustedWidth) {
            if (this.textAlign === 'center') {
                return this.pos.x + adjustedWidth - longestLineWidth / 2;
            } else if (this.textAlign === 'left') {
                return this.pos.x + adjustedWidth - longestLineWidth;
            } else { // 'right'
                return this.pos.x + adjustedWidth;
            }
        },

        getLeftAlignedStartX: function (longestLineWidth, adjustedWidth) {
            if (this.textAlign === 'center') {
                return this.pos.x + longestLineWidth / 2 + this.notifPadding;
            } else if (this.textAlign === 'left') {
                return this.pos.x + this.notifPadding;
            } else { // 'right'
                return this.pos.x;
            }
        },

        getStartY: function (totalTextHeight, adjustedHeight, lineCount) {
            switch (this.textVAlign) {
                case 'middle':
                    return this.pos.y + adjustedHeight / 2 - lineCount * this.lineHeight / 2;
                case 'bottom':
                    return this.pos.y + adjustedHeight - totalTextHeight - this.notifPadding;
                default: // 'top'
                    return this.pos.y + this.notifPadding;
            }
        },
        
        wrapTextAndGetLines: function (ctx, text, maxWidth) {
            var words = text.split(' ');
            var line = '';
            var lines = [];
            for (var n = 0; n < words.length; n++) {
                while (ctx.measureText(words[n]).width > maxWidth) {
                    var tmp = words[n];
                    words[n] = tmp.substring(0, tmp.length - 1);
                    if (n < words.length - 1) {
                        words[n + 1] = tmp[tmp.length - 1] + words[n + 1];
                    } else {
                        words.push(tmp[tmp.length - 1]);
                    }
                }
                        
                var testLine = line + words[n] + ' ';
                if (ctx.measureText(testLine).width > maxWidth && n > 0) {
                    lines.push(line);
                    line = words[n] + ' ';
                } else {
                    line = testLine;
                }
            }
            lines.push(line);
    
            if (lines.length > this.maxLines) {
                lines = lines.slice(0, this.maxLines);
                var lastLine = lines[this.maxLines - 1].trim();
                if (lastLine.length > 3) {
                    lastLine = lastLine.substring(0, lastLine.length - 3);
                }
                lines[this.maxLines - 1] = lastLine + "...";
            }       
        
            return lines;
        },

        draw: function () {
            this.parent();
            var ctx = ig.system.context;
            ctx.save();
            ctx.globalAlpha = this.alpha;
            ctx.translate(this.pos.x + this.notifProperties.adjustedWidth * 0.5, this.pos.y + this.notifProperties.adjustedHeight * 0.5);
            ctx.scale(this.scale.x, this.scale.y);
            ctx.translate(-this.pos.x - this.notifProperties.adjustedWidth * 0.5, -this.pos.y - this.notifProperties.adjustedHeight * 0.5);

            // Set the font, textAlign, and textBaseline
            ctx.font = this.fontSize + 'px ' + this.fontFace;
            ctx.textAlign = this.textAlign;
            ctx.textBaseline = this.textBaseline;
            ctx.fillStyle = this.fontColor;

            ctx.save();
            ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
            ctx.shadowBlur = 6;
            ctx.shadowOffsetX = 6;
            ctx.shadowOffsetY = 6;
        
            ctx.fillStyle = this.backgroundColor;
            ctx.roundRect(this.pos.x, this.pos.y, this.notifProperties.adjustedWidth, this.notifProperties.adjustedHeight);
            ctx.restore();

            ctx.fillStyle = this.fontColor;

            var startX = this.getStartX(this.notifProperties.longestLineWidth, this.notifProperties.adjustedWidth);
            var startY = this.getStartY(this.notifProperties.lines.length * this.lineHeight, this.notifProperties.adjustedHeight, Math.min(this.notifProperties.lines.length, this.maxLines));

            // Directly draw the precomputed wrappedLines
            for (var i = 0; i < this.notifProperties.lines.length; i++) {
                ctx.fillText(this.notifProperties.lines[i], startX, startY + i * this.lineHeight);
            }
        
            ctx.restore();
        },

        update: function () {
            if (this.toBeRemoved) {
                var index = EntityNotification.notificationArr.indexOf(this);
                if (index > -1) {
                    EntityNotification.notificationArr.splice(index, 1);
                }
                this.kill();
                return;
            }
            this.parent();
            this.handleDelayTimer();
        }
    });

    CanvasRenderingContext2D.prototype.roundRect = function (x, y, width, height, radius, stroke) {
        if (typeof stroke == "undefined" ) {
            stroke = false;
        }
        if (typeof radius === "undefined") {
            radius = 5;
        }
        this.save();
        this.beginPath();
        this.moveTo(x + radius, y);
        this.lineTo(x + width - radius, y);
        this.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.lineTo(x + width, y + height - radius);
        this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.lineTo(x + radius, y + height);
        this.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.lineTo(x, y + radius);
        this.quadraticCurveTo(x, y, x + radius, y);
        this.closePath();
        if (stroke) {
            this.lineWidth = 4;
            this.stroke();
        }
        this.fill();
        this.restore();
    };
});
