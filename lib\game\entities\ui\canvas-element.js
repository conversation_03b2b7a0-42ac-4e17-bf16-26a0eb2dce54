//New updates on 03-18-24
//Disable adjust font height on mac/apple devices

//New updates on 03-11-24
//Adds an ability to adjust height based on the text content while the wrapword is enabled.
//Revamps wrapword method
//Adds an ability to adjust font height
//set text rendering to optimizeSpeed

//New updates on 03-06-24
//Adds an ability to wrapwords, if set to enable, isResizeText set to true will not work

//New updates on 04-24-25
//Optimized property assignment
//Improved text rendering performance
//Optimized canvas operations
//Added caching for calculations

ig.module('game.entities.ui.canvas-element')
.requires(
    'impact.entity'
)
.defines(function () {

    EntityCanvasElement = ig.Entity.extend({
        
        name: "EntityCanvasElement",
        size: new Vector2(100, 100),
        posOffset: new Vector2(0, 0),
        
        customFillStyle: null,
        customStrokeStyle: null,
        imageBitmap: null,
        alpha: 1,
        rotate: 0,
        scale: 1,
        clipOnce: false,
        isHide: false,

        element: {
            type: null, //ellipse, arc, box, text, image
            settings: {}
        },

        //top-left,    top-center,    top-right
        //center-left, center-center, center-right
        //bot-left,    bot-center,    bot-right
        pivot: 'center-center', 
        pivotPoint: new Vector2(0, 0),
        
        // Cache for expensive calculations
        _cachedTextProperties: null,
        _lastTextSettings: null,

        // events
        onInit: null,
        onReDraw: null,
        onClip: null,
        onUpdate: null,
        onDraw: null,
        onTextResize: null,

        init: function (x, y, settings) {
            this.initSettings(settings);
            if (this.onInit) this.onInit(this, settings);

            this.pos = new Vector2(x, y);
            this.ctx = ig.system.context;

            this.createOffscreenCanvas();
            this.drawElementOnce(this.element);
            this.setPivot();
            this._size = new Vector2(this.size.x, this.size.y);

            if (this.onPostInit) this.onPostInit(this, settings);
            ig.game.sortEntitiesDeferred();
        },
        
        // Create offscreen canvas with proper fallback
        createOffscreenCanvas: function () {
            // Pre-render similar primitives or repeating objects on an offscreen canvas
            // https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API/Tutorial/Optimizing_canvas#pre-render_similar_primitives_or_repeating_objects_on_an_offscreen_canvas
            if (window.OffscreenCanvas == undefined){
                //if OffscreenCanvas Method is not supported, create an improvise
                this.offCanvas = document.createElement("canvas");
                this.offCanvas.width = this.size.x;
                this.offCanvas.height = this.size.y;
            }
            else { 
                this.offCanvas = new OffscreenCanvas(this.size.x, this.size.y); 
            }

            this.offCanvasCTX = this.offCanvas.getContext("2d");
        },

        update: function (){
            this.parent();
            if (this.isHide == true) return;
            if (this.onUpdate) this.onUpdate(this);
        },

        draw: function () {
            if (this.isHide == true || this.alpha == 0) return;

            this.ctx.save();
            this.ctx.globalAlpha = this.alpha;

            this.ctx.translate(this.posOffset.x, this.posOffset.y);
            this.drawTransform(this.pos.x, this.pos.y);
            this.drawCanvasElement(this.imageBitmap);

            this.ctx.restore();        

            if (this.onDraw != null) {
                this.ctx.save();
                this.onDraw(this);
                this.ctx.restore();
            }

            if (ig.game.debug){
                this.ctx.save();
                this.ctx.strokeStyle = "yellow";  
                this.ctx.lineWidth = 5; 
                this.drawTransform(this.pos.x, this.pos.y);
                this.ctx.strokeRect(this.pivotPoint.x, this.pivotPoint.y, this.size.x, this.size.y);
                this.ctx.restore(); 
            } 
        },

        initSettings: function (settings){
            if (!settings) return;
            
            // Copy element settings using Object.assign for better performance
            if (settings.element) {
                Object.assign(this.element, settings.element);
            }

            if (this.element.settings && this.element.settings.image) {
                this.size = new Vector2(
                    this.element.settings.image.width, 
                    this.element.settings.image.height
                );
            } 

            // Use a more efficient approach to assign properties
            var propertiesToCopy = [
                'size', 'scale', 'alpha', 'rotate', 'zIndex', 'onReDraw', 
                'onClip', 'onUpdate', 'isHide', 'onDraw', 'pivot', 
                'posOffset', 'onInit', 'onPostInit', 'onTextResize'
            ];
            
            for (var i = 0; i < propertiesToCopy.length; i++) {
                var prop = propertiesToCopy[i];
                if (settings[prop] !== undefined) {
                    this[prop] = settings[prop];
                }
            }
        },

        // This function is no longer needed as we're using a more direct approach
        // hasValue: function (setValue, currentVal){ return setValue != undefined ? setValue : currentVal; },

        setPivot: function (newPivot) {
            this.pivot = newPivot || this.pivot;
            
            // Use a lookup table for pivot points instead of switch statement
            var pivotMap = {
                'top-left': [0, 0],
                'top-center': [-this.size.x / 2, 0],
                'top-right': [-this.size.x, 0],
                'center-left': [0, -this.size.y / 2],
                'center-center': [-this.size.x / 2, -this.size.y / 2],
                'center-right': [-this.size.x, -this.size.y / 2],
                'bot-left': [0, -this.size.y],
                'bot-center': [-this.size.x / 2, -this.size.y],
                'bot-right': [-this.size.x, -this.size.y]
            };
            
            var coords = pivotMap[this.pivot] || pivotMap['center-center'];
            this.pivotPoint = new Vector2(coords[0], coords[1]);
        },

        reDraw: function (settings, isDrawCanvasGroup){
            isDrawCanvasGroup = isDrawCanvasGroup != undefined ? isDrawCanvasGroup : false;

            if (this.offCanvasCTX.reset != undefined) this.offCanvasCTX.reset();

            this.clearDraw();

            // Use Object.assign for better performance
            Object.assign(this.element.settings, settings);

            // Reset text properties cache if text settings changed
            if (this.element.type === "text") {
                this._cachedTextProperties = null;
                this._lastTextSettings = null;
            }

            this.size = settings.size || this.size;
            
            this.offCanvas.width = this.size.x;
            this.offCanvas.height = this.size.y;

            this.drawElementOnce(this.element);
            
            this._size = this.size;
            this.setPivot();
            
            if (this.onReDraw) this.onReDraw(this);
            ig.game.sortEntitiesDeferred();

            if (this._canvasGroup && isDrawCanvasGroup) this._canvasGroup.reDrawElements();
        },

        drawCanvasElement: function (element){
            if (!element) return;
            
            // No need for save/restore if we're just drawing an image
            //drawImage(image, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight)
            this.ctx.drawImage(
                element, 
                0, 0, 
                this._size.x, this._size.y, 
                this.pivotPoint.x, this.pivotPoint.y, 
                this.size.x, this.size.y
            );
        },

        drawTransform: function (posX, posY){
            // Sub-pixel rendering occurs when you render objects on a canvas without whole values.
            var x = Math.floor(posX);
            var y = Math.floor(posY);
            this.ctx.translate(x, y);
            this.ctx.scale(this.scale, this.scale);
            this.ctx.rotate(this.rotate * (Math.PI / 180));
        },

        drawElementOnce: function (element){
            this.clipOnce = false;

            this.offCanvasCTX.save();
            this.offCanvasCTX.globalAlpha = element.settings.alpha || 1;

            // Set default values directly
            var scaleX = element.settings.scaleX || 1;
            var scaleY = element.settings.scaleY || 1;
            var rotate = element.settings.rotate || 0;
            
            this.offCanvasCTX.scale(scaleX, scaleY);
            this.offCanvasCTX.rotate(rotate * (Math.PI / 180));

            // Use function reference map for better performance than switch
            var drawFunctions = {
                "image": this.drawImage,
                "text": this.drawText,
                "box": this.drawBox,
                "arc": this.drawArc,
                "ellipse": this.drawEllipse
            };
            
            var drawFunction = drawFunctions[element.type];
            if (drawFunction) {
                drawFunction.call(this, element.settings, this.size);
            }

            this.offCanvasCTX.restore();

            this.cleanupAndTransferBitmap();
        },
        
        cleanupAndTransferBitmap: function () {
            // Clean up previous bitmap if it exists
            if (this.imageBitmap && this.imageBitmap.close != undefined) {
                this.imageBitmap.close();
            }

            // Create new bitmap using the most efficient available method
            if (this.offCanvas.transferToImageBitmap != undefined) {
                this.imageBitmap = this.offCanvas.transferToImageBitmap();
            }
            else if (this.offCanvas.createImageBitmap != undefined) {
                this.imageBitmap = this.offCanvas.createImageBitmap();
            }
            else {
                this.imageBitmap = this.offCanvas;
            }
        },

        clearDraw: function (){ 
            this.offCanvasCTX.clearRect(0, 0, this.size.x, this.size.y); 
        },

        drawImage: function (settings){
            if (settings.image == null) return;

            // Create default settings object
            var set = { 
                image: null, 
                filter: null,
                sx: 0, sy: 0,
                dx: 0, dy: 0,
                sWidth: settings.image.width,
                sHeight: settings.image.height,
                dWidth: settings.image.width,
                dHeight: settings.image.height
            };

            // Use Object.assign for better performance
            Object.assign(set, settings);

            // Only save context if we need to apply a filter
            var needsRestore = set.filter != null;
            if (needsRestore) {
                this.offCanvasCTX.save();
                this.offCanvasCTX.filter = set.filter;
            }
            
            var img = set.image;
            
            //drawImage(image, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight)
            this.offCanvasCTX.drawImage(
                img.data, 
                set.sx, set.sy, 
                set.sWidth, set.sHeight, 
                set.dx, set.dy, 
                set.dWidth, set.dHeight
            );
            
            if (needsRestore) {
                this.offCanvasCTX.restore();
            }
        },

        drawBox: function (settings, size, isNotRectPath){
            isNotRectPath = isNotRectPath != undefined ? true : false;

            var lineWidth = settings.lineWidth || 0;
            
            // Create default settings object with pre-calculated values
            var set = {
                fillStyle: null,
                strokeStyle: null,
                x: lineWidth / 2,
                y: lineWidth / 2,
                width: size.x - lineWidth,
                height: size.y - lineWidth,
                borderRadius: settings.borderRadius,
                radii: [
                    settings.borderRadius || 0, 
                    settings.borderRadius || 0, 
                    settings.borderRadius || 0, 
                    settings.borderRadius || 0
                ],
                lineWidth: 0,
                lineJoin: "miter" // "miter", "bevel", "round"
            };

            // Use Object.assign for better performance
            Object.assign(set, settings);

            this.offCanvasCTX.save();
            this.offCanvasCTX.beginPath();

            this.setFillStrokeStyle(set, this.offCanvasCTX);

            if (isNotRectPath == true){
                if (set.fillStyle != null) {
                    this.offCanvasCTX.fillRect(set.x, set.y, set.width, set.height);
                }
                if (set.strokeStyle != null) {
                    this.offCanvasCTX.strokeRect(set.x, set.y, set.width, set.height);
                }
            }
            else {
                if (this.offCanvasCTX.roundRect == undefined){
                    this.offCanvasCTX.rect(set.x, set.y, set.width, set.height); 
                }
                else {
                    //roundRect(x, y, width, height, radii)
                    this.offCanvasCTX.roundRect(set.x, set.y, set.width, set.height, set.radii);
                }
                
                if (set.fillStyle != null) {
                    this.offCanvasCTX.fill();
                }
                if (set.strokeStyle != null) {
                    this.offCanvasCTX.stroke();
                }
            }
            
            this.offCanvasCTX.closePath();

            if (this.onClip && this.clipOnce == false){ 
                this.clipOnce = true; 
                this.offCanvasCTX.clip(); 
                this.onClip(this); 
            }

            this.offCanvasCTX.restore();
        },

        drawEllipse: function (settings, size){
            var lineWidth = settings.lineWidth || 10;
            var radius = settings.radius || (size.x / 2);
            var halfLineWidth = lineWidth / 2;
            
            // Create default settings object with pre-calculated values
            var set = {
                fillStyle: null,
                strokeStyle: null,
                x: radius,
                y: radius || (size.y / 2),
                radX: radius - halfLineWidth || (size.x / 2) - halfLineWidth,
                radY: radius - halfLineWidth || (size.y / 2) - halfLineWidth,
                rotation: 0,
                startAngle: 0,
                endAngle: 360,
                antiClockwise: false,
                lineWidth: lineWidth
            };

            // Use Object.assign for better performance
            Object.assign(set, settings);

            this.offCanvasCTX.save();
            this.offCanvasCTX.beginPath();

            this.setFillStrokeStyle(set, this.offCanvasCTX);

            // Convert angles to radians once
            var startRad = set.startAngle * (Math.PI / 180);
            var endRad = set.endAngle * (Math.PI / 180);
            
            //ellipse(x, y, radiusX, radiusY, rotation, startAngle, endAngle, anticlockwise)
            this.offCanvasCTX.ellipse(
                set.x, set.y, 
                set.radX, set.radY, 
                set.rotation, 
                startRad, endRad, 
                set.antiClockwise
            );

            if (set.fillStyle != null) {
                this.offCanvasCTX.fill();
            }
            if (set.strokeStyle != null) {
                this.offCanvasCTX.stroke();
            }

            if (this.onClip != null && this.clipOnce == false){ 
                this.clipOnce = true; 
                this.offCanvasCTX.clip(); 
                this.onClip(this); 
            }

            this.offCanvasCTX.closePath();
            this.offCanvasCTX.restore();
        },

        drawArc: function (settings, size){
            var lineWidth = settings.lineWidth || 10;
            var halfLineWidth = lineWidth / 2;
            var defaultRadius = (size.x / 2);
            
            // Create default settings object with pre-calculated values
            var set = {
                fillStyle: null,
                strokeStyle: null,
                x: settings.radius || defaultRadius,
                y: settings.radius || defaultRadius,
                radius: defaultRadius - halfLineWidth,
                startAngle: 0,
                endAngle: 180,
                antiClockwise: false,
                lineWidth: lineWidth,
                lineCap: "round" // "butt" "square" "round"
            };

            // Use Object.assign for better performance
            Object.assign(set, settings);

            this.offCanvasCTX.save();
            this.offCanvasCTX.beginPath();

            this.setFillStrokeStyle(set, this.offCanvasCTX);

            // Convert angles to radians once
            var startRad = set.startAngle * (Math.PI / 180);
            var endRad = set.endAngle * (Math.PI / 180);
            
            //arc(x, y, radius, startAngle, endAngle, counterclockwise)
            this.offCanvasCTX.arc(
                set.x, set.y, 
                set.radius, 
                startRad, endRad, 
                set.antiClockwise
            );
            
            if (set.fillStyle != null) {
                this.offCanvasCTX.fill();
            }
            if (set.strokeStyle != null) {
                this.offCanvasCTX.stroke();
            }

            if (this.onClip != null && this.clipOnce == false){ 
                this.clipOnce = true; 
                this.offCanvasCTX.clip(); 
                this.onClip(this); 
            }

            this.offCanvasCTX.closePath();
            this.offCanvasCTX.restore();
        },

        drawText: function (settings, size) {
            // Default settings with pre-calculated values
            var set = {
                fillStyle: null,
                strokeStyle: null,
                textShadow: null,
                maxWidth: null,
                textAlign: "center",
                textBaseline: "top",
                text: "text",
                fontWeight: "normal",
                fontFamily: "arial",
                fontSize: 60,
                offPosX: 0,
                offPosY: 0,
                shadowPosX: 5,
                shadowPosY: 5,
                textShadowAlpha: 1,
                lineWidth: 0,
                adjustFontHeight: 0,
                isResizeByText: false,
                isWrapWord: false
            };

            // Use Object.assign for better performance
            Object.assign(set, settings);

            // Check if we can use cached text properties
            var shouldUseCache = this._lastTextSettings && 
                this._cachedTextProperties && 
                set.text === this._lastTextSettings.text &&
                set.fontSize === this._lastTextSettings.fontSize &&
                set.fontFamily === this._lastTextSettings.fontFamily &&
                set.fontWeight === this._lastTextSettings.fontWeight &&
                set.isWrapWord === this._lastTextSettings.isWrapWord;

            this.offCanvasCTX.save();
            this.offCanvasCTX.textRendering = "optimizeSpeed";
            
            this.setFillStrokeStyle(set, this.offCanvasCTX);
            
            // Set font properties
            var fontString = set.fontWeight + " " + set.fontSize + "px " + set.fontFamily;
            this.offCanvasCTX.font = fontString;
            this.offCanvasCTX.textBaseline = set.textBaseline;
            this.offCanvasCTX.textAlign = set.textAlign;            
            
            // Get text properties (using cache if possible)
            if (!shouldUseCache) {
                if (set.isWrapWord) { 
                    this.textProperties = this.getTextPropertiesOnWordWrap(set, this.offCanvasCTX); 
                } else { 
                    this.textProperties = this.getTextProperties(set, this.offCanvasCTX); 
                }
                
                // Cache the text properties and settings
                this._cachedTextProperties = this.textProperties;
                this._lastTextSettings = {
                    text: set.text,
                    fontSize: set.fontSize,
                    fontFamily: set.fontFamily,
                    fontWeight: set.fontWeight,
                    isWrapWord: set.isWrapWord
                };
            } else {
                this.textProperties = this._cachedTextProperties;
            }

            // Handle text resizing
            if (set.isResizeByText && set.maxWidth == null) {
                if (set.isWrapWord) {
                    // Only update height for word wrap
                    this.size.y = Math.round(this.textProperties.textLinesHeight);
                } else {
                    this.size = new Vector2(
                        Math.round(this.textProperties.highestWidth), 
                        Math.round(this.textProperties.textLinesHeight)
                    );
                }

                // Update canvas dimensions
                this.offCanvas.width = this.size.x;
                this.offCanvas.height = this.size.y;

                set.isResizeByText = false;
                set.maxWidth = null;
                this._size = this.size;
                this.setPivot();

                // Reset context properties after canvas resize
                this.setFillStrokeStyle(set, this.offCanvasCTX);
                this.offCanvasCTX.font = fontString;
                this.offCanvasCTX.textBaseline = set.textBaseline;
                this.offCanvasCTX.textAlign = set.textAlign;  

                if (this.onTextResize) this.onTextResize(this);
            }

            // Set text position based on alignment
            this.textPos = new Vector2(0, 0);
            this.setTextAlign(set);
            
            // Draw text shadow if needed
            if (set.textShadow) {
                this.offCanvasCTX.save();
                this.offCanvasCTX.globalAlpha = set.textShadowAlpha;
                this.offCanvasCTX.fillStyle = set.textShadow;
                
                if (set.strokeStyle) {
                    this.offCanvasCTX.strokeStyle = set.textShadow;
                }

                var shadowPos = new Vector2(
                    this.textPos.x + set.shadowPosX, 
                    this.textPos.y + set.shadowPosY
                );
                
                if (set.isWrapWord) {
                    this.wordWrap(shadowPos, this.offCanvasCTX, set.strokeStyle != null);
                } else {
                    this.drawFillStrokeText(this.offCanvasCTX, shadowPos, false, set.maxWidth);
                    if (set.strokeStyle) {
                        this.drawFillStrokeText(this.offCanvasCTX, shadowPos, true, set.maxWidth);
                    }
                }

                this.offCanvasCTX.restore();
            }

            // Draw main text
            if (set.isWrapWord) {
                this.wordWrap(this.textPos, this.offCanvasCTX, set.strokeStyle != null);
            } else {
                this.drawFillStrokeText(this.offCanvasCTX, this.textPos, false, set.maxWidth);
                if (set.strokeStyle) {
                    this.drawFillStrokeText(this.offCanvasCTX, this.textPos, true, set.maxWidth);
                }
            }

            this.offCanvasCTX.restore();
        },

        setTextAlign: function (set){
            // Use a lookup table for text alignment positions
            var alignMap = {
                "center": [(this.size.x / 2) + set.offPosX, set.offPosY],
                "left": [0 + set.offPosX, set.offPosY],
                "right": [this.size.x + set.offPosX, set.offPosY]
            };
            
            var coords = alignMap[set.textAlign] || alignMap["center"];
            this.textPos = new Vector2(coords[0], coords[1]);
        },

        textProperties: null,
        getTextProperties: function (set, ctx){
            // Split text into lines
            var textLines = set.text.split('<br>');
            
            // Measure the first line to get font metrics
            var metrics = ctx.measureText(textLines[0]);
            
            // Calculate font height with platform-specific adjustment
            var fontHeight = (metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent) + 
                ((ig.ua.iOS || ig.ua.is_mac) ? 0 : set.adjustFontHeight);

            // Calculate total height for all lines
            var textLinesHeight = fontHeight * textLines.length;

            // Pre-allocate array for line widths
            var widthPerLine = new Array(textLines.length);
            var maxWidth = 0;
            
            // Measure each line and track the maximum width
            for (var i = 0; i < textLines.length; i++) {
                var width = ctx.measureText(textLines[i]).width;
                widthPerLine[i] = width;
                if (width > maxWidth) maxWidth = width;
            }

            // Return text properties object
            return { 
                widthPerLine: widthPerLine,
                highestWidth: maxWidth,
                textLines: textLines, 
                fontHeight: fontHeight, 
                textLinesHeight: textLinesHeight
            };
        },

        getTextPropertiesOnWordWrap: function (set, context){
            var text = set.text;
            var fitWidth = this.size.x;
            
            // Measure text for font metrics
            var metrics = context.measureText(text);
            var fontHeight = (metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent) + 
                ((ig.ua.iOS || ig.ua.is_mac) ? 0 : set.adjustFontHeight);

            // Split text into words
            var words = text.split(' ');
            var textLines = [];
            var currentLine = 0;
            
            // More efficient word wrapping algorithm
            if (words.length > 0) {
                var idx = 1;
                
                // Process words to fit within width
                while (words.length > 0 && idx <= words.length) {
                    // Join words up to current index
                    var str = words.slice(0, idx).join(' ');
                    var textWidth = context.measureText(str).width;

                    if (textWidth > fitWidth) {
                        // Handle case where a single word is too long
                        if (idx == 1) idx = 2;

                        // Add line and continue with remaining words
                        var setText = words.slice(0, idx - 1).join(' ');
                        textLines.push(setText);

                        currentLine++;
                        words = words.splice(idx - 1);
                        idx = 1;
                    } else {
                        // Try adding one more word
                        idx++;
                    }
                }

                // Add remaining words as the last line
                if (words.length > 0) {
                    currentLine++;
                    textLines.push(words.join(' '));
                }
            }

            // Return text properties object
            return { 
                widthPerLine: [], // Not used for word wrap
                highestWidth: fitWidth,
                textLines: textLines, 
                fontHeight: fontHeight, 
                textLinesHeight: fontHeight * currentLine
            };
        },

        wordWrap: function (textPos, ctx, isStrokeText){
            isStrokeText = isStrokeText != undefined ? true : false;
            var lines = this.textProperties.textLines;
            var fontHeight = this.textProperties.fontHeight;
            var x = textPos.x;
            
            // Render each line of text
            for (var i = 0; i < lines.length; i++) {
                var y = textPos.y + (fontHeight * i);
                
                // Always fill text
                ctx.fillText(lines[i], x, y);
                
                // Stroke text if needed
                if (isStrokeText) {
                    ctx.strokeText(lines[i], x, y);
                }
            }
        },

        drawFillStrokeText: function (ctx, textPos, isStroke, maxWidth){
            isStroke = isStroke != undefined ? isStroke : false;
            var lines = this.textProperties.textLines;
            var fontHeight = this.textProperties.fontHeight;
            var x = textPos.x;
            
            // Choose the appropriate text drawing function based on parameters
            var drawFunction;
            if (maxWidth == null) {
                drawFunction = isStroke ? 
                    function (text, x, y) { ctx.strokeText(text, x, y); } :
                    function (text, x, y) { ctx.fillText(text, x, y); };
            } else {
                drawFunction = isStroke ? 
                    function (text, x, y) { ctx.strokeText(text, x, y, maxWidth); } :
                    function (text, x, y) { ctx.fillText(text, x, y, maxWidth); };
            }
            
            // Render each line of text
            for (var i = 0; i < lines.length; i++) {
                var y = textPos.y + (fontHeight * i);
                drawFunction(lines[i], x, y);
            }
        },

        setFillStrokeStyle: function (set, ctx){
            // Apply fill style if provided
            if (set.fillStyle != null) {
                ctx.fillStyle = this.customFillStyle || set.fillStyle;
            }

            // Apply stroke style and related properties if provided
            if (set.strokeStyle != null) {
                ctx.strokeStyle = this.customStrokeStyle || set.strokeStyle;
                
                // Set line properties with defaults to avoid unnecessary assignments
                if (set.lineWidth) ctx.lineWidth = set.lineWidth;
                if (set.lineCap) ctx.lineCap = set.lineCap;
                if (set.lineJoin) ctx.lineJoint = set.lineJoin;
            }
        }
    });

});
