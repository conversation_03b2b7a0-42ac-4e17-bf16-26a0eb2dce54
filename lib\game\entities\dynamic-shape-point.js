//EntityDynamicShapePoint, it is used to plot points to form a shape on an image.
//This shape point can also rotate along with the image based on its center point of rotation

ig.module('game.entities.dynamic-shape-point')
.requires(
    'impact.entity'
)
.defines(function () {
    EntityDynamicShapePoint = ig.Entity.extend({
        name: "EntityDynamicShapePoint",
        zIndex: 99999,

        pointSize: new Vector2(20, 20),
        rotate: 0,

        isShow: false,
        isEdit: false,
        
        pointIndex: null,
        messageLog: null,
        parentObject: null,
        
        // New flag to control coordinate format output
        useCartesianOutput: false, // false = polar (default), true = cartesian
        
        // Center point for dragging entire shape
        isDraggingCenter: false,
        centerDragOffset: null,
        showCenterHandle: true, // Show center drag handle in edit mode

        points: [
            //[distance, angle]
            //[0, 0],
        ],

        _points: [
            // { x: 0, y: 0, isActive: false }
        ],

        init: function (x, y, settings){
            this.parent(x, y, settings);
            if (ig.global.wm) return;  
            
            if (settings){
                Object.entries(settings).forEach(function (value){
                    this[value[0]] = value[1];
                }.bind(this));
            }

            this.points.forEach(function (){
                this._points.push({ x: 0, y: 0, isActive: false });
            }.bind(this));

            this.ctx = ig.system.context;
            ig.game.sortEntitiesDeferred();
        },
        
        update: function (){
            this.parent();
            this.updatePoints();
            this.checkPointer();
        },

        draw: function (){
            this.parent();

            if (this._points.length >= 1 && this.isShow == true){
                this.ctx.save();

                if (ig.game.camera) this.ctx.translate(-ig.game.screen.x, -ig.game.screen.y);

                this.ctx.lineWidth = 5;
                this.ctx.strokeStyle = "yellow";

                if (this._points.length >= 2){
                    this.ctx.lineJoin = "round";
                    this.ctx.beginPath();
                    this.ctx.moveTo(this._points[0].x, this._points[0].y);
                }

                for (var i = 0; i < this._points.length; i++) {
                    this.drawPoints(this.ctx, this._points[i]);
                    if (this._points.length >= 2 && i >= 1){
                        this.ctx.lineTo(this._points[i].x, this._points[i].y);
                    }
                }

                if (this._points.length >= 2){
                    this.ctx.closePath();
                    this.ctx.stroke();
                }
                
                // Draw center handle for dragging entire shape
                if (this.isEdit && this.showCenterHandle && this.parentObject) {
                    this.drawCenterHandle(this.ctx);
                }

                this.ctx.restore();
            }
        },

        checkPointer: function (){
            if ( ig.game.io.mouse == undefined || this.isShow == false ||
                this.points.length == 0 || this.isEdit == false) return;

            var mousePos = ig.game.io.mouse.pos;
            var isPressed = ig.input.state('click');
            var wasJustPressed = ig.input.pressed('click');

            // Handle mouse release
            if (isPressed == false || isPressed == undefined){
                if (this.pointIndex != null) {
                    this._points[this.pointIndex].isActive = false;
                    if (this.messageLog != null) console.log(this.messageLog);
                }
                this.messageLog = null;
                this.pointIndex = null;
                this.isDraggingCenter = false;
                this.centerDragOffset = null;
                return;
            }

            var offsetPos = new Vector2(0, 0);
            if (ig.game.camera) offsetPos = new Vector2(ig.game.screen.x, ig.game.screen.y);
            
            // Check if we're clicking/dragging the center handle
            if (this.showCenterHandle && this.parentObject) {
                var centerPos = new Vector2(
                    this.parentObject.pos.x - offsetPos.x,
                    this.parentObject.pos.y - offsetPos.y
                );
                
                var centerHandleSize = 30; // Larger than point size for easier grabbing
                var checkCenterX =
                    mousePos.x >= centerPos.x - (centerHandleSize * 0.5) &&
                    mousePos.x <= centerPos.x + (centerHandleSize * 0.5);
                var checkCenterY =
                    mousePos.y >= centerPos.y - (centerHandleSize * 0.5) &&
                    mousePos.y <= centerPos.y + (centerHandleSize * 0.5);
                    
                if (checkCenterX && checkCenterY) {
                    if (wasJustPressed || !this.isDraggingCenter) {
                        this.isDraggingCenter = true;
                        this.centerDragOffset = new Vector2(
                            mousePos.x - centerPos.x,
                            mousePos.y - centerPos.y
                        );
                        // Deactivate any active point
                        if (this.pointIndex !== null) {
                            this._points[this.pointIndex].isActive = false;
                            this.pointIndex = null;
                        }
                    }
                }
            }
            
            // Handle center dragging
            if (this.isDraggingCenter && this.centerDragOffset) {
                this.dragCenter(mousePos, offsetPos);
                return; // Don't process individual points while dragging center
            }

            // Check if mouse is over any point
            var hoveredPointIndex = null;
            for (var i = 0; i < this._points.length; i++) {
                var newPos = new Vector2(this._points[i].x - offsetPos.x, this._points[i].y - offsetPos.y);

                var checkCollideX =
                    mousePos.x >= newPos.x - (this.pointSize.x * 0.5) &&
                    mousePos.x <= newPos.x + (this.pointSize.x * 0.5);

                var checkCollideY =
                    mousePos.y >= newPos.y - (this.pointSize.y * 0.5) &&
                    mousePos.y <= newPos.y + (this.pointSize.y * 0.5);

                if (checkCollideX && checkCollideY){
                    hoveredPointIndex = i;
                    break;
                }
            }

            // If we just clicked or if we're hovering over a different point, switch to it
            if (wasJustPressed || (hoveredPointIndex !== null && hoveredPointIndex !== this.pointIndex)) {
                // Deactivate previous point
                if (this.pointIndex !== null) {
                    this._points[this.pointIndex].isActive = false;
                }
                
                // Activate new point if hovering over one
                if (hoveredPointIndex !== null) {
                    this._points[hoveredPointIndex].isActive = true;
                    this.pointIndex = hoveredPointIndex;
                    this.messageLog = "";
                } else {
                    this.pointIndex = null;
                }
            }
            
            // Drag the currently selected point
            if (this.pointIndex !== null) {
                this.dragPoints(this.pointIndex, mousePos);
            }
        },

        updatePoints: function (){
            for (var i = 0; i < this.points.length; i++) {
                if (this._points[i].isActive == true) continue;

                var newPos = this.rotateAround(this.parentObject.pos, 
                    this.points[i][0], this.points[i][1] + this.rotate);
                this._points[i].x = newPos.x;
                this._points[i].y = newPos.y;
            }
        },

        dragPoints: function (pointIndex, mousePos){

            var offsetPos = new Vector2(0, 0);
            if (ig.game.camera) offsetPos = new Vector2(ig.game.screen.x, ig.game.screen.y);

            this._points[pointIndex].x = mousePos.x + offsetPos.x;
            this._points[pointIndex].y = mousePos.y + offsetPos.y;

            var newProp = this.findAngleAndDistance(this.parentObject.pos, this._points[pointIndex]);
            this.points[pointIndex][0] = newProp.distance;
            this.points[pointIndex][1] = newProp.angle;

            // Generate message log based on coordinate format flag
            this.messageLog = this.getCoordinatesString();
        },

        drawPoints: function (ctx, pos){
            ctx.save();
            ctx.fillStyle = pos.isActive ? "red" : "cyan";
            ctx.translate(pos.x, pos.y);
            ctx.fillRect(
                -this.pointSize.x * 0.5, -this.pointSize.y * 0.5, 
                 this.pointSize.x, this.pointSize.y
            );
            ctx.restore();
        },
        
        drawCenterHandle: function (ctx) {
            ctx.save();
            
            // Draw center handle with distinct style
            var centerHandleSize = 30;
            ctx.fillStyle = this.isDraggingCenter ? "orange" : "green";
            ctx.strokeStyle = "white";
            ctx.lineWidth = 3;
            
            ctx.translate(this.parentObject.pos.x, this.parentObject.pos.y);
            
            // Draw circle for center handle
            ctx.beginPath();
            ctx.arc(0, 0, centerHandleSize * 0.5, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            
            // Draw crosshair
            ctx.strokeStyle = "white";
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(-centerHandleSize * 0.3, 0);
            ctx.lineTo(centerHandleSize * 0.3, 0);
            ctx.moveTo(0, -centerHandleSize * 0.3);
            ctx.lineTo(0, centerHandleSize * 0.3);
            ctx.stroke();
            
            ctx.restore();
        },
        
        dragCenter: function (mousePos, offsetPos) {
            // Calculate new parent position based on mouse drag
            var newParentX = mousePos.x - this.centerDragOffset.x + offsetPos.x;
            var newParentY = mousePos.y - this.centerDragOffset.y + offsetPos.y;
            
            // Calculate the offset from the original parent position
            var deltaX = newParentX - this.parentObject.pos.x;
            var deltaY = newParentY - this.parentObject.pos.y;
            
            // Update parent object position (this moves the entire shape)
            this.parentObject.pos.x = newParentX;
            this.parentObject.pos.y = newParentY;
            
            // Log the new center position
            this.messageLog = "Center: {x:" + Math.round(newParentX) + ",y:" + Math.round(newParentY) + "}";
        },

        findAngleAndDistance: function (obj1, obj2) {
            /**
            * Find the angle and distance based on the two points
            * @param  {Vector2} obj1    - Object point
            * @param  {Vector2} obj2    - Center point
            * @return {Number} angle    - Angle between the two points
            * @return {Number} distance - Distance between the two points
            */

            // Calculate the differences
            var deltaX = obj2.x - obj1.x;
            var deltaY = obj2.y - obj1.y;

            // Calculate the distance
            var distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            // Calculate the angle in radians
            var theta = Math.atan2(deltaY, deltaX);

            // Convert the angle to degrees
            var angle = theta * (180 / Math.PI);

            return { angle: angle, distance: distance };
        },

        rotateAround: function (obj, distance, angle){
            /**
            * Rotate an object around on another object
            * @param   {object}  obj      - x, y position
            * @param   {number}  angle    - angle to rotate
            * @param   {number}  distance - distance between 2 objects
            * @return  {Vector2} returns new position
            */
            var theta = angle * Math.PI / 180;
            var newX = distance * Math.cos(theta) + obj.x;
            var newY = distance * Math.sin(theta) + obj.y;
            return { x: newX, y: newY };
        },

        /**
         * Get coordinates in the specified format
         * @param {Boolean} asCartesian - If true, returns Cartesian coordinates; if false, returns polar coordinates
         * @return {Array} Array of coordinates in the requested format
         */
        getCoordinates: function (asCartesian) {
            var useCartesian = asCartesian !== undefined ? asCartesian : this.useCartesianOutput;
            var coords = [];
            
            if (useCartesian) {
                // Return Cartesian coordinates
                for (var i = 0; i < this._points.length; i++) {
                    coords.push({
                        x: Math.round(this._points[i].x),
                        y: Math.round(this._points[i].y)
                    });
                }
            } else {
                // Return polar coordinates
                for (var i = 0; i < this.points.length; i++) {
                    coords.push({
                        distance: Math.round(this.points[i][0]),
                        angle: Math.round(this.points[i][1])
                    });
                }
            }
            
            return coords;
        },

        /**
         * Get coordinates as a formatted string for logging
         * @param {Boolean} asCartesian - Optional override for coordinate format
         * @return {String} Formatted string of coordinates
         */
        getCoordinatesString: function (asCartesian) {
            var useCartesian = asCartesian !== undefined ? asCartesian : this.useCartesianOutput;
            var coordString = "";
            
            if (useCartesian) {
                // Format as Cartesian coordinates
                for (var i = 0; i < this._points.length; i++) {
                    coordString += "{x:" + Math.round(this._points[i].x) +
                                  ",y:" + Math.round(this._points[i].y) + "},";
                }
            } else {
                // Format as polar coordinates
                for (var i = 0; i < this.points.length; i++) {
                    coordString += "[" + Math.round(this.points[i][0]) + "," +
                                  Math.round(this.points[i][1]) + "],";
                }
            }
            
            // Remove trailing comma
            if (coordString.length > 0) {
                coordString = coordString.slice(0, -1);
            }
            
            return coordString;
        }
        
    });
});