﻿<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<title>Weltmeister</title>
	<link rel="stylesheet" type="text/css" href="lib/weltmeister/weltmeister.css"/>

	<script src="settings/dev.js" type="text/javascript" charset="utf-8"></script>
	<script src="lib/weltmeister/jquery-1.7.1.min.js" type="text/javascript" charset="utf-8"></script>
	<script src="lib/weltmeister/jquery-ui-1.8.1.custom.min.js" type="text/javascript" charset="utf-8"></script>
	<script src="lib/impact/impact.js" type="text/javascript" charset="utf-8"></script>

    <script src="lib/plugins/data/color-rgb.js" type="text/javascript" charset="utf-8"></script>
	<script src="lib/plugins/data/vector.js" type="text/javascript" charset="utf-8"></script>
	<script src="lib/weltmeister/weltmeister.js" type="text/javascript" charset="utf-8"></script>
</head>
<body>
<div id="headerMenu">
	<span class="headerTitle"></span>
	<span class="unsavedTitle"></span>
	<span class="headerFloat">
		<input type="button" id="levelSave" value="Save" class="button"/>
		<input type="button" id="levelSaveAs" value="Save As" class="button"/>
		<input type="button" id="levelNew" value="New" class="button"/>
		<input type="button" id="levelLoad" value="Load" class="button"/>
		<input type="button" id="reloadImages" value="Reload Images" title="Reload Images" class="button"/>
		<input type="button" id="toggleSidebar" value="Toggle Sidebar" title="Toggle Sidebar" class="button"/>
	</span>
</div>

<div id="editor">
	<div id="entityMenu"></div>
	
	<canvas id="canvas"></canvas>
	
	<div id="menu">
		<div id="layerContainer">
			<h2>Layers</h2>
			<div id="layerButtons">
				<div class="button" id="buttonAddLayer" title="Add Layer">+</div>
			</div>
			<div id="layers">
				<div id="layerEntities" class="layer">
					<span class="visible specialVis checkedVis" title="Toggle Visibility (Shift+0)"></span>
					<span class="name">entities</span>
				</div>
			</div>
		</div>
		
		
		<div id="layerSettings">
			<h2>Layer Settings</h2>
			<dl>
				<dt>Name:</dt><dd><input type="text" class="text" id="layerName"/></dd>
				<dt>Tileset:</dt><dd><input type="text" class="text" id="layerTileset"/></dd>
				<dt>Tilesize:</dt><dd><input type="text" class="number" id="layerTilesize"/></dd>
				<dt>Dimensions:</dt>
				<dd>
					<input type="text" class="number" id="layerWidth"/> &times; <input type="text" class="number" id="layerHeight"/>
				</dd>
				<dt>Distance:</dt><dd><input type="text" class="number" id="layerDistance"/></dd>
				<dd>
					<input type="checkbox" id="layerIsCollision"/>
					<label for="layerIsCollision">Is Collision Layer</label>
				</dd>
				<dd>
					<input type="checkbox" id="layerPreRender"/>
					<label for="layerPreRender">Pre-Render in Game</label>
				</dd>
				<dd>
					<input type="checkbox" id="layerRepeat"/>
					<label for="layerRepeat">Repeat</label>
				</dd>
				<dd>
					<input type="checkbox" id="layerLinkWithCollision"/>
					<label for="layerLinkWithCollision">Link with Collision</label>
				</dd>
				<dd>
					<input type="button" id="buttonSaveLayerSettings" value="Apply Changes" class="button"/>
					<input type="button" id="buttonRemoveLayer" value="Delete" class="button"/>
				</dd>
			</dl>
		</div>
		
		<div id="entitySettings">
			<h2>Entity Settings</h2>
			<h3 id="entityClass">EntityClassName</h3>
			<div id="entityDefinitions">
				<div class="entityDefinition"><span class="key">x</span>:<span class="value">188</span></div>
				<div class="entityDefinition"><span class="key">y</span>:<span class="value">269</span></div>
			</div>
			<dl id="entityDefinitionInput">
				<dt>Key:</dt><dd><input type="text" class="text" id="entityKey"/></dd>
				<dt>Value:</dt><dd><input type="text" class="text" id="entityValue"/></dd>
			</dl>
		</div>
	</div>
	
	<div id="zoomIndicator">1x</div>
	
</div>
</body>
</html>
