/*
    This plugin provides multiple methods to randomize outcomes:
    - Shuffle Bag Method
    - Avoid Immediate Repetition
    - Weighted Randomization
    - Queue System

    ============================== USAGE ==============================
    var outcomes = ['a', 'b', 'c'];
    var weights = { 'a': 0.5, 'b': 0.3, 'c': 0.2 };
    var randomizer = new ig.Randomizer(outcomes, weights);

    var outcome1 = randomizer.getRandomOutcome('shuffleBag');
    var outcome2 = randomizer.getRandomOutcome('avoidRepetition');
    var outcome3 = randomizer.getRandomOutcome('weighted');
    var outcome4 = randomizer.getRandomOutcome('queue');

    // Or use individual methods:
    var outcome5 = randomizer.randomizeShuffleBag();
*/

ig.module(
    'plugins.utils.randomizer'
).requires(
    'impact.impact'
).defines(function () {
    ig.Randomizer = ig.Class.extend({
        shuffleBag: null,
        lastOutcome: null,
        queue: null,
        outcomes: [],
        weights: {},
        history: {
            shuffleBag: [],
            avoidRepetition: [],
            weighted: [],
            queue: []
        },
        
        init: function (outcomes, weights) {
            this.setOutcomes(outcomes);
            this.setWeights(weights);
        },
        
        setOutcomes: function (outcomes) {
            if (Object.prototype.toString.call(outcomes) === '[object Array]') {
                this.outcomes = outcomes;
            } else {
                throw new Error('Outcomes must be an array');
            }
        },
        
        setWeights: function (weights) {
            if (typeof weights === 'object' && weights !== null) {
                this.weights = weights;
                this.normalizeWeights();
            } else {
                throw new Error('Weights must be an object');
            }
        },
        
        normalizeWeights: function () {
            var total = 0;
            for (var outcome in this.weights) {
                if (this.weights.hasOwnProperty(outcome)) {
                    total += this.weights[outcome];
                }
            }
            for (var outcome in this.weights) {
                if (this.weights.hasOwnProperty(outcome)) {
                    this.weights[outcome] /= total;
                }
            }
        },

        /*
            This method involves creating a shuffle bag that contains a pre-determined number of each type of outcome.
            Once an outcome is selected, it is removed from the bag, and only replenished once the bag is empty.
        */
        randomizeShuffleBag: function () {
            if (!this.shuffleBag || this.shuffleBag.length === 0) {
                this.shuffleBag = this.outcomes.slice();
                this.shuffleBag.sort(function () { return Math.random() - 0.5; });
            }
            var outcome = this.shuffleBag.pop();
            this.addToHistory('shuffleBag', outcome);
            return outcome;
        },

        /*
            Keep track of the last outcome and avoid using it again immediately.
            This doesn't ensure a perfect distribution but avoids consecutive repetitions.
        */
        randomizeAvoidRepetition: function () {
            var self = this;
            var possibilities = this.outcomes.filter(function (outcome) {
                return outcome !== self.lastOutcome;
            });
            if (possibilities.length === 0) {
                possibilities = this.outcomes.slice();
            }
            var outcome = possibilities[Math.floor(Math.random() * possibilities.length)];
            this.lastOutcome = outcome;
            this.addToHistory('avoidRepetition', outcome);
            return outcome;
        },

        /*
            Create a table of weighted outcomes. Higher weight means more possibility of it appearing
            Sum of all weights should not exceed 100% or 1.0
        */
        randomizeWeighted: function () {
            var r = Math.random();
            var cumulativeWeight = 0;
            for (var outcome in this.weights) {
                if (this.weights.hasOwnProperty(outcome)) {
                    cumulativeWeight += this.weights[outcome];
                    if (r <= cumulativeWeight) {
                        this.addToHistory('weighted', outcome);
                        return outcome;
                    }
                }
            }
            var lastOutcome = this.outcomes[this.outcomes.length - 1];
            this.addToHistory('weighted', lastOutcome);
            return lastOutcome; // Fallback to last outcome
        },

        /*
            Queue the next outcomes in a sequence that prevents immediate repetition and cycles through types in a more controlled manner.
        */
        randomizeQueue: function () {
            if (!this.queue || this.queue.length === 0) {
                this.queue = this.outcomes.slice();
                this.queue.sort(function () { return Math.random() - 0.5; });
            }
            var nextOutcome = this.queue.shift();
            this.queue.push(nextOutcome);
            this.addToHistory('queue', nextOutcome);
            return nextOutcome;
        },
        
        getRandomOutcome: function (method) {
            method = method || 'shuffleBag';
            switch (method.toLowerCase()) {
                case 'shufflebag':
                    return this.randomizeShuffleBag();
                case 'avoidrepetition':
                    return this.randomizeAvoidRepetition();
                case 'weighted':
                    return this.randomizeWeighted();
                case 'queue':
                    return this.randomizeQueue();
                default:
                    throw new Error('Invalid randomization method');
            }
        },

        addToHistory: function (method, outcome) {
            if (this.history.hasOwnProperty(method)) {
                this.history[method].push(outcome);
            } else {
                throw new Error('Invalid randomization method for history');
            }
        },

        getHistory: function (method) {
            if (method === undefined) {
                return this.history;
            }
            if (this.history.hasOwnProperty(method)) {
                return this.history[method];
            }
            throw new Error('Invalid randomization method for history');
        },

        clearHistory: function (method) {
            if (method === undefined) {
                for (var key in this.history) {
                    if (this.history.hasOwnProperty(key)) {
                        this.history[key] = [];
                    }
                }
            } else if (this.history.hasOwnProperty(method)) {
                this.history[method] = [];
            } else {
                throw new Error('Invalid randomization method for history');
            }
        }
    });
});
