/**
 * Extends the `ig.Animation` class to add scaling functionality.
 * 
 * The `scale` property allows you to set the x and y scaling factors for the animation, as well as the alignment of the origin point.
 * 
 * The `draw` method is overridden to apply the scaling transformation before drawing the animation.
 * 
 * 
 * Usage:
 * 
 * @example
 * Double the scale of an animation:
 * this.currentAnim.scale.x = 2;
 * this.currentAnim.scale.y = 2;
 * 
 * Scale the animation, but change the origin point (button sinking, but sinks from the left):
 * this.currentAnim.scale.x = 0.5;
 * this.currentAnim.scale.y = 0.5;
 * this.currentAnim.scale.align.x = 'left';
 */
ig.module( 'plugins.animation-scale')
.requires(
	'impact.animation'
)
.defines(function () {
    ig.Animation.inject({
        /**
         * The scaling properties for the animation.
         * @property {number} x - The horizontal scaling factor.
         * @property {number} y - The vertical scaling factor.
         * @property {object} align - The alignment of the origin point.
         * @property {string|false} align.x - The horizontal alignment, either 'left', 'right', or false.
         * @property {string|false} align.y - The vertical alignment, either 'top', 'bottom', or false.
         */
        scale: { x: 1, y: 1, align: { x: false, y: false } },
		
		draw: function ( targetX, targetY ) {
			var scale = ig.system.scale;
			ig.system.context.save();
			var originX = 0, originY = 0;
			/**
			* Calculates the horizontal origin point for the animation based on the `scale.align.x` property.
			* 
			* If `scale.align.x` is set to 'left', the origin point is set to the target X coordinate.
			* If `scale.align.x` is set to 'right', the origin point is set to the target X coordinate plus the width of the animation sheet.
			* Otherwise, the origin point is set to the target X coordinate plus half the width of the animation sheet.
			*
			* @param {number} targetX - The target X coordinate for the animation.
			* @returns {number} The calculated horizontal origin point.
			*/
			originX = (this.scale.align && this.scale.align.x && this.scale.align.x == 'left') ?  targetX
					: (this.scale.align && this.scale.align.x && this.scale.align.x == 'right') ? targetX + this.sheet.width
					: targetX + (this.sheet.width / 2);
			/**
			* Calculates the vertical origin point for the animation based on the `scale.align.y` property.
			*
			* If `scale.align.y` is set to 'top', the origin point is set to the target Y coordinate.
			* If `scale.align.y` is set to 'bottom', the origin point is set to the target Y coordinate plus the height of the animation sheet.
			* Otherwise, the origin point is set to the target Y coordinate plus half the height of the animation sheet.
			*
			* @param {number} targetY - The target Y coordinate for the animation.
			* @returns {number} The calculated vertical origin point.
			*/
			originY = (this.scale.align && this.scale.align.y && this.scale.align.y == 'top') ?  targetY
					: (this.scale.align && this.scale.align.y && this.scale.align.y == 'bottom') ? targetY + this.sheet.height
					: targetY + (this.sheet.height / 2);
			/**
			* Applies a scaling transformation to the animation before drawing it.
			* 
			* The method first translates the drawing context to the calculated origin point of the animation, then scales the context by the specified `x` and `y` scaling factors. Finally, it translates the context back to the original position.
			* 
			* This allows the animation to be drawn at a different scale while maintaining the correct origin point and alignment.
			*/
			ig.system.context.translate(originX * scale, originY * scale);
			ig.system.context.scale(this.scale.x, this.scale.y);
			ig.system.context.translate(-originX * scale, -originY * scale);
			this.parent(targetX, targetY);
			ig.system.context.restore();
		}
    });
});