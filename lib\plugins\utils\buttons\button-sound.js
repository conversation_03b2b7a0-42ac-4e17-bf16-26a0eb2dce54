ig.module('plugins.utils.buttons.button-sound')
.requires(
	'plugins.utils.buttons.button-image'
)
.defines(function () {
	EntityButtonSound = EntityButtonImage.extend({
		idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-blank.png'), frameCountX: 1, frameCountY: 1 },
		volume: 0.5,
        mutedFlag: false,
		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.anims.off = new ig.Animation(this.idleSheet, 1, [0], true);
			this.anims.on = new ig.Animation(this.idleSheet, 1, [1], true);
			this.mutedFlag = ig.game.load('music') === 0;

			this.handleOnLoadAudio();
		},
        onClickCallback: function () {
            this.handleAudio();
        },
        handleAudio: function () {
            if (this.mutedFlag)
            {
                // console.log('unmute');
                this.currentAnim = this.anims.on;
				/** unmute */
                // ig.soundHandler.unmuteAll();

				/** volume */
				ig.soundHandler.bgmPlayer.volume(this.volume);
				ig.soundHandler.sfxPlayer.volume(this.volume);

                if (!ig.soundHandler.bgmPlayer.soundList.background.playing()) ig.soundHandler.bgmPlayer.play('background');

				/** save session data */
				ig.game.save('music', 0.5);
				ig.game.save('sound', 0.5);

                this.mutedFlag = false;
            }
            else
            {
                // console.log('mute');
                this.currentAnim = this.anims.off;
				/** mute */
                // ig.soundHandler.muteAll();

				/** volume */
				ig.soundHandler.bgmPlayer.volume(0);
				ig.soundHandler.sfxPlayer.volume(0);

				/** save session data */
				ig.game.save('music', 0);
				ig.game.save('sound', 0);

                this.mutedFlag = true;
            }
        },
        handleOnLoadAudio: function () {
            if (this.mutedFlag)
            {
                this.currentAnim = this.anims.off;
                ig.soundHandler.bgmPlayer.volume(0);
				ig.soundHandler.sfxPlayer.volume(0);
            }
            else
            {
                this.currentAnim = this.anims.on;
                ig.soundHandler.bgmPlayer.volume(this.volume);
				ig.soundHandler.sfxPlayer.volume(this.volume);
            }
        }
	});
});
