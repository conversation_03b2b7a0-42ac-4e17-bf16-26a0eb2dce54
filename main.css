html,body {
	background-color: #000;
	margin: 0;
	padding: 0;
	position: relative;
	font-family:"Arial";
	width:100%;
	touch-action: none;
	overflow: hidden;
}

#game {
	position: absolute;
	left: 0;
	top: 0;
	z-index: 0;
}

#ajaxbar{
	background:url('media/graphics/loading/ajax-loader.gif') center no-repeat;
}

#canvas {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	/*image-rendering: optimizeSpeed;*/
	-ms-interpolation-mode: nearest-neighbor;
	/*image-rendering:-webkit-optimize-contrast;*/
	-webkit-transform: scale3d(1, 1, 1);
	z-index: 1;
}

#orientate {
	position: absolute;
	float:left;
	width:100%;
	height:100%;
	top: 0;
	left: 0;
	z-index: 10002;
	display:none;
}

#orientate img {
	position:absolute;
	float:left;
	width:100%;
	height:100%;
}
/* Deprecated
#scrollDown{
	border-style: none;
	height:0%;
	width:0%;
	right:0;
	top:0;
	position:absolute;
	z-index:12000;
}
*/
.play {
	position:absolute;
	float:left;
	width:100%;
	height:100%;
	z-index:1000;
	background-color:#fff;
	left:0;
	top:0;
	display:none;
}

.play img {
	position:absolute;
	float:left;
	width:100%;
	height:100%;
	z-index:1000;
	left:0;
	top:0;
}

#nohtml5 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: #000;
	z-index: 1002;
	visibility:hidden;
}

#nohtml5 img {
	position: absolute;
	width: 100%;
	height: 100%;
}

#nohtml5-bubble{
	position: absolute;
	bottom: 20px;
	left: 50px;
	width: 380px;
	height: 100px;
	z-index: 1002;
	color:#000;
	background:rgba(255,255,255,0.75);

	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	/*border:2px solid #969696;*/
}

#nohtml5-text{
	padding:10px;
}

.horizontal-seperator{
	height:10px;
	width:100%;
}
