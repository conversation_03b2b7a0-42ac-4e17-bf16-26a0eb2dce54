# Comprehensive Technical Documentation: Truck System

This document provides a complete technical overview of the truck entity system in the ImpactJS game, based on a detailed analysis of `lib/game/entities/objects/truck.js` and related game files.

---

## 1. Truck Pathfinding System

### 1.1. Overview

The Truck Pathfinding System is a core gameplay mechanic responsible for the movement and navigation of truck entities. It is a player-driven system where routes are manually drawn by the user, rather than being determined by an automated pathfinding algorithm like A*. The system's complexity lies in its real-time path validation, waypoint following, and automated sequences for parking and exiting the level.

The primary files governing this system are:
*   `lib/game/entities/objects/truck.js`: The main truck entity containing all movement, input, and state logic.
*   `lib/game/entities/path-head.js`: A logical entity used for real-time collision detection of the drawn path.
*   `lib/game/entities/objects/parking-slot.js`: Defines the destination points for trucks.
*   `lib/game/levels/*.js`: Level files that define the physical environment, including collision boundaries, parking slot locations, and exit zones.

### 1.2. Truck Navigation and Route Drawing

Truck navigation is initiated and controlled directly by the player through a click-and-drag interface.

#### 1.2.1. Path Creation

1.  **Initiation**: The player clicks on a truck entity. This action is detected in `truck.js` by the `handleInput` function, which calls `onClicked()`.
2.  **Path Head**: A logical `EntityPathHead` is spawned at the cursor's position. This entity has a circular collision shape whose radius (`pathHeadRadius`) is based on the truck's width, effectively representing the truck's physical footprint as the path is drawn.
3.  **Drawing**: As the player drags the cursor, the `clicking()` function continuously updates the `pathHead`'s position and adds coordinates to the truck's `trajectory` array.
    *   Points are added only if the distance between them exceeds a minimum threshold, preventing an excessive number of waypoints.
    *   To ensure path smoothness, the system interpolates and adds extra points if the cursor moves a large distance between frames.
4.  **Path Validation**: The system performs two crucial validation checks in real-time:
    *   **Collision Detection**: The `checkPathHeadCollisions()` function uses the Separating Axis Theorem (SAT) to check if the `pathHead`'s shape intersects with any static obstacles (like buildings defined in the level file) or other trucks. If a collision is detected, the path is marked as invalid (`isCurrentPathInvalid = true`), and the user cannot continue drawing that route.
    *   **Minimum Distance**: The initial segment of the path must exceed a minimum length (`minTrajectoryDistance`), preventing accidental, short paths.

#### 1.2.2. Route Following

Once the player releases the mouse button, the drawn `trajectory` array becomes the truck's route.

1.  **State Transition**: The truck enters a movement state where `isFollowingTrajectory()` returns `true`.
2.  **Waypoint Processing**: The `updateTrajectoryMovement()` function is the core of the navigation loop.
    *   It retrieves the next waypoint from the front of the `trajectory` array using `getCurrentWaypoint()`.
    *   It calculates the required angle to the waypoint and smoothly rotates the truck towards it using `updateAngleSimple()`.
    *   The truck's velocity is applied in its forward direction. The speed is dynamically adjusted based on the sharpness of the turn; sharper turns result in lower speeds to simulate more realistic movement.
3.  **Waypoint Completion**: The truck is considered to have reached a waypoint when its pivot point is within a small tolerance radius (`checkWaypointReached()`). Upon reaching a waypoint, it is removed from the `trajectory` array via `advanceWaypoint()`, and the truck proceeds to the next point.
4.  **Route Completion**: When the `trajectory` array is empty, the `finalizeTrajectoryMovement()` function is called, and the truck stops.

### 1.3. Pathfinding Algorithm and AI Logic

The system does not use a traditional, automated pathfinding algorithm. The "intelligence" is focused on assisting and validating the player's manually drawn path.

#### 1.3.1. Snap-to-Park (Parking Assistance)

This is the system's primary "AI" decision-making feature. It simplifies the process of parking a truck.

1.  **Detection**: While the player is drawing a path, the `attemptSnapToParkingSlot()` function checks if the path's endpoint is within the `optimizationRadius` of a parking slot's designated `frontPoint`.
2.  **Validation**: A slot is a valid target only if it is not currently occupied (`isCurrentlyOccupied()`) and its `color` property matches the truck's `color`.
3.  **Execution**: If a valid target is found, the path is automatically extended to the slot's `frontPoint`, and the trajectory is finalized. This triggers the automated parking sequence.

#### 1.3.2. Path Smoothing (Chaikin's Algorithm)

The file `lib/plugins/path-smoother.js` contains an implementation of Chaikin's algorithm, which can be used to smooth the sharp corners of a path. However, analysis of `truck.js` shows that the calls to this smoothing function are currently commented out.

---

## 2. Parking and Unloading Mechanics

### 2.1. Parking System Implementation

The parking system is initiated when a player draws a path that ends in proximity to a valid parking spot. The core of this system is the "Snap-to-Park" feature that automates the final approach.

#### 2.1.1. Automated Parking Maneuver

- **Initiation**: Once a path is snapped, the truck enters the parking state by setting `isParking = true` and `truckState = 'parking'`.
- **Movement & Alignment**: The `updateParking` and `handleParking` functions manage the maneuver, moving the truck to the slot's center and rotating it to the final parking angle.
- **Completion**: The maneuver is complete when the truck is within a `parkingThreshold` distance of the slot's center and correctly aligned.

### 2.2. Unloading Process

The "unloading" process is a timed event that occurs immediately after a truck has successfully parked.

- **State Trigger**: Upon successful parking, `finishParking` is called, setting `isParked = true` and `truckState = 'waiting'`.
- **Unloading Timer**: A `parkingWaitTimer` counts up to `parkingWaitTime`. A circular progress indicator is drawn on-screen.
- **Completion**: Once the timer finishes, `truckState` transitions to `'exit-ready'`, and the truck's angle is flipped 180 degrees, ready for exit.

### 2.3. State Transitions during Parking

The truck's behavior is governed by a set of boolean flags and a state machine property.

```mermaid
graph TD
    A[Idle / Following Path<br>(truckState: 'none')] --> B{Path Snaps?};
    B -->|Yes| C[Parking Maneuver<br>(isParking: true, truckState: 'parking')];
    C --> D[Parked & Unloading<br>(isParked: true, truckState: 'waiting')];
    D -->|Timer Complete| E[Ready for Exit<br>(isParked: true, truckState: 'exit-ready')];
    E -->|Player Draws Exit Path| F[Exiting<br>(isParked: false, truckState: 'exiting')];
    F --> G[Off-screen & Removed];
```

### 2.4. Parking Validation Logic

The `_validateSlot` function ensures a truck can only park in a valid slot:
- **Color Matching**: The truck's `color` must match the slot's `color`.
- **Occupancy Status**: The slot must not be currently occupied.

---

## 3. Collision Detection and Response System

### 3.1. Collision Boundaries and Shapes

Trucks use precise, custom-defined polygons for collision detection, allowing for shapes that accurately match the truck's sprite.

- **Main Body Shape**: Defined in `customVertices`, these are rotated and translated to world space to create a `ig.SAT.Shape` for collision checks.
- **Attack Box**: A smaller, forward-facing rectangle (`attackBoxPercentage`) is used for specific checks, like detecting off-screen boundaries.

### 3.2. Three-Phase Collision Response System

When a truck collides with a `BUILDING` or `BOUNDARY`, it initiates a three-phase response.

1.  **Phase 1: Immediate Stop & Bounce Initiation**
    *   The truck's trajectory is cleared.
    *   A "crash" sound plays.
    *   `startBounceBack()` is called, setting `isBouncing = true` and calculating the bounce direction.
    *   The collision mask is temporarily modified to prevent getting stuck.

2.  **Phase 2: Bounce Back Animation**
    *   `updateBounceBack()` moves the truck in the `bounceBackDirection` with a speed that eases out over time.

3.  **Phase 3: Resume Normal State**
    *   `finishBounceBack()` stops all movement.
    *   The original collision mask is restored after a short delay.

### 3.3. Special Collision Handling

- **Parking Slots**: Collisions with `PARKING_SLOT` entities do not trigger the bounce-back response but instead initiate the parking sequence.
- **Inter-Truck Collisions**: Direct physics collisions are avoided. The `checkPathHeadCollisions()` function prevents drawing a path through another truck, and the Proximity Detection System provides warnings.

---

## 4. Additional Core Truck Functions

### 4.1. Truck State Management and Lifecycle

The `truckState` property governs the truck's lifecycle, progressing from `'none'` (idle) through `'parking'`, `'waiting'`, `'exit-ready'`, and finally `'exiting'` before being removed from the game.

### 4.2. Proximity Detection System

Managed in `updateProximityDetection`, this system prevents collisions and adds realism.

- **Detection**: Checks for other trucks within a `proximityThreshold`.
- **Audible Warning**: Plays a "honk" sound when another truck is too close.
- **Visual Warning**: Swaps the truck's animation to a red-colored "warning" version.

### 4.3. Exit Conditions and Screen Boundary Handling

- **Exit Zones**: Defined in level data, these are areas where trucks can despawn. An `'exit-ready'` truck with a path ending in an exit zone will enter `'exit'` mode.
- **Exiting Process**: The truck continues moving in its last direction until it is fully off-screen, at which point it is destroyed and the score is incremented.
- **Screen Boundaries**: For non-exiting trucks, the screen edges act as solid walls, triggering the standard collision response.

### 4.4. Performance Optimizations

- **Exit Zone Caching**: The bounding boxes of exit zones are pre-calculated and cached on initialization.
- **Proximity Check Throttling**: Proximity checks are performed on a cooldown, not every frame.
- **Conditional Updates**: Position and collision vertex calculations are skipped for stationary trucks.
- **Broad-Phase Collision Detection**: A cheap AABB overlap test is performed before the more expensive SAT polygon collision check.