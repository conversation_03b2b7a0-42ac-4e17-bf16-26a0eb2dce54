ig.module('plugins.tweens-handler').requires('impact.entity', 'plugins.tween', "plugins.patches.entity-patch")
	.defines(function () {
		/**
		 *
		 * var coords = { x: 0, y: 0 };
		 * var tween = new ig.TweenDef(coords)
		 * 	.to({ x: 100, y: 100 }, 1000)
		 * 	.onUpdate(function() {
		 * 		console.log(this.x, this.y);
		 * })
		 * .start();
		 * 
		 * requestAnimationFrame(animate);
		 * 
		 * function animate(time) {
		 * 	requestAnimationFrame(animate);
		 * 	ig.TweenDef.update(time);
		 * }
		 * 
		 */

		if (!Array.prototype.indexOf) {
			Array.prototype.indexOf = function (el, start) {
				var start = start || 0;
				for (var i = 0; i < this.length; ++i) {
					if (this[i] === el) {
						return i;
					}
				}
				return -1;
			};
		};

		ig.TweensHandler = ig.Class.extend({
			_tweens: [],
			_systemPausedTweens: [],

			init: function () {},

			getAll: function () {
				return this._tweens;
			},

			removeAll: function () {
				this._tweens = [];
			},

			add: function (tween) {
				this._tweens.push(tween);
			},

			remove: function (tween) {
				var i = this._tweens.indexOf(tween);
				if (i !== -1) {
					this._tweens.splice(i, 1);
				}
			},

			onSystemPause: function () {
				this._tweens.forEach(function (tween) {
					if (tween._isPlaying) {
						// register this tween to be resumed 
						this._systemPausedTweens.push(tween);
						tween.pause();
					}
				}.bind(this));
			},

			onSystemResume: function () {
				this._systemPausedTweens.forEach(function (tween) {
					tween.resume();
				});
				// clean up the array
				this._systemPausedTweens = [];
			},

			update: function (time) {
				this._tweens.forEach(function (tween) {
					tween.update(time);
				});
			},

			now: function () {
				return Date.now();
			}
		});

		ig.TweenDef = ig.Class.extend({
			version: "2.0.0",
			_ent: null,
			_valuesStart: {},
			_valuesEnd: {},
			_valuesStartRepeat: {},
			_duration: 1000,
			_repeat: 0,
			//_repeatDelayTime,
			_yoyo: false,
			_isPlaying: false,
			_reversed: false,
			_delayTime: 0,
			_startTime: null,
			_pauseTime: null,
			_easingFunction: ig.Tween.Easing.Linear.EaseNone,
			_interpolationFunction: ig.Tween.Interpolation.Linear,
			_chainedTweens: [],
			_onStartCallback: null,
			_onStartCallbackFired: false,
			_onUpdateCallback: null,
			_onCompleteCallback: null,
			_onStopCallback: null,
			_onPauseCallback: null,
			_onResumeCallback: null,
			_currentElapsed: 0,

			init: function (object) {
				this._object = object;
			},

			to: function (properties, duration) {
				this._valuesEnd = properties;

				if (duration !== undefined) {
					this._duration = duration;
				}
				return this;
			},

			start: function (time) {
				if (this._isPlaying) {
					return this;
				}

				ig.game.tweens.add(this);
				this._isPlaying = true;

				this._onStartCallbackFired = false;

				this._startTime = time !== undefined ? time : ig.game.tweens.now();
				this._startTime += this._delayTime;

				var _valuesEnd = this._valuesEnd;
				this._valuesEnd = {};
				for (var property in _valuesEnd) {
					// Check if an Array was provided as property value
					if (_valuesEnd[property] instanceof Array) {
						if (_valuesEnd[property].length === 0) continue;
						// Create a local copy of the Array with the start value at the front
						_valuesEnd[property] = [this._object[property]].concat(_valuesEnd[property]);
					}

					// If `to()` specifies a property that doesn't exist in the source object,
					// we should not set that property in the object					
					if (this._object[property] === undefined) continue;

					// Process ending values and Save starting values
					if (typeof _valuesEnd[property] == 'object') {
						var obj = _valuesEnd[property];
						this._valuesEnd[property] = {};
						var props = ['position', 'scaling', 'rotation'];
						if (props.indexOf(property) != -1) {
							this._valuesStart[property] = {};
							var axes = ['x', 'y', 'z'];
							axes.forEach(function (axis) {
								if (typeof obj[axis] != 'undefined') {
									this._valuesStart[property][axis] = this._object[property][axis];
									this._valuesEnd[property][axis] = obj[axis];
								}
							}.bind(this));
						} else {
							this._valuesEnd[property] = ig.copy(obj);
							this._valuesStart[property] = ig.copy(this._object[property]);
						}
					} else {
						this._valuesEnd[property] = _valuesEnd[property];
						this._valuesStart[property] = this._object[property];
					}

					this._valuesStartRepeat[property] = this._valuesStart[property] || 0;
				}
				return this;
			},

			set: function (settings) {
				ig.merge(this, settings);
				return this;
			},

			stop: function () {
				if (!this._isPlaying) {
					return this;
				}

				ig.game.tweens.remove(this);
				this._isPlaying = false;

				if (this._onStopCallback !== null) {
					this._onStopCallback.call(this._object, this._object);
				}

				this.stopChainedTweens();
				return this;
			},

			pause: function () {
				/* check if playing */
				if (!this._isPlaying) {
					return this;
				}

				/* set paused flag to stop updating */
				ig.game.tweens.remove(this);
				this._isPlaying = false;

				/* keep track of where it stopped - paused timestamp */
				this._pauseTime = ig.game.tweens.now();

				/* callback function */
				if (this._onPauseCallback !== null) {
					this._onPauseCallback.call(this._object, this._object);
				}
				return this;
			},

			resume: function () {
				/* check if paused */
				if (this._isPlaying) {
					return this;
				}
				if (!this._pauseTime) {
					return this;
				}

				/* continue from last checkpoint - time elapsed */
				var compensation = ig.game.tweens.now() - this._pauseTime;
				this._startTime += compensation;


				/* unset paused flag to continue updating */
				ig.game.tweens.add(this);
				this._isPlaying = true;

				/* callback function */
				if (this._onResumeCallback !== null) {
					this._onResumeCallback.call(this._object, this._object);
				}
				this._pauseTime = null;
				return this;
			},

			end: function () {
				this.update(this._startTime + this._duration);
				return this;
			},

			stopChainedTweens: function () {
				for (var i = 0, numChainedTweens = this._chainedTweens.length; i < numChainedTweens; i++) {
					this._chainedTweens[i].stop();
				}
			},

			delay: function (amount) {
				this._delayTime = amount;
				return this;
			},

			repeat: function (times) {
				this._repeat = times;
				return this;
			},

			repeatDelay: function (amount) {
				this._repeatDelayTime = amount;
				return this;
			},

			yoyo: function (yoyo) {
				this._yoyo = yoyo;
				return this;
			},

			easing: function (easing) {
				this._easingFunction = easing;
				return this;
			},

			interpolation: function (interpolation) {
				this._interpolationFunction = interpolation;
				return this;
			},

			chain: function () {
				this._chainedTweens = arguments;
				return this;
			},

			onStart: function (callback) {
				this._onStartCallback = callback;
				return this;
			},

			onUpdate: function (callback) {
				this._onUpdateCallback = callback;
				return this;
			},

			onComplete: function (callback) {
				this._onCompleteCallback = callback;
				return this;
			},

			onStop: function (callback) {
				this._onStopCallback = callback;
				return this;
			},

			onPause: function (callback) {
				this._onPauseCallback = callback;
				return this;
			},

			onResume: function (callback) {
				this._onResumeCallback = callback;
				return this;
			},

			propUpdate: function (prop, obj, start, end, value) {
				// Parses relative end values with start as base (e.g.: +10, -3)
				if (typeof (end) === 'string') {
					if (end.charAt(0) === '+' || end.charAt(0) === '-') {
						end = start + parseFloat(end);
					} else {
						end = parseFloat(end);
					}
				}

				if (typeof (obj[prop]) !== "object") {
					obj[prop] = start + (end - start) * value;
				} else {
					for (var subprop in end) {
						this.propUpdate(subprop, obj[prop], start[subprop], end[subprop], value);
					}
				}
			},

			update: function (time) {
				if (time < this._startTime) return;
				var property;
				var elapsed;
				var value;

				if (this._onStartCallbackFired === false) {
					if (this._onStartCallback !== null) {
						this._onStartCallback.call(this._object, this._object);
					}
					this._onStartCallbackFired = true;
				}

				elapsed = (time - this._startTime) / this._duration;
				if (elapsed > 1) elapsed = 1;

				this._currentElapsed = elapsed;

				value = this._easingFunction(elapsed);

				for (property in this._valuesEnd) {
					var start = this._valuesStart[property];
					var end = this._valuesEnd[property];

					if (end instanceof Array) {
						this._object[property] = this._interpolationFunction(end, value);
					} else {
						this.propUpdate(property, this._object, start, end, value);
					}
				}

				if (this._onUpdateCallback !== null) {
					this._onUpdateCallback.call(this._object, this._object, value);
				}

				if (elapsed === 1) {
					if (this._repeat > 0) {
						if (isFinite(this._repeat)) {
							this._repeat--;
						}

						// Reassign starting values, restart by making startTime = now
						for (property in this._valuesStartRepeat) {
							if (this._yoyo) {
								var tmp = this._valuesStartRepeat[property];
								this._valuesStartRepeat[property] = this._valuesEnd[property];
								this._valuesEnd[property] = tmp;
							}
							this._valuesStart[property] = this._valuesStartRepeat[property];
						}

						if (this._yoyo) {
							this._reversed = !this._reversed;
						}

						if (this._repeatDelayTime !== undefined) {
							this._startTime = time + this._repeatDelayTime;
						} else {
							this._startTime = time + this._delayTime;
						}
					} else {

						this._isPlaying = false;
						ig.game.tweens.remove(this);

						if (this._onCompleteCallback !== null) {
							this._onCompleteCallback.call(this._object, this._object);
						}

						for (var i = 0, numChainedTweens = this._chainedTweens.length; i < numChainedTweens; i++) {
							// Make the chained tweens start exactly at the time they should,
							// even if the `update()` method was called way past the duration of the tween
							this._chainedTweens[i].start(this._startTime + this._duration);
						}
					}
				}
			}
		});

		ig.Tween.Interpolation = {
			Linear: function (v, k) {

				var m = v.length - 1;
				var f = m * k;
				var i = Math.floor(f);
				var fn = ig.Tween.Interpolation.Utils.Linear;

				if (k < 0) {
					return fn(v[0], v[1], f);
				}

				if (k > 1) {
					return fn(v[m], v[m - 1], m - f);
				}

				return fn(v[i], v[i + 1 > m ? m : i + 1], f - i);

			},

			Bezier: function (v, k) {

				var b = 0;
				var n = v.length - 1;
				var pw = Math.pow;
				var bn = ig.Tween.Interpolation.Utils.Bernstein;

				for (var i = 0; i <= n; i++) {
					b += pw(1 - k, n - i) * pw(k, i) * v[i] * bn(n, i);
				}

				return b;

			},

			CatmullRom: function (v, k) {

				var m = v.length - 1;
				var f = m * k;
				var i = Math.floor(f);
				var fn = ig.Tween.Interpolation.Utils.CatmullRom;

				if (v[0] === v[m]) {

					if (k < 0) {
						i = Math.floor(f = m * (1 + k));
					}

					return fn(v[(i - 1 + m) % m], v[i], v[(i + 1) % m], v[(i + 2) % m], f - i);

				} else {

					if (k < 0) {
						return v[0] - (fn(v[0], v[0], v[1], v[1], -f) - v[0]);
					}

					if (k > 1) {
						return v[m] - (fn(v[m], v[m], v[m - 1], v[m - 1], f - m) - v[m]);
					}

					return fn(v[i ? i - 1 : 0], v[i], v[m < i + 1 ? m : i + 1], v[m < i + 2 ? m : i + 2], f - i);

				}

			},

			Utils: {

				Linear: function (p0, p1, t) {

					return (p1 - p0) * t + p0;

				},

				Bernstein: function (n, i) {

					var fc = ig.Tween.Interpolation.Utils.Factorial;

					return fc(n) / fc(i) / fc(n - i);

				},

				Factorial: (function () {

					var a = [1];

					return function (n) {

						var s = 1;

						if (a[n]) {
							return a[n];
						}

						for (var i = n; i > 1; i--) {
							s *= i;
						}

						a[n] = s;
						return s;

					};

				})(),

				CatmullRom: function (p0, p1, p2, p3, t) {

					var v0 = (p2 - p0) * 0.5;
					var v1 = (p3 - p1) * 0.5;
					var t2 = t * t;
					var t3 = t * t2;

					return (2 * p1 - 2 * p2 + v0 + v1) * t3 + (-3 * p1 + 3 * p2 - 2 * v0 - v1) * t2 + v0 * t + p1;

				}

			}
		};
	});