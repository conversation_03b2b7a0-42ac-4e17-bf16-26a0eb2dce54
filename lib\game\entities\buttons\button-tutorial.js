ig.module('game.entities.buttons.button-tutorial')
.requires(
    'plugins.utils.buttons.button-image',
    'game.entities.objects.popup-tutorial'
)
.defines(function () {
    "use strict";

    ig.EntityButtonTutorial = ig.global.EntityButtonTutorial = EntityButtonImage.extend({
        name: 'button-tutorial',
        idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/btn-tutorial.png'), frameCountX: 1, frameCountY: 1 },

        init: function (x, y, settings) {
            this.parent(x, y, settings);
        },

        onClickCallback: function () {
            // spawn settings popup
            this._parent.tweenHide();
            var tutorialPopup = ig.game.spawnEntity(EntityPopupTutorial, 0, 0, {
                zIndex: ig.game.LAYERS.POPUP,
                exitCb: function () {
                    // Mark tutorial as completed
                    ig.game.completeTutorial();

                    // Resume the game
                    this._parent.tweenShow();
                }.bind(this)
            });
        }
    });
});
