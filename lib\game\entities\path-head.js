ig.module('game.entities.path-head')
.requires(
    'impact.entity',
    'plugins.math.sat' // Assuming SAT is needed for the circle shape, though it's managed by truck
)
.defines(function () {
    EntityPathHead = ig.Entity.extend({
        // This entity is purely logical for collision checking during path drawing.
        // It does not need to be updated or drawn in the traditional sense.
        // It also does not participate in standard game physics/collisions.
        collides: ig.Entity.COLLIDES.NEVER,
        type: ig.Entity.TYPE.NONE,
        checkAgainst: ig.Entity.TYPE.NONE,

        _radius: 0,
        satShape: null, // Will be an ig.SAT.Circle

        init: function (x, y, settings) {
            this.parent(x, y, settings); // Sets this.pos

            if (settings && typeof settings.radius !== 'undefined') {
                this._radius = settings.radius;
            } else {
                console.warn("EntityPathHead initialized without a radius.");
                this._radius = 10; // Default fallback, should be overridden
            }

            // Initial SAT shape creation
            this.updateSATShape(this.pos);
        },

        updateSATShape: function (newPosition) {
            // Update internal position
            this.pos.x = newPosition.x;
            this.pos.y = newPosition.y;

            // Recreate the SAT circle shape at the new position
            // The SAT.Circle constructor expects a SAT.Vector2D for its center
            this.satShape = new ig.SAT.Circle(new ig.SAT.Vector2D(this.pos.x, this.pos.y), this._radius);
        },

        getRadius: function () {
            return this._radius;
        },

        getSATShape: function () {
            return this.satShape;
        },

        // No update or draw needed as it's a logical entity
        update: function () {
            // No standard update logic
        },

        draw: function () {
            // No drawing needed
        }
    });
});
